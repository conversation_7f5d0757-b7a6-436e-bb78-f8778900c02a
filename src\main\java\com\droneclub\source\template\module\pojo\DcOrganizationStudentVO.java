package com.droneclub.source.template.module.pojo;

import com.droneclub.source.template.entity.DcExamRecord;
import com.droneclub.source.template.entity.User;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class DcOrganizationStudentVO {

    private Integer id;

    private Integer companyId;

    private String companyName;

    private Integer coachId;

    private Integer studentId;

    private String coachName;

    private String studentName;

    private String studentCourse;

    private String studentStage;

    /**
     * 基础-总答题数
     */
    private Long totalAnswerCount;

    /**
     * 基础-正确答题数
     */
    private Long correctAnswerCount;

    /**
     * 综合-总答题数
     */
    private Long zhTotalAnswerCount;

    /**
     * 综合-正确答题数
     */
    private Long zhCorrectAnswerCount;

    /**
     * 教员-总答题数
     */
    private Long jyTotalAnswerCount;

    /**
     * 教员-正确答题数
     */
    private Long jyCorrectAnswerCount;

    /**
     * 正确答题率
     */
    private Double correctAnswerRate;

    /**
     * 最近十天答题记录
     */
    private List<DcExamRecord> examRecords;

    private List<DcExamRecord> zhExamRecords;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private User user;

    private DcExamRecord examRecord;
}