<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.droneclub.source.template.mapper.DcTopicMapper">

    <select id="getRandomTopics" resultType="com.droneclub.source.template.entity.DcTopic">
        SELECT *
        FROM dc_topic
        WHERE is_delete = 0
        AND topic_status = 1
        AND chapter_type = #{chapterType}
        <if test="excludeChapterName != null and excludeChapterName.size() > 0">
            AND chapter_name NOT IN
            <foreach item="name" collection="excludeChapterName" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
        ORDER BY RAND()
    </select>

    <select id="getDcYcTopicForAllRecords" resultType="com.droneclub.source.template.module.pojo.DcTopicVO">
        SELECT t.*,
               COUNT(*) AS 'errorTotalNumber'
        FROM dc_all_topic_record r
                 INNER JOIN
             dc_topic t ON r.topic_id = t.id
        WHERE r.answer_right = 0
          AND t.topic_status = 1
        GROUP BY t.id, t.topic_name
        ORDER BY COUNT(*) DESC LIMIT 100;
    </select>

    <select id="getDcYcTopicFor7DayRecords" resultType="com.droneclub.source.template.module.pojo.DcTopicVO">
        SELECT t.*,
               COUNT(*) AS 'errorTotalNumber'
        FROM dc_all_topic_record r
                 INNER JOIN
             dc_topic t ON r.topic_id = t.id
        WHERE r.answer_right = 0
          AND r.create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
          AND t.topic_status = 1
        GROUP BY t.id, t.topic_name
        ORDER BY COUNT(*) DESC LIMIT 100;
    </select>

</mapper>