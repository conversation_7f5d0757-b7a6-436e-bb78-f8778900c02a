package com.droneclub.source.template.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

@Slf4j
public class ResourceUtils {

    /**
     * 加载资源
     *
     * @param path 资源路径
     * @return 资源文本
     */
    public static String loadResource(String path) {
        ClassLoader classLoader = ResourceUtils.class.getClassLoader();
        try (InputStream inputStream = classLoader.getResourceAsStream(path)) {
            if (inputStream == null) {
                log.error("资源不存在: {}", path);
                throw new IOException("Resource not found: " + path);
            }

            StringBuilder stringBuilder = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    stringBuilder.append(line);
                }
            }

            return stringBuilder.toString();
        } catch (IOException e) {
            log.error("加载: {} 失败", path, e);
        }
        return null;
    }

}
