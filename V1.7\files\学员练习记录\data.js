﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,k)),m,[],n,_(h,o),p,[q],r,_(s,t,u,v,g,w,x,_(),y,[],z,_(A,B,C,D,E,_(F,G,H,I),J,null,K,L,L,M,N,O,null,P,Q,R,S,T,U,V,Q,W,X,_(F,G,H,Y),Z,Q,ba,bb,_(bc,bd,be,bf,bg,bf,bh,bf,bi,k,H,_(bj,bk,bl,bk,bm,bk,bn,bo)),i,_(j,k,l,k)),bp,_(),bq,_(),br,_(bs,[_(bt,bu,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,bC,l,bD),A,bE,bF,_(bG,bH,bI,bJ),E,_(F,G,H,bK)),bp,_(),bL,_(),bM,bd),_(bt,bN,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,bC,l,bO),A,bE,bF,_(bG,bH,bI,bJ)),bp,_(),bL,_(),bM,bd),_(bt,bP,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,bQ,l,bR),A,bS,bF,_(bG,bT,bI,bU)),bp,_(),bL,_(),bM,bd),_(bt,bV,bv,h,bw,bW,u,bX,bz,bX,bA,bB,z,_(A,bY,i,_(j,bZ,l,bZ),bF,_(bG,ca,bI,cb),J,null),bp,_(),bL,_(),cc,_(cd,ce)),_(bt,cf,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,bC,l,cg),A,ch,bF,_(bG,bH,bI,ci)),bp,_(),bL,_(),bM,bd),_(bt,cj,bv,h,bw,bW,u,bX,bz,bX,bA,bB,z,_(i,_(j,ck,l,ck),A,bY,J,null,bF,_(bG,cl,bI,cm),Z,cn),bp,_(),bL,_(),cc,_(cd,co)),_(bt,cp,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cq,l,bR),A,bS,bF,_(bG,cr,bI,cs)),bp,_(),bL,_(),bM,bd),_(bt,ct,bv,h,bw,cu,u,by,bz,by,bA,bB,z,_(i,_(j,cv,l,bR),A,ch,bF,_(bG,cw,bI,cx),Z,cy,cz,cA),bp,_(),bL,_(),cc,_(cd,cB),bM,bd),_(bt,cC,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cD,l,cE),A,ch,bF,_(bG,cF,bI,cG),Z,cH),bp,_(),bL,_(),bM,bd),_(bt,cI,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cD,l,cJ),A,ch,bF,_(bG,cF,bI,cK),Z,cH),bp,_(),bL,_(),bM,bd),_(bt,cL,bv,h,bw,cM,u,by,bz,cN,bA,bB,z,_(i,_(j,cO,l,bR),A,cP,bF,_(bG,cQ,bI,cR),V,cS),bp,_(),bL,_(),cc,_(cd,cT),bM,bd),_(bt,cU,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cV,l,bR),A,bS,bF,_(bG,cW,bI,cR)),bp,_(),bL,_(),bM,bd),_(bt,cX,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(cY,cZ,i,_(j,cq,l,bR),A,da,bF,_(bG,cW,bI,db),cz,dc),bp,_(),bL,_(),bM,bd),_(bt,dd,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(cY,cZ,i,_(j,cq,l,bR),A,da,bF,_(bG,cr,bI,db),cz,dc),bp,_(),bL,_(),bM,bd),_(bt,de,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(cY,cZ,i,_(j,cq,l,bR),A,da,bF,_(bG,df,bI,db),cz,dc),bp,_(),bL,_(),bM,bd),_(bt,dg,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,dh,l,di),A,bS,bF,_(bG,dj,bI,dk),cz,dl),bp,_(),bL,_(),bM,bd),_(bt,dm,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,dh,l,di),A,bS,bF,_(bG,dn,bI,dk),cz,dl),bp,_(),bL,_(),bM,bd),_(bt,dp,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,dq,l,di),A,bS,bF,_(bG,cJ,bI,dk),cz,dl),bp,_(),bL,_(),bM,bd),_(bt,dr,bv,h,bw,cM,u,by,bz,cN,bA,bB,z,_(i,_(j,cO,l,bR),A,cP,bF,_(bG,cv,bI,ds),V,cS),bp,_(),bL,_(),cc,_(cd,cT),bM,bd),_(bt,dt,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cV,l,bR),A,bS,bF,_(bG,du,bI,ds)),bp,_(),bL,_(),bM,bd),_(bt,dv,bv,h,bw,dw,u,by,bz,by,bA,bB,z,_(i,_(j,dx,l,dy),A,dz,bF,_(bG,du,bI,dA)),bp,_(),bL,_(),cc,_(cd,dB),bM,bd),_(bt,dC,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(cY,cZ,i,_(j,dD,l,di),A,da,bF,_(bG,dE,bI,dF)),bp,_(),bL,_(),bM,bd),_(bt,dG,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cb,l,bR),A,bS,bF,_(bG,dH,bI,dI)),bp,_(),bL,_(),bM,bd),_(bt,dJ,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,dK,l,dL),A,dM,bF,_(bG,dN,bI,dO),Z,dP),bp,_(),bL,_(),bM,bd),_(bt,dQ,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,bC,l,bD),A,bE,bF,_(bG,dR,bI,bJ),E,_(F,G,H,bK)),bp,_(),bL,_(),bM,bd),_(bt,dS,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,bC,l,bO),A,bE,bF,_(bG,dR,bI,bJ)),bp,_(),bL,_(),bM,bd),_(bt,dT,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,dU,l,bR),A,bS,bF,_(bG,dV,bI,bU)),bp,_(),bL,_(),bM,bd),_(bt,dW,bv,h,bw,bW,u,bX,bz,bX,bA,bB,z,_(A,bY,i,_(j,bZ,l,bZ),bF,_(bG,dX,bI,cb),J,null),bp,_(),bL,_(),cc,_(cd,ce)),_(bt,dY,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,bC,l,cg),A,ch,bF,_(bG,dR,bI,ci)),bp,_(),bL,_(),bM,bd),_(bt,dZ,bv,h,bw,bW,u,bX,bz,bX,bA,bB,z,_(i,_(j,ck,l,ck),A,bY,J,null,bF,_(bG,ea,bI,cm),Z,cn),bp,_(),bL,_(),cc,_(cd,co)),_(bt,eb,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cq,l,bR),A,bS,bF,_(bG,ec,bI,cs)),bp,_(),bL,_(),bM,bd),_(bt,ed,bv,h,bw,cu,u,by,bz,by,bA,bB,z,_(i,_(j,cv,l,bR),A,ch,bF,_(bG,ee,bI,cx),Z,cy,cz,cA),bp,_(),bL,_(),cc,_(cd,cB),bM,bd),_(bt,ef,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cD,l,eg),A,ch,bF,_(bG,eh,bI,cG),Z,cH),bp,_(),bL,_(),bM,bd),_(bt,ei,bv,h,bw,cM,u,by,bz,cN,bA,bB,z,_(i,_(j,cO,l,bR),A,cP,bF,_(bG,ej,bI,cR),V,cS),bp,_(),bL,_(),cc,_(cd,cT),bM,bd),_(bt,ek,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cV,l,bR),A,bS,bF,_(bG,el,bI,cR)),bp,_(),bL,_(),bM,bd),_(bt,em,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(cY,cZ,i,_(j,cq,l,bR),A,da,bF,_(bG,el,bI,db),cz,dc),bp,_(),bL,_(),bM,bd),_(bt,en,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(cY,cZ,i,_(j,cq,l,bR),A,da,bF,_(bG,ec,bI,db),cz,dc),bp,_(),bL,_(),bM,bd),_(bt,eo,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(cY,cZ,i,_(j,cq,l,bR),A,da,bF,_(bG,ep,bI,db),cz,dc),bp,_(),bL,_(),bM,bd),_(bt,eq,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,dh,l,di),A,bS,bF,_(bG,er,bI,dk),cz,dl),bp,_(),bL,_(),bM,bd),_(bt,es,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,dh,l,di),A,bS,bF,_(bG,et,bI,dk),cz,dl),bp,_(),bL,_(),bM,bd),_(bt,eu,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,dq,l,di),A,bS,bF,_(bG,ev,bI,dk),cz,dl),bp,_(),bL,_(),bM,bd),_(bt,ew,bv,h,bw,ex,u,by,bz,ey,bA,bB,z,_(i,_(j,ez,l,eA),A,cP,bF,_(bG,ej,bI,dO),eB,eC),bp,_(),bL,_(),cc,_(cd,eD),bM,bd),_(bt,eE,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,eF,l,bR),A,bS,bF,_(bG,eG,bI,eH)),bp,_(),bL,_(),bM,bd),_(bt,eI,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(cY,cZ,A,eJ,i,_(j,eK,l,bR),cz,dc,bF,_(bG,eG,bI,eL)),bp,_(),bL,_(),bM,bd),_(bt,eM,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(cY,cZ,A,eJ,i,_(j,cm,l,bR),cz,dc,bF,_(bG,eN,bI,eL)),bp,_(),bL,_(),bM,bd),_(bt,eO,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,eF,l,bR),A,bS,bF,_(bG,eG,bI,eP)),bp,_(),bL,_(),bM,bd),_(bt,eQ,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(cY,cZ,A,eJ,i,_(j,eK,l,bR),cz,dc,bF,_(bG,eG,bI,eG)),bp,_(),bL,_(),bM,bd),_(bt,eR,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(cY,cZ,A,eJ,i,_(j,cm,l,bR),cz,dc,bF,_(bG,eN,bI,eG)),bp,_(),bL,_(),bM,bd),_(bt,eS,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,eT,l,eU),A,eV,bF,_(bG,cF,bI,eW),cz,eX),bp,_(),bL,_(),bM,bd)])),eY,_(),eZ,_(fa,_(fb,fc),fd,_(fb,fe),ff,_(fb,fg),fh,_(fb,fi),fj,_(fb,fk),fl,_(fb,fm),fn,_(fb,fo),fp,_(fb,fq),fr,_(fb,fs),ft,_(fb,fu),fv,_(fb,fw),fx,_(fb,fy),fz,_(fb,fA),fB,_(fb,fC),fD,_(fb,fE),fF,_(fb,fG),fH,_(fb,fI),fJ,_(fb,fK),fL,_(fb,fM),fN,_(fb,fO),fP,_(fb,fQ),fR,_(fb,fS),fT,_(fb,fU),fV,_(fb,fW),fX,_(fb,fY),fZ,_(fb,ga),gb,_(fb,gc),gd,_(fb,ge),gf,_(fb,gg),gh,_(fb,gi),gj,_(fb,gk),gl,_(fb,gm),gn,_(fb,go),gp,_(fb,gq),gr,_(fb,gs),gt,_(fb,gu),gv,_(fb,gw),gx,_(fb,gy),gz,_(fb,gA),gB,_(fb,gC),gD,_(fb,gE),gF,_(fb,gG),gH,_(fb,gI),gJ,_(fb,gK),gL,_(fb,gM),gN,_(fb,gO),gP,_(fb,gQ),gR,_(fb,gS),gT,_(fb,gU)));}; 
var b="url",c="学员练习记录.html",d="generationDate",e=new Date(1750408319529.74),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=0,l="height",m="adaptiveViews",n="sketchKeys",o="s0",p="variables",q="OnLoadVariable",r="page",s="packageId",t="da53ee609f63413d975db7d686a4c2a8",u="type",v="Axure:Page",w="学员练习记录",x="notes",y="annotations",z="style",A="baseStyle",B="627587b6038d43cca051c114ac41ad32",C="pageAlignment",D="center",E="fill",F="fillType",G="solid",H="color",I=0xFFFFFFFF,J="image",K="imageAlignment",L="near",M="imageRepeat",N="auto",O="favicon",P="sketchFactor",Q="0",R="colorStyle",S="appliedColor",T="fontName",U="Applied Font",V="borderWidth",W="borderVisibility",X="borderFill",Y=0xFF797979,Z="cornerRadius",ba="cornerVisibility",bb="outerShadow",bc="on",bd=false,be="offsetX",bf=5,bg="offsetY",bh="blurRadius",bi="spread",bj="r",bk=0,bl="g",bm="b",bn="a",bo=0.349019607843137,bp="adaptiveStyles",bq="interactionMap",br="diagram",bs="objects",bt="id",bu="c2f664b1c4ff45ffb4de4821deff7271",bv="label",bw="friendlyType",bx="矩形",by="vectorShape",bz="styleType",bA="visible",bB=true,bC=375,bD=758,bE="60d87ff5e0934fb5a735f21d2a268c7d",bF="location",bG="x",bH=75,bI="y",bJ=31,bK=0xFFF5F5F5,bL="imageOverrides",bM="generateCompound",bN="0fb7b6f5635e4563b6ac99c329d2f8f5",bO=45,bP="d00c4009dbf044929a114fde9ae807f0",bQ=84,bR=16,bS="f8c70a63ec8c4ded9e1c2963c0e658a5",bT=232,bU=46,bV="ffc86de355ec469885291e84abafbf5f",bW="图片 ",bX="imageBox",bY="********************************",bZ=20,ca=87,cb=44,cc="images",cd="normal~",ce="images/作业布置/u38.png",cf="b691a1402c04412885653c0349933c8c",cg=178,ch="93950f64c5104d7fbe432f744db64e34",ci=77,cj="cb0d198240bf498a9a1f8fc6303bb70e",ck=80,cl=220,cm=94,cn="40",co="images/学员练习记录/u315.svg",cp="3035a40df4f64b7cbc42a36349b7cfd3",cq=70,cr=225,cs=186,ct="2245f1ec0d4140d586ca4bf5abd8ca44",cu="形状",cv=99,cw=211,cx=209,cy="3",cz="fontSize",cA="10px",cB="images/学员练习记录/u317.svg",cC="ca88fc0775774ae982956cbf216ebb4d",cD=361,cE=157,cF=82,cG=260,cH="10",cI="15c8a6f4f3044ad8a48c9aaae3fa0070",cJ=356,cK=426,cL="0a34e4ac60724c4e9c5734641046060c",cM="垂直线",cN="verticalLine",cO=4,cP="1df8bc12869c446989a07f36813b37ee",cQ=97,cR=272,cS="4",cT="images/创建作业/u147.svg",cU="88bcb7f7c83a4a4a9dc8b83e792f40e1",cV=56,cW=108,cX="8c5621867b5e40fa96f8928e056f86a7",cY="fontWeight",cZ="700",da="8c7a4c5ad69a4369a5f7788171ac0b32",db=333,dc="14px",dd="a1f0bb20f5134bb78925a34d35ef54fa",de="b4cd85cb03644d25819f880e4938d051",df=344,dg="fe813ff6119845c5a758502db4d00474",dh=41,di=21,dj=123,dk=305,dl="18px",dm="19850e0ed6324dae83e797d8de57e6fc",dn=240,dp="27d38d8734f54f689c7171baa48a8db3",dq=47,dr="ca6edc6b09d34170999d05a884523ee4",ds=442,dt="80044306b80946fb97852a8af67a8c9e",du=110,dv="0dc175a817644b5192235dd42261832e",dw="占位符",dx=300,dy=170,dz="d47d8120d97741469da4152217605972",dA=503,dB="images/学员练习记录/u330.svg",dC="be0954999fd547b8951bc279bd2163e9",dD=54,dE=233,dF=578,dG="21fa79c1a8b84fdd98992b3759f7e97c",dH=242,dI=626,dJ="0a2f7e3354654a8091eac3c2e329cad1",dK=135,dL=32,dM="c26e509c01924380b00a973a82019677",dN=193,dO=370,dP="65",dQ="0eadd50cab174915b8aef17df5bde62b",dR=487,dS="ed8fda37581e4d8880a73d25d3553bd1",dT="53d71bf4061845d89a04543d246260f5",dU=112,dV=610,dW="cf72de1116854a7fa18dd5b90780058b",dX=499,dY="354d6641d51a41568aa83f28f0dfb378",dZ="15fdb70b4faf4f72a4fe474a6aa1b71e",ea=632,eb="6864b4dcdf32424ea025f5d48e41a762",ec=637,ed="7693d81be7ee4453aceb38ddcbdb6683",ee=623,ef="956fa135b39b436898c3f09d613c5ced",eg=522,eh=494,ei="529dcaeecd42409bbc111c1227a39578",ej=509,ek="d56322a2df2347f5984d3ba67328166a",el=520,em="825e8325e59d4f2c80ae51cd710b405d",en="2ad367c38dc74d309a2e4482b3a6c60c",eo="1f20fc2ea0be4493a4087ab036e06516",ep=756,eq="02d339490fc549a5bfeb099117bc6347",er=535,es="24dbd4c0d28d475585443b53154acd1b",et=652,eu="1c1790e74a3244deaac790dfcae8227b",ev=768,ew="5c607801b9ed424bbb506cf224dbf263",ex="线段",ey="horizontalLine",ez=326,eA=1,eB="linePattern",eC="dashed",eD="images/学员练习记录/u351.svg",eE="bb00a6ff74264416b9d6aaa1aa08cb29",eF=116,eG=505,eH=400,eI="c9cf3f73e1064ca299fab09bf9ca24f7",eJ="4988d43d80b44008a4a415096f1632af",eK=92,eL=434,eM="f5a1208901014c42ae9784ebabf5a689",eN=672,eO="668f53fc4ffd4fe9817cd49fbea23eb1",eP=471,eQ="3ce91785eb704a208cace9ac02945766",eR="c59991655ca145b4805ab10ce02a6b0c",eS="7c731fd16800431f8ef0118aa0b76258",eT=533,eU=231,eV="31e8887730cc439f871dc77ac74c53b6",eW=801,eX="16px",eY="masters",eZ="objectPaths",fa="c2f664b1c4ff45ffb4de4821deff7271",fb="scriptId",fc="u310",fd="0fb7b6f5635e4563b6ac99c329d2f8f5",fe="u311",ff="d00c4009dbf044929a114fde9ae807f0",fg="u312",fh="ffc86de355ec469885291e84abafbf5f",fi="u313",fj="b691a1402c04412885653c0349933c8c",fk="u314",fl="cb0d198240bf498a9a1f8fc6303bb70e",fm="u315",fn="3035a40df4f64b7cbc42a36349b7cfd3",fo="u316",fp="2245f1ec0d4140d586ca4bf5abd8ca44",fq="u317",fr="ca88fc0775774ae982956cbf216ebb4d",fs="u318",ft="15c8a6f4f3044ad8a48c9aaae3fa0070",fu="u319",fv="0a34e4ac60724c4e9c5734641046060c",fw="u320",fx="88bcb7f7c83a4a4a9dc8b83e792f40e1",fy="u321",fz="8c5621867b5e40fa96f8928e056f86a7",fA="u322",fB="a1f0bb20f5134bb78925a34d35ef54fa",fC="u323",fD="b4cd85cb03644d25819f880e4938d051",fE="u324",fF="fe813ff6119845c5a758502db4d00474",fG="u325",fH="19850e0ed6324dae83e797d8de57e6fc",fI="u326",fJ="27d38d8734f54f689c7171baa48a8db3",fK="u327",fL="ca6edc6b09d34170999d05a884523ee4",fM="u328",fN="80044306b80946fb97852a8af67a8c9e",fO="u329",fP="0dc175a817644b5192235dd42261832e",fQ="u330",fR="be0954999fd547b8951bc279bd2163e9",fS="u331",fT="21fa79c1a8b84fdd98992b3759f7e97c",fU="u332",fV="0a2f7e3354654a8091eac3c2e329cad1",fW="u333",fX="0eadd50cab174915b8aef17df5bde62b",fY="u334",fZ="ed8fda37581e4d8880a73d25d3553bd1",ga="u335",gb="53d71bf4061845d89a04543d246260f5",gc="u336",gd="cf72de1116854a7fa18dd5b90780058b",ge="u337",gf="354d6641d51a41568aa83f28f0dfb378",gg="u338",gh="15fdb70b4faf4f72a4fe474a6aa1b71e",gi="u339",gj="6864b4dcdf32424ea025f5d48e41a762",gk="u340",gl="7693d81be7ee4453aceb38ddcbdb6683",gm="u341",gn="956fa135b39b436898c3f09d613c5ced",go="u342",gp="529dcaeecd42409bbc111c1227a39578",gq="u343",gr="d56322a2df2347f5984d3ba67328166a",gs="u344",gt="825e8325e59d4f2c80ae51cd710b405d",gu="u345",gv="2ad367c38dc74d309a2e4482b3a6c60c",gw="u346",gx="1f20fc2ea0be4493a4087ab036e06516",gy="u347",gz="02d339490fc549a5bfeb099117bc6347",gA="u348",gB="24dbd4c0d28d475585443b53154acd1b",gC="u349",gD="1c1790e74a3244deaac790dfcae8227b",gE="u350",gF="5c607801b9ed424bbb506cf224dbf263",gG="u351",gH="bb00a6ff74264416b9d6aaa1aa08cb29",gI="u352",gJ="c9cf3f73e1064ca299fab09bf9ca24f7",gK="u353",gL="f5a1208901014c42ae9784ebabf5a689",gM="u354",gN="668f53fc4ffd4fe9817cd49fbea23eb1",gO="u355",gP="3ce91785eb704a208cace9ac02945766",gQ="u356",gR="c59991655ca145b4805ab10ce02a6b0c",gS="u357",gT="7c731fd16800431f8ef0118aa0b76258",gU="u358";
return _creator();
})());