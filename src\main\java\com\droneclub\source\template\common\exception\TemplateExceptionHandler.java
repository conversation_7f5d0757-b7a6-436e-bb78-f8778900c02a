package com.droneclub.source.template.common.exception;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.model.RestResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@Slf4j
@Order(9998)
@RestControllerAdvice
public class TemplateExceptionHandler {

    @ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
    public RestResult<?> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        log.error("", e);
        return RestResult.failure(ResultCode.FAIL.getCode(), "不支持" + e.getMethod() + "请求.");
    }

    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public RestResult<?> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        FieldError fieldError = e.getBindingResult().getFieldError();
        if (fieldError != null) {
            return RestResult.failure("401", fieldError.getDefaultMessage());
        }
        return RestResult.failure("401", "参数不合法");
    }


}
