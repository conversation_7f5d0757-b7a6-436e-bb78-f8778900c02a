package com.droneclub.source.template.system.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import com.alibaba.fastjson.JSONArray;
import com.droneclub.source.template.common.config.SourceBuilderConfig;
import com.droneclub.source.template.common.utils.CamelCaseConverterUtils;
import com.droneclub.source.template.common.utils.CapitalizeConverterUtils;
import com.droneclub.source.template.common.utils.ResourceUtils;
import com.droneclub.source.template.mapper.MysqlMetaInfoMapper;
import com.droneclub.source.template.system.pojo.ColumnMetadata;
import com.droneclub.source.template.system.pojo.SourceBuildConfigItem;
import com.droneclub.source.template.system.service.ISourceBuilderService;
import com.droneclub.source.template.system.service.ITemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Service
@RequiredArgsConstructor
public class SourceBuilderServiceImpl implements ISourceBuilderService {

    private final ITemplateService templateService;
    private final SourceBuilderConfig sourceBuilderConfig;
    private final MysqlMetaInfoMapper mysqlMetaInfoMapper;

    @Override
    public Object sourceBuilder(boolean replace, String tableName, String mode) {

        String str = ResourceUtils.loadResource(String.format("sourceBuilder/%s-buildConfig.json", mode));
        List<SourceBuildConfigItem> buildConfigItems = JSONArray.parseArray(str, SourceBuildConfigItem.class);
        if (buildConfigItems == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "配置异常.");
        }

        // 读取数据表元数据
        List<ColumnMetadata> columnMetadata = mysqlMetaInfoMapper.getColumnMetadata(tableName);
        String moduleName = CamelCaseConverterUtils.toCamelCase(tableName);
        String ModuleName = CapitalizeConverterUtils.capitalize(moduleName);
        // 组装code构建参数
        Map<String, Object> fillData = new HashMap<>();
        fillData.put("module", moduleName);
        fillData.put("Module", ModuleName);
        fillData.put("tableName", tableName);
        fillData.put("columns", columnMetadata);

        for (SourceBuildConfigItem item : buildConfigItems) {
            String filePath = sourceBuilderConfig.getSrcDir() + String.format(item.getTargetPath(), ModuleName);
            File file = new File(filePath);
            if (file.exists() && !replace) {
                throw new ZkException(ResultCode.FAIL.getCode(), filePath + "已存在, 请进一步核实.");
            }
            String sourceText = templateService.getStringTemplate(fillData, item.getTemplate());
            file = FileUtil.touch(filePath);
            try (FileWriter writer = new FileWriter(file)) {
                writer.write(sourceText);
                log.info("Java source 创建成功: {}", filePath);
            } catch (IOException e) {
                log.error("Java source 创建或写入失败: {}", filePath, e);
            }
        }
        return true;
    }
}
