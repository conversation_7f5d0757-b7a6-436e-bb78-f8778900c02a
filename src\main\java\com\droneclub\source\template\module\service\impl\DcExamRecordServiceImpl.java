package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import cn.soulspark.source.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.common.model.TemplateCurrentUser;
import com.droneclub.source.template.common.utils.DateUtils;
import com.droneclub.source.template.common.utils.TemplateSessionUtils;
import com.droneclub.source.template.entity.*;
import com.droneclub.source.template.mapper.DcExamRecordMapper;
import com.droneclub.source.template.mapper.DcTopicMapper;
import com.droneclub.source.template.module.pojo.DcExamRecordListSearch;
import com.droneclub.source.template.module.pojo.DcExamRecordVO;
import com.droneclub.source.template.module.service.IDcExamRecordService;
import com.droneclub.source.template.module.service.IDcExamTopicRecordService;
import com.droneclub.source.template.module.service.IDcExamUserRankService;
import com.droneclub.source.template.system.pojo.UserVO;
import com.droneclub.source.template.system.service.IUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcExamRecordServiceImpl implements IDcExamRecordService {

    private final DcExamRecordMapper dcExamRecordMapper;
    private final DcTopicMapper dcTopicMapper;
    private final IDcExamTopicRecordService examTopicRecordService;
    private final IDcExamUserRankService examUserRankService;
    private final IUserService userService;

    @Override
    public ListData<DcExamRecordVO> getDcExamRecordList(DcExamRecordListSearch params) {
        LambdaQueryWrapper<DcExamRecord> queryWrapper = new LambdaQueryWrapper<>();
        if (TemplateSessionUtils.getCurrentUser() != null) {
            queryWrapper.eq(DcExamRecord::getUserId, TemplateSessionUtils.getCurrentUser().getId());
            queryWrapper.isNotNull(DcExamRecord::getExamResult);
        } else {
            return new ListData<>(new ArrayList<>(), 0L, params.getPageNo(), params.getPageSize());
        }
        if (StringUtils.isValid(params.getExamTopicType()) && "base".equals(params.getExamTopicType())) {
            queryWrapper.and(wrapper ->
                    wrapper.eq(DcExamRecord::getExamTopicType, "base")
                            .or()
                            .isNull(DcExamRecord::getExamTopicType)
            );
        }

        if (StringUtils.isValid(params.getExamTopicType()) && "zh".equals(params.getExamTopicType())) {
            queryWrapper.and(wrapper ->
                    wrapper.eq(DcExamRecord::getExamTopicType, "zh")
            );
        }

        // 查询总数
        Long total = dcExamRecordMapper.selectCount(queryWrapper);
        // 指定排序倒序排序
        queryWrapper.orderByDesc(DcExamRecord::getId);

        // 分页查询
        Page<DcExamRecord> page = new Page<>(params.getPageNo(), params.getPageSize());
        IPage<DcExamRecord> dcExamRecordPage = dcExamRecordMapper.selectPage(page, queryWrapper);
        List<DcExamRecord> list = dcExamRecordPage.getRecords();
        List<DcExamRecordVO> listVO = list.stream()
                .map(dcExamRecord -> JSONObject.parseObject(JSONObject.toJSONString(dcExamRecord), DcExamRecordVO.class))
                .collect(Collectors.toList());
        return new ListData<>(listVO, total, params.getPageNo(), params.getPageSize());
    }

    @Override
    public DcExamRecordVO getDcExamRecordById(Integer id) {
        DcExamRecord dcExamRecord = dcExamRecordMapper.selectById(id);
        if (dcExamRecord == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        DcExamRecordVO examRecordVO = JSONObject.parseObject(JSONObject.toJSONString(dcExamRecord), DcExamRecordVO.class);
        UserVO userVO = userService.getUserById(String.valueOf(examRecordVO.getUserId()));
        examRecordVO.setUserName(userVO.getUserName());
        examRecordVO.setUserAvatar(userVO.getAvatar());
        examRecordVO.setExamDuration(DateUtils.convertTime(examRecordVO.getExamDuration()));
        return examRecordVO;
    }


    @Override
    public DcExamRecord createDcExamRecord(DcExamRecord data) {
        TemplateCurrentUser currentUser = TemplateSessionUtils.getCurrentUser();
        if (currentUser == null) {
            data.setUserId(-1);
        } else {
            data.setUserId(currentUser.getId());
        }
        boolean rs = dcExamRecordMapper.insert(data) > 0;
        log.info("创建 DcExamRecord: {}", rs ? "成功" : "失败");
        // 初始化考试数据
        List<DcTopic> examTopic = get100RandomTopics(data.getChapterType(), data.getExamType(), data.getExamTopicType());
        if (examTopic != null && examTopic.size() > 0) {
            Collections.shuffle(examTopic);
            examTopicRecordService.batchInsert(data.getId(), examTopic);
        } else {
            log.error("题目数据抽取有误, 请检查: {}", examTopic.size());
        }
        return data;
    }

    /**
     * 获取随机100道题目
     */
    public List<DcTopic> get100RandomTopics(String chapterType, String examType, String examTopicType) {
        // 排除章节标题
        List<String> excludeChapterName = new ArrayList<>();
        // 抽取题目
        List<DcTopic> selectedTopics = new ArrayList<>();
        // 如果是综合问答则只从综合问答抽十题返回
        if ("zh".equals(examTopicType)) {
            // 综合问答抽10题
            List<DcTopic> otherTopics = dcTopicMapper.getRandomTopicsByChapterName(chapterType, "第九章 综合问答");
            selectedTopics.addAll(otherTopics.stream().limit(10).collect(Collectors.toList()));
            return selectedTopics;
        }
        if (!"教员".equals(examType)) {
            // 排除综合问答和教员法，单独出题
            excludeChapterName.add("第九章 综合问答");
            excludeChapterName.add("第十章 教员法（教员适用）");
            // 排除口试章节
            excludeChapterName.add("口试-飞行过程");
            excludeChapterName.add("口试-飞行原理");
            excludeChapterName.add("口试-飞行理论");
            excludeChapterName.add("口试-公式、测算原理");
            excludeChapterName.add("口试-电池");
            excludeChapterName.add("口试-电调");
            excludeChapterName.add("口试-遥控器");
            excludeChapterName.add("口试-飞控");
            excludeChapterName.add("口试-地面站");
            excludeChapterName.add("口试-电机");
            excludeChapterName.add("口试-螺旋桨");
            excludeChapterName.add("口试-动力系统");
            excludeChapterName.add("口试-链路、连接、安装");
            excludeChapterName.add("口试-内部件");
            excludeChapterName.add("口试-教学");
            excludeChapterName.add("口试-气象");
            // 1. 获取所有符合条件的题目
            List<DcTopic> topics = dcTopicMapper.getRandomTopics(chapterType, excludeChapterName);

            // 2. 按章节名称分组
            Map<String, List<DcTopic>> groupedTopics = topics.stream()
                    .collect(Collectors.groupingBy(DcTopic::getChapterName));

            // 3. 每组抽取最多 10 条
            for (Map.Entry<String, List<DcTopic>> entry : groupedTopics.entrySet()) {
                List<DcTopic> chapterTopics = entry.getValue();
                selectedTopics.addAll(chapterTopics.stream().limit(10).collect(Collectors.toList()));
            }

            // 4. 如果不足 100 条，从全量题目中补足
            if (selectedTopics.size() < 100) {
                int remainingCount = 100 - selectedTopics.size();
                List<DcTopic> remainingTopics = topics.stream()
                        .filter(topic -> !selectedTopics.contains(topic)) // 排除已选题目
                        .collect(Collectors.toList());

                Collections.shuffle(remainingTopics); // 随机打乱
                selectedTopics.addAll(remainingTopics.stream().limit(remainingCount).collect(Collectors.toList()));
            }
        } else {
            // 教员只从教员章节抽取40题
            List<DcTopic> otherTopics = dcTopicMapper.getRandomTopicsByChapterName(chapterType, "第十章 教员法（教员适用）");
            selectedTopics.addAll(otherTopics.stream().limit(40).collect(Collectors.toList()));
        }
        return selectedTopics;
    }

    @Override
    public DcExamRecord updateDcExamRecord(DcExamRecord data) {
        // 根据考试Id获取数据, 计算分数
        List<DcExamTopicRecord> records = examTopicRecordService.getExamTopicRecord(data.getId());
        long rightNum = records.stream()
                .filter(DcExamTopicRecord::isAnswerResult)
                .count();
        long errorNum = records.stream()
                .filter(topic -> !topic.isAnswerResult())
                .count();
        // 记录考试正确数、错误数、考试分数
        data.setCorrectAnswerRecordNum(rightNum);
        data.setErrorAnswerRecordNum(errorNum);
        data.setExamScore(String.valueOf(rightNum * 1));
        if ("zh".equals(data.getExamTopicType())) {
            data.setExamResult(rightNum >= 7 ? 1 : 2);
        } else if ("教员".equals(data.getExamType())) {
            // 一题*2.5 >= 80分通过
            BigDecimal threshold = new BigDecimal(rightNum);
            BigDecimal multiplier = new BigDecimal("2.5");
            BigDecimal product = multiplier.multiply(threshold);
            data.setExamResult(product.compareTo(new BigDecimal("80")) >= 0 ? 1 : 2);
            data.setExamScore(product.stripTrailingZeros().toPlainString());
        } else if ("视距内".equals(data.getExamType())) {
            // 一题*1 >= 70分
            data.setExamResult(rightNum >= 70 ? 1 : 2);
        } else if ("超视距".equals(data.getExamType())) {
            // 一题*1 >= 80分
            data.setExamResult(rightNum >= 80 ? 1 : 2);
        }
        boolean rs = dcExamRecordMapper.updateById(data) > 0;
        log.info("更新 DcExamRecord: {}", rs ? "成功" : "失败");
        TemplateCurrentUser currentUser = TemplateSessionUtils.getCurrentUser();
        // 插入考试排行榜记录
        if (currentUser != null) {
            examUserRankService.createDcExamUserRank(DcExamUserRank.builder()
                    .chapterType(data.getChapterType())
                    .examType(data.getExamType())
                    .examTopicType(data.getExamTopicType())
                    .userId(currentUser.getId())
                    .userName(currentUser.getName())
                    .score(data.getExamScore())
                    .examTime(data.getExamDuration())
                    .build());
        }
        return data;
    }

    @Override
    public boolean deleteDcExamRecordById(Integer id) {
        boolean rs = dcExamRecordMapper.deleteById(id) > 0;
        log.info("删除 DcExamRecord: {}", rs ? "成功" : "失败");
        return rs;
    }
}
