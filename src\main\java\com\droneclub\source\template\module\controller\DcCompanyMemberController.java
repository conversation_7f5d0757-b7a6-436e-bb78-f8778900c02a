package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcCompanyMember;
import com.droneclub.source.template.module.pojo.DcCompanyMemberSearch;
import com.droneclub.source.template.module.pojo.DcCompanyMemberVO;
import com.droneclub.source.template.module.service.IDcCompanyMemberService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dcCompanyMember")
public class DcCompanyMemberController {

    private final IDcCompanyMemberService memberService;

    @GetMapping("/getMemberList")
    public RestResult<ListData<DcCompanyMemberVO>> getMemberList(DcCompanyMemberSearch params) {
        return RestResult.success(memberService.getMemberList(params));
    }

    @PostMapping("/createMember")
    public RestResult<DcCompanyMember> createMember(@RequestBody DcCompanyMember data) {
        return RestResult.success(memberService.createMember(data));
    }

    @PutMapping("/updateMember")
    public RestResult<Boolean> updateMember(@RequestBody DcCompanyMember data) {
        return RestResult.success(memberService.updateMember(data));
    }

    @DeleteMapping("/deleteMemberById")
    public RestResult<Boolean> deleteMemberById(Integer id) {
        return RestResult.success(memberService.deleteMemberById(id));
    }

    @GetMapping("/checkMemberExists")
    public RestResult<Boolean> checkMemberExists(Integer userId, Integer companyId) {
        return RestResult.success(memberService.checkMemberExists(userId, companyId));
    }
} 