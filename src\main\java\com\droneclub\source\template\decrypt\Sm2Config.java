package com.droneclub.source.template.decrypt;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "sm2")
public class Sm2Config {

    private boolean development;

    private String requestPublicKeyHax;

    private String requestPrivateKeyHax;

    private String responsePublicKeyHax;

    private String responsePrivateKeyHax;

}
