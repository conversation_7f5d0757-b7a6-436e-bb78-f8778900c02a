package com.droneclub.source.template.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.droneclub.source.template.entity.PublishInfo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface PublishInfoMapper extends BaseMapper<PublishInfo> {

    @Select("SELECT * FROM dc_publish_info WHERE bind_url is not null ORDER BY create_time DESC LIMIT 1")
    PublishInfo getLatestPublishInfo();

    @Insert("${insertSql}")
    int executeUrl(String insertSql);
}
