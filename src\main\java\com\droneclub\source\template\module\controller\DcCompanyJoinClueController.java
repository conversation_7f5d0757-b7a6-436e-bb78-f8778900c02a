package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcCompanyJoinClue;
import com.droneclub.source.template.module.pojo.DcCompanyJoinClueVO;
import com.droneclub.source.template.module.pojo.DcCompanyJoinClueListSearch;
import com.droneclub.source.template.module.service.IDcCompanyJoinClueService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dcCompanyJoinClue")
public class DcCompanyJoinClueController {

    private final IDcCompanyJoinClueService dcCompanyJoinClueService;

    @GetMapping("/getDcCompanyJoinClueList")
    public RestResult<ListData<DcCompanyJoinClueVO>> getDcCompanyJoinClueList(DcCompanyJoinClueListSearch params) {
        return RestResult.success(dcCompanyJoinClueService.getDcCompanyJoinClueList(params));
    }

    @GetMapping("/getDcCompanyJoinClueById")
    public RestResult<DcCompanyJoinClueVO> getDcCompanyJoinClueById(Integer id) {
        return RestResult.success(dcCompanyJoinClueService.getDcCompanyJoinClueById(id));
    }

    @PostMapping("/createDcCompanyJoinClue")
    public RestResult<DcCompanyJoinClue> createDcCompanyJoinClue(@RequestBody DcCompanyJoinClue data) {
        return RestResult.success(dcCompanyJoinClueService.createDcCompanyJoinClue(data));
    }

    @PutMapping("/updateDcCompanyJoinClue")
    public RestResult<Boolean> updateDcCompanyJoinClue(@RequestBody DcCompanyJoinClue data) {
        return RestResult.success(dcCompanyJoinClueService.updateDcCompanyJoinClue(data));
    }

    @DeleteMapping("/deleteDcCompanyJoinClueById")
    public RestResult<Boolean> deleteDcCompanyJoinClueById(Integer id) {
        return RestResult.success(dcCompanyJoinClueService.deleteDcCompanyJoinClueById(id));
    }
}
