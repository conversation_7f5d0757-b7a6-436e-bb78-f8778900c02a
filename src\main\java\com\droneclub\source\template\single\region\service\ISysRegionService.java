package com.droneclub.source.template.single.region.service;


import com.droneclub.source.template.single.region.pojo.RegionTreeVO;
import com.droneclub.source.template.single.region.pojo.SysCityVO;
import com.droneclub.source.template.single.region.pojo.SysDistrictVO;
import com.droneclub.source.template.single.region.pojo.SysProvinceVO;

import java.util.List;
import java.util.Map;

/**
 * 区域服务接口
 */
public interface ISysRegionService {

    /**
     * 同步高德地图省市区数据
     * @return 是否同步成功
     */
    boolean syncRegionData();
    
    /**
     * 获取完整地区信息（三级分类结构）
     * 所有数据按照首字母排序
     * @return 完整地区信息
     */
    List<RegionTreeVO> getCompleteRegionTree();

    /**
     * 获取首字母分组城市列表
     * @return 城市列表
     */
    Map<String,List<SysCityVO>> firstCharCities();

    /**
     * 获取热门城市
     * @return 热门城市列表
     */
    List<SysCityVO> hotCities();

    /**
     * 查询省份列表
     * @return 省份列表
     */
    List<SysProvinceVO> listProvinces();
    
    /**
     * 根据省份编码获取所有城市
     * @param provinceCode 省份编码
     * @return 城市列表
     */
    List<SysCityVO> listCitiesByProvinceCode(String provinceCode);
    
    /**
     * 根据城市编码获取所有区县
     * @param cityCode 城市编码
     * @return 区县列表
     */
    List<SysDistrictVO> listDistrictsByCityCode(String cityCode);
}