package com.droneclub.source.template.module.service;

import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcChapterDoTopicRecord;
import com.droneclub.source.template.module.pojo.DcChapterDoTopicRecordVO;
import com.droneclub.source.template.module.pojo.DcChapterDoTopicRecordListSearch;

public interface IDcChapterDoTopicRecordService {
    
    ListData<DcChapterDoTopicRecordVO> getDcChapterDoTopicRecordList(DcChapterDoTopicRecordListSearch params);

    DcChapterDoTopicRecordVO getDcChapterDoTopicRecordById(Integer id);

    DcChapterDoTopicRecord createDcChapterDoTopicRecord(DcChapterDoTopicRecord data);

    boolean updateDcChapterDoTopicRecord(DcChapterDoTopicRecord data);

    boolean deleteDcChapterDoTopicRecordById(Integer id);
}
