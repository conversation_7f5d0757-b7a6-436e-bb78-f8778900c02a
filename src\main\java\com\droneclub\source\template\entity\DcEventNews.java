package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("dc_event_news")
public class DcEventNews {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String eventStatus;
    private String eventType;
    private String title;
    private String synopsis;
    private String eventUrl;
    private String coverImg;
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    private String createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer updateUser;
    private String updateTime;
    private String isDelete;

}
