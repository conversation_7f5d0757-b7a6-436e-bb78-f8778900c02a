package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tm_rel_user_role")
public class TmRelUserRole {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer userId;
    private String roleCode;
    @TableField(exist = false)
    private String roleName;

}
