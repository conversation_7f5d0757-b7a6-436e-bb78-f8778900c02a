package com.droneclub.source.template.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.droneclub.source.template.entity.DcTopic;
import com.droneclub.source.template.entity.DcTopicDetailAnalysis;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface DcTopicDetailAnalysisMapper extends BaseMapper<DcTopicDetailAnalysis> {
    @Select("SELECT * FROM dc_topic WHERE id not in (SELECT topic_id FROM dc_topic_detail_analysis) order by id asc ")
    List<DcTopic> selectNotExistTopic();
}