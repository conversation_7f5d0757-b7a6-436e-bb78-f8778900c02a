package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcAllTopicRecord;
import com.droneclub.source.template.module.pojo.DcAllTopicRecordVO;
import com.droneclub.source.template.module.pojo.DcAllTopicRecordListSearch;
import com.droneclub.source.template.module.service.IDcAllTopicRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dcAllTopicRecord")
public class DcAllTopicRecordController {

    private final IDcAllTopicRecordService dcAllTopicRecordService;

    @GetMapping("/getDcAllTopicRecordList")
    public RestResult<ListData<DcAllTopicRecordVO>> getDcAllTopicRecordList(DcAllTopicRecordListSearch params) {
        return RestResult.success(dcAllTopicRecordService.getDcAllTopicRecordList(params));
    }

    @GetMapping("/getDcAllTopicRecordById")
    public RestResult<DcAllTopicRecordVO> getDcAllTopicRecordById(Integer id) {
        return RestResult.success(dcAllTopicRecordService.getDcAllTopicRecordById(id));
    }

    @PostMapping("/createDcAllTopicRecord")
    public RestResult<DcAllTopicRecord> createDcAllTopicRecord(@RequestBody DcAllTopicRecord data) {
        return RestResult.success(dcAllTopicRecordService.createDcAllTopicRecord(data));
    }

    @PutMapping("/updateDcAllTopicRecord")
    public RestResult<Boolean> updateDcAllTopicRecord(@RequestBody DcAllTopicRecord data) {
        return RestResult.success(dcAllTopicRecordService.updateDcAllTopicRecord(data));
    }

    /**
     * 删除错误答题记录
     * @param topicId 题目id
     * @return
     */
    @DeleteMapping("/deleteErrorAnswerRecord")
    public RestResult<Boolean> deleteDcAllTopicRecordById(Integer topicId) {
        return RestResult.success(dcAllTopicRecordService.deleteErrorAnswerRecord(topicId));
    }
}
