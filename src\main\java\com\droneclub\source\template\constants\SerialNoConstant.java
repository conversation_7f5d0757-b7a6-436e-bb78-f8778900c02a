package com.droneclub.source.template.constants;


import java.time.LocalDate;

public interface SerialNoConstant {

    /**
     * 初始化序列号
     */
    int INIT_SIZE = 1;
    /**
     * 序列号步长
     */
    int STEP_SIZE = 1;

    /**
     * 4位流水号
     */
    String FOUR_FORMAT = "0000";
    /**
     * 7位流水号
     */
    String SEVEN_FORMAT = "0000000";
    /**
     * 8位流水号
     */
    String EIGHT_FORMAT = "00000000";

    /**
     * 固定序列号使用时间
     */
    LocalDate FIX_DATE = LocalDate.of(2023, 1, 1);

    /**
     * 订单
     */
    String DD_MODULE = "DD";
}
