package com.droneclub.source.template.system.service;

import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.User;
import com.droneclub.source.template.system.pojo.OpenMember;
import com.droneclub.source.template.system.pojo.UserListSearch;
import com.droneclub.source.template.system.pojo.UserVO;

import java.util.List;

public interface IUserService {


    List<User> getUserOption(UserListSearch params);

    ListData<UserVO> getUserList(UserListSearch params);

    UserVO getUserById(String id);

    User createUser(User data);

    boolean updateUser(User data);

    boolean deleteUserById(String id);

    User getUserByAuthId(Integer authId);

    Boolean openMember(OpenMember openMember);
}
