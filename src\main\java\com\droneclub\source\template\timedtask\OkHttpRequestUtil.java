package com.droneclub.source.template.timedtask;

import okhttp3.*;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

public class OkHttpRequestUtil {
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(1800, TimeUnit.SECONDS) // 连接超时
            .writeTimeout(1800, TimeUnit.SECONDS)   // 写入超时
            .readTimeout(1800, TimeUnit.SECONDS)    // 读取超时
            .build();

    public static String postRequest(String url, String jsonBody, String bearerToken) {
        try {
            MediaType JSON = MediaType.get("application/json; charset=utf-8");
            RequestBody body = RequestBody.create(JSON, jsonBody);

            Request request = new Request.Builder()
                    .url(url)
                    .header("Authorization", "Bearer " + bearerToken)
                    .header("Content-Type", "application/json")
                    .post(body)
                    .build();

            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    return response.body().string();
                }
                return null;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getRequest(String url, String bearerToken) {
        try {
            Request request = new Request.Builder()
                    .url(url)
                    .header("Authorization", "Bearer " + bearerToken)
                    .header("Content-Type", "application/json")
                    .get()
                    .build();

            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    return response.body().string();
                }
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
