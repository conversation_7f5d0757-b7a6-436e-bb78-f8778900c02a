package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.droneclub.source.template.entity.DcCompanyProduct;
import com.droneclub.source.template.entity.DcFile;
import com.droneclub.source.template.mapper.DcCompanyProductMapper;
import com.droneclub.source.template.module.pojo.DcCompanyProductListSearch;
import com.droneclub.source.template.module.pojo.DcCompanyProductVO;
import com.droneclub.source.template.module.service.IDcCompanyProductService;
import com.droneclub.source.template.module.service.IDcFileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcCompanyProductServiceImpl implements IDcCompanyProductService {

    private final DcCompanyProductMapper dcCompanyProductMapper;
    private final IDcFileService dcFileService;

    @Override
    public List<DcCompanyProductVO> getDcCompanyProductList(DcCompanyProductListSearch params) {
        LambdaQueryWrapper<DcCompanyProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcCompanyProduct::getCompanyId, params.getCompanyId());
        queryWrapper.orderByDesc(DcCompanyProduct::getCreateTime);
        List<DcCompanyProduct> list = dcCompanyProductMapper.selectList(queryWrapper);
        List<DcCompanyProductVO> listVO = list.stream()
                .map(dcCompanyProduct -> JSONObject.parseObject(JSONObject.toJSONString(dcCompanyProduct), DcCompanyProductVO.class))
                .collect(Collectors.toList());
        for (DcCompanyProductVO dcCompanyProductVO : listVO) {
            buildExtraInfo(dcCompanyProductVO);
        }
        return listVO;
    }

    private void buildExtraInfo(DcCompanyProductVO vo) {
        List<DcFile> files = dcFileService.getFileList("cpfw", vo.getId());
        Map<String, List<DcFile>> fileTypeMap = files.stream().collect(Collectors.groupingBy(DcFile::getFileType));
        JSONObject filesJson = new JSONObject();
        for (String fileType : fileTypeMap.keySet()) {
            filesJson.put(fileType, fileTypeMap.get(fileType).stream()
                    .map(DcFile::getFilePath)
                    .collect(Collectors.toList()));
        }
        vo.setFiles(filesJson);
    }

    private void bindFile(DcCompanyProduct data) {
        if (data.getFiles() == null || data.getFiles().size() == 0) {
            return;
        }
        for (String key : data.getFiles().keySet()) {
            dcFileService.bindFile("cpfw", key, data.getId(),
                    JSONArray.parseArray(data.getFiles().getJSONArray(key).toJSONString(), String.class)
            );
        }
    }

    @Override
    public DcCompanyProductVO getDcCompanyProductById(Integer id) {
        DcCompanyProduct dcCompanyProduct = dcCompanyProductMapper.selectById(id);
        if (dcCompanyProduct == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        return JSONObject.parseObject(JSONObject.toJSONString(dcCompanyProduct), DcCompanyProductVO.class);
    }


    @Override
    public DcCompanyProduct createDcCompanyProduct(DcCompanyProduct data) {
        data.setProductStatus(1);
        boolean rs = dcCompanyProductMapper.insert(data) > 0;
        log.info("创建 DcCompanyProduct: {}", rs ? "成功" : "失败");
        bindFile(data);
        return data;
    }

    @Override
    public boolean updateDcCompanyProduct(DcCompanyProduct data) {
        boolean rs = dcCompanyProductMapper.updateById(data) > 0;
        log.info("更新 DcCompanyProduct: {}", rs ? "成功" : "失败");
        bindFile(data);
        return rs;
    }

    @Override
    public boolean deleteDcCompanyProductById(Integer id) {
        boolean rs = dcCompanyProductMapper.deleteById(id) > 0;
        log.info("删除 DcCompanyProduct: {}", rs ? "成功" : "失败");
        return rs;
    }
}
