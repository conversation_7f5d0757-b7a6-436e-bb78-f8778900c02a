package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@TableName("dc_topic_detail_analysis")
public class DcTopicDetailAnalysis {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer topicId;
    private String topicAiDetailAnalysis;
}