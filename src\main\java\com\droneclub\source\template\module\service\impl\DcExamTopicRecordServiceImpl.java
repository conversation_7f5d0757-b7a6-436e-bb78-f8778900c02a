package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.common.utils.TemplateSessionUtils;
import com.droneclub.source.template.entity.DcAllTopicRecord;
import com.droneclub.source.template.entity.DcExamTopicRecord;
import com.droneclub.source.template.entity.DcTopic;
import com.droneclub.source.template.mapper.DcExamTopicRecordMapper;
import com.droneclub.source.template.module.pojo.DcExamTopicRecordListSearch;
import com.droneclub.source.template.module.pojo.DcExamTopicRecordVO;
import com.droneclub.source.template.module.service.IDcAllTopicRecordService;
import com.droneclub.source.template.module.service.IDcExamTopicRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcExamTopicRecordServiceImpl implements IDcExamTopicRecordService {

    private final DcExamTopicRecordMapper dcExamTopicRecordMapper;

    @Override
    public ListData<DcExamTopicRecordVO> getDcExamTopicRecordList(DcExamTopicRecordListSearch params) {
        QueryWrapper<DcExamTopicRecord> queryWrapper = new QueryWrapper<>();
        // 查询总数
        Long total = dcExamTopicRecordMapper.selectCount(queryWrapper);

        // 分页查询
        Page<DcExamTopicRecord> page = new Page<>(params.getPageNo(), params.getPageSize());
        IPage<DcExamTopicRecord> dcExamTopicRecordPage = dcExamTopicRecordMapper.selectPage(page, queryWrapper);
        List<DcExamTopicRecord> list = dcExamTopicRecordPage.getRecords();
        List<DcExamTopicRecordVO> listVO = list.stream()
                .map(dcExamTopicRecord -> JSONObject.parseObject(JSONObject.toJSONString(dcExamTopicRecord), DcExamTopicRecordVO.class))
                .collect(Collectors.toList());
        return new ListData<>(listVO, total, params.getPageNo(), params.getPageSize());
    }

    @Override
    public DcExamTopicRecordVO getDcExamTopicRecordById(Integer id) {
        DcExamTopicRecord dcExamTopicRecord = dcExamTopicRecordMapper.selectById(id);
        if (dcExamTopicRecord == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        return JSONObject.parseObject(JSONObject.toJSONString(dcExamTopicRecord), DcExamTopicRecordVO.class);
    }


    @Override
    public DcExamTopicRecord createDcExamTopicRecord(DcExamTopicRecord data) {
        boolean rs = dcExamTopicRecordMapper.insert(data) > 0;
        log.info("创建 DcExamTopicRecord: {}", rs ? "成功" : "失败");
        return data;
    }

    @Override
    public boolean updateDcExamTopicRecord(DcExamTopicRecord data) {
        LambdaQueryWrapper<DcExamTopicRecord> examTopicRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        examTopicRecordLambdaQueryWrapper.eq(DcExamTopicRecord::getExamId, data.getExamId());
        examTopicRecordLambdaQueryWrapper.eq(DcExamTopicRecord::getTopicId, data.getTopicId());
        DcExamTopicRecord examTopicRecord = dcExamTopicRecordMapper.selectOne(examTopicRecordLambdaQueryWrapper);
        data.setAnswerResult(data.getTopicAnswer().equals(data.getCorrectAnswer()));
        data.setId(examTopicRecord.getId());
        boolean rs = dcExamTopicRecordMapper.updateById(data) > 0;
        log.info("更新 DcExamTopicRecord: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public boolean deleteDcExamTopicRecordById(Integer id) {
        boolean rs = dcExamTopicRecordMapper.deleteById(id) > 0;
        log.info("删除 DcExamTopicRecord: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public void batchInsert(Integer examId, List<DcTopic> topics) {
        List<DcExamTopicRecord> listData = new ArrayList<>();
        for (DcTopic dcTopic : topics) {
            listData.add(DcExamTopicRecord.builder()
                    .examId(examId)
                    .topicId(dcTopic.getId())
                    .build());
        }
        dcExamTopicRecordMapper.insertBatch(listData);
    }

    @Override
    public List<DcExamTopicRecord> getExamTopicRecord(Integer examId) {
        return dcExamTopicRecordMapper.selectExamTopicRecordWithChapter(examId);
    }

    /**
     * 获取当前用户错题记录
     */
    @Override
    public List<DcExamTopicRecord> getExamErrorTopicRecord() {
        LambdaQueryWrapper<DcExamTopicRecord> examTopicRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        examTopicRecordLambdaQueryWrapper.eq(DcExamTopicRecord::getCreateUser, TemplateSessionUtils.getCurrentUser().getId());
        examTopicRecordLambdaQueryWrapper.eq(DcExamTopicRecord::isAnswerResult, false);
        return dcExamTopicRecordMapper.selectList(examTopicRecordLambdaQueryWrapper);
    }

    @Override
    public List<DcExamTopicRecord> getExamErrorTopicRecord(Integer examIdForErrorTopic) {
        LambdaQueryWrapper<DcExamTopicRecord> examTopicRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        examTopicRecordLambdaQueryWrapper.eq(DcExamTopicRecord::getExamId, examIdForErrorTopic);
        examTopicRecordLambdaQueryWrapper.eq(DcExamTopicRecord::isAnswerResult, false);
        return dcExamTopicRecordMapper.selectList(examTopicRecordLambdaQueryWrapper);
    }
}
