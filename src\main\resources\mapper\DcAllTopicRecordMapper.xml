<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.droneclub.source.template.mapper.DcAllTopicRecordMapper">

    <select id="getAnswerStatsByUserId" resultType="com.droneclub.source.template.module.pojo.AnswerStats">
        SELECT
                COUNT(DISTINCT topic_id) AS totalCount,
                COUNT(DISTINCT CASE WHEN answer_right = 1 THEN topic_id END) AS correctCount
            FROM dc_all_topic_record
            WHERE user_id = #{userId}
        <if test="chapterNames != null and chapterNames.size() > 0">
            AND chapter_name IN
            <foreach item="item" collection="chapterNames" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY RAND()
    </select>
</mapper>