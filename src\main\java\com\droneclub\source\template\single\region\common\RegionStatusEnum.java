package com.droneclub.source.template.single.region.common;

/**
 * 地区状态枚举
 */
public enum RegionStatusEnum {
    /**
     * 删除
     */
    DELETED(0, "删除"),
    
    /**
     * 有效
     */
    VALID(1, "有效"),
    
    /**
     * 更新
     */
    UPDATED(2, "更新");
    
    private final int code;
    private final String desc;
    
    RegionStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
} 