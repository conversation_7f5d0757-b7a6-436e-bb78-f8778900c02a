package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("dc_exam_topic_record")
public class DcExamTopicRecord {

    @TableId(type = IdType.AUTO)
    private Integer id;
    @TableField(exist = false)
    private String chapterName;
    private Integer examId;
    private Integer topicId;
    private String topicAnswer;
    private String correctAnswer;
    private boolean answerResult;
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    private String createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer updateUser;
    private String updateTime;
    private Integer isDelete;

}
