<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.droneclub.source.template.mapper.DcOperationRecordMapper">

    <select id="countShowListData" parameterType="com.droneclub.source.template.module.pojo.DcOperationRecordListSearch"
            resultType="long">
        select count(1)
        from dc_operation_record dor
        left join dc_publish_info dpi on dor.business_id = dpi.id
        where dor.operation_type = #{operationType}
        and dor.operation_user = #{operationUser}
        and dpi.info_status = #{dataStatus}
        <if test="infoType != null">
            and dpi.info_type = #{infoType}
        </if>
    </select>

    <select id="getPublishInfoVOList" resultType="com.droneclub.source.template.module.pojo.PublishInfoVO">
        select dpi.*, dor.create_time as 'viewTime', dor.business_create_by
        from dc_operation_record dor
        left join dc_publish_info dpi on dor.business_id = dpi.id
        where dor.operation_type = #{operationType}
        and dor.operation_user = #{operationUser}
        <if test="dataStatus != null">
            and dpi.info_status = #{dataStatus}
        </if>
        <if test="infoType != null">
            and dpi.info_type = #{infoType}
        </if>
        order by dor.create_time desc
        limit #{offset}, #{pageSize}
    </select>

</mapper>