package com.droneclub.source.template.system.service.impl;

import cn.soulspark.source.common.constants.RedisPrefix;
import cn.soulspark.source.common.execption.ZkException;
import cn.soulspark.source.common.utils.UUIDUtils;
import cn.soulspark.source.starter.common.redis.RedisUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.droneclub.source.template.common.model.TemplateCurrentUser;
import com.droneclub.source.template.common.utils.TemplateSessionUtils;
import com.droneclub.source.template.entity.*;
import com.droneclub.source.template.mapper.*;
import com.droneclub.source.template.module.pojo.DcAuthInfoVO;
import com.droneclub.source.template.module.service.IDcAuthInfoService;
import com.droneclub.source.template.system.dto.UserAuthDTO;
import com.droneclub.source.template.system.pojo.LoginResult;
import com.droneclub.source.template.system.service.IAuthManegeService;
import com.droneclub.source.template.system.service.IAuthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements IAuthService {

    private final TmUserMapper userMapper;
    private final IAuthManegeService authManegeService;
    private final TmRelUserRoleMapper userRoleMapper;
    private final DcOperationRecordMapper operationRecordMapper;
    private final DcMemberOpenMapper memberOpenMapper;
    private final DcCompanyMemberMapper memberMapper;
    private final DcCompanyStudentMapper companyStudentMapper;
    private final DcCompanyMapper dcCompanyMapper;
    @Lazy
    @Autowired
    private IDcAuthInfoService authInfoService;

    @Override
    public LoginResult wxLogin(String account) {
        User user = getLoginUser(account);
        String token = UUIDUtils.getUUID();
        TemplateCurrentUser currentUser = TemplateCurrentUser.builder()
                .id(user.getId())
                .account(user.getAccount())
                .authId(user.getAuthId())
                .name(user.getUserName())
                .authStatus(user.getAuthStatus())
                .token(token)
                .certificateType(user.getCertificateType())
                .certificateStatus(user.getCertificateStatus())
                .build();
        // 取认证信息
        if (user.getAuthId() != null) {
            DcAuthInfoVO dcAuthInfoVO = authInfoService.getDcAuthInfoById(user.getAuthId());
            currentUser.setAuthInfo(dcAuthInfoVO);
        }
        // 设置会话缓存
        RedisUtils.set(RedisPrefix.USER_SESSION + token, currentUser, 300L, TimeUnit.DAYS);
        RedisUtils.setSet(RedisPrefix.USER_SESSION + user.getId(), token);
        return LoginResult.builder().token(token).build();
    }

    /**
     * 获取登录用户信息
     *
     * @param account 用户账号
     * @return 用户信息
     */
    public User getLoginUser(String account) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("account", account);
        User existUser = userMapper.selectOne(wrapper);
        if (existUser == null) {
            User newUser = User.builder()
                    .account(account)
                    .userName(account)
                    .build();
            boolean rs = userMapper.insert(newUser) > 0;
            // 赋权通用角色
            userRoleMapper.insert(TmRelUserRole.builder().userId(newUser.getId()).roleCode("common_role").build());
            log.info("账号: {} 用户不存在, 创建用户: {}", newUser, rs ? "成功" : "失败");
            return newUser;
        }
        return existUser;
    }

    @Override
    public boolean logout(Integer userId) {
        if (userId == null) {
            userId = TemplateSessionUtils.getCurrentUser().getId();
        }
        Set<Object> tokens = RedisUtils.getSet(RedisPrefix.USER_SESSION + userId);
        for (Object token : tokens) {
            RedisUtils.delete(RedisPrefix.USER_SESSION + token);
        }
        RedisUtils.delete(RedisPrefix.USER_SESSION + userId);
        return true;
    }

    @Override
    public TemplateCurrentUser getCurrentUser() {
        TemplateCurrentUser existAuthUser = TemplateSessionUtils.getCurrentUser();
        // 添加用户最新状态
        User user = userMapper.selectById(existAuthUser.getId());
        // 获取最新角色和权限信息
        LambdaQueryWrapper<TmRelUserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmRelUserRole::getUserId, user.getId());
        Set<String> roleCodes = userRoleMapper.selectList(wrapper).stream().map(TmRelUserRole::getRoleCode).collect(Collectors.toSet());
        // 获取权限信息
        if (roleCodes.size() == 0) {
            throw new ZkException("用户未被授权任何角色");
        }
        List<UserAuthDTO> userAuths = authManegeService.getUserAuthData(user.getId());

        TemplateCurrentUser currentUser = new TemplateCurrentUser(user);
        currentUser.setToken(existAuthUser.getToken());
        currentUser.setRoleCodes(roleCodes);
        currentUser.setUserAuths(userAuths == null ? new ArrayList<>() : userAuths);
        currentUser.setAvatar(user.getAvatar());
        currentUser.setRegularPlace(user.getRegularPlace());
        currentUser.setExpertise(user.getExpertise());
        currentUser.setCertificateStatus(user.getCertificateStatus());
        currentUser.setCertificateType(user.getCertificateType());
        if (user.getAuthId() != null) {
            // 设置最新认证信息
            DcAuthInfoVO authInfoVO = authInfoService.getDcAuthInfoById(user.getAuthId());
            currentUser.setAuthInfo(authInfoVO);
        }
        // 添加附属统计信息
        LambdaQueryWrapper<DcOperationRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcOperationRecord::getOperationUser, currentUser.getId()).or(v -> v.eq(DcOperationRecord::getBusinessCreateBy, currentUser.getId()));
        List<DcOperationRecord> dcOperationRecords = operationRecordMapper.selectList(queryWrapper);
        currentUser.setViewedNum(dcOperationRecords.stream().filter(v -> ("look_publish_info").equals(v.getOperationType())
                && v.getOperationUser().equals(currentUser.getId())).count());

        currentUser.setBeViewedNum(dcOperationRecords.stream().filter(v -> ("look_publish_info").equals(v.getOperationType())
                && v.getBusinessCreateBy().equals(currentUser.getId()) && !v.getOperationUser().equals(currentUser.getId())).count());

        currentUser.setBeCollectedNum(dcOperationRecords.stream().filter(v -> ("collect_publish_info").equals(v.getOperationType())
                && v.getOperationUser().equals(currentUser.getId())).count());
        currentUser.setContactPhone("***********");
        // 添加个人会员信息
        currentUser.setPersonalMemberOpen(getMemberQuery(currentUser.getId(), null));
        // 添加会员获取归属公司
        LambdaQueryWrapper<DcCompanyMember> dcCompanyMemberLambdaQueryWrapper = new LambdaQueryWrapper<>();
        dcCompanyMemberLambdaQueryWrapper.eq(DcCompanyMember::getUserId, currentUser.getId());
        DcCompanyMember dcCompanyMember = memberMapper.selectOne(dcCompanyMemberLambdaQueryWrapper);
        if (dcCompanyMember != null) {
            currentUser.setCompany(dcCompanyMapper.selectById(dcCompanyMember.getCompanyId()));
            if (currentUser.getCompany() != null) {
                // 添加企业会员信息
                currentUser.setCompanyMemberOpen(getMemberQuery(null, currentUser.getCompany().getId()));
            }
        }
        // 添加培训公司的信息
        LambdaQueryWrapper<DcCompanyStudent> dcCompanyStudentLambdaQueryWrapper = new LambdaQueryWrapper<>();
        dcCompanyStudentLambdaQueryWrapper.eq(DcCompanyStudent::getStudentId, currentUser.getId());
        DcCompanyStudent dcCompanyStudent = companyStudentMapper.selectOne(dcCompanyStudentLambdaQueryWrapper);
        if (dcCompanyStudent != null) {
            currentUser.setPxCompany(dcCompanyMapper.selectById(dcCompanyStudent.getCompanyId()));
            if (currentUser.getPxCompany() != null) {
                // 添加企业会员信息
                currentUser.setPxCompanyMemberOpen(getMemberQuery(null, currentUser.getPxCompany().getId()));
            }
        }
        currentUser.setPxCompanyId(user.getPxCompanyId());
        currentUser.setPxCity(user.getPxCity());
        // 添加用户选择的培训机构
        if (user.getPxCompanyId() != null) {
            currentUser.setSelectPxCompany(dcCompanyMapper.selectById(user.getPxCompanyId()));
        }
        return currentUser;
    }

    private DcMemberOpen getMemberQuery(Integer userId, Integer companyId) {
        LambdaQueryWrapper<DcMemberOpen> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DcMemberOpen::getStatus, 1);
        if (userId != null) {
            wrapper.eq(DcMemberOpen::getUserId, userId);
        }
        if (companyId != null) {
            wrapper.eq(DcMemberOpen::getCompanyId, companyId);
        }
        List<DcMemberOpen> opens = memberOpenMapper.selectList(wrapper);
        if (opens.size() == 0) {
            return null;
        } else if (opens.size() == 1) {
            return opens.get(0);
        } else if (opens.size() == 2) {
            DcMemberOpen gjCandidate = null; // 存储 *_gj 类型的候选
            DcMemberOpen normalCandidate = null; // 存储普通类型的候选

            for (DcMemberOpen open : opens) {
                String memberType = open.getMemberType();

                // 优先匹配 *_gj 类型
                if ("month_gj".equals(memberType) || "quarter_gj".equals(memberType)
                        || "year_gj".equals(memberType)) {
                    gjCandidate = open;
                }

                // 如果没有匹配到 *_gj，再匹配普通类型
                if (normalCandidate == null && ("month".equals(memberType)
                        || "quarter".equals(memberType)
                        || "year".equals(memberType))) {
                    normalCandidate = open;
                }

                // 如果已经找到最高优先级的 *_gj，直接返回
                if (gjCandidate != null) {
                    return gjCandidate;
                }
            }

            // 如果存在普通类型，返回普通类型，否则返回第一个元素
            return normalCandidate != null ? normalCandidate : opens.get(0);
        }
        return null;
    }
}
