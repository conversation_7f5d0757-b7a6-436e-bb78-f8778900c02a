﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,k)),m,[],n,_(h,o),p,[q],r,_(s,t,u,v,g,w,x,_(),y,[],z,_(A,B,C,D,E,_(F,G,H,I),J,null,K,L,L,M,N,O,null,P,Q,R,S,T,U,V,Q,W,X,_(F,G,H,Y),Z,Q,ba,bb,_(bc,bd,be,bf,bg,bf,bh,bf,bi,k,H,_(bj,bk,bl,bk,bm,bk,bn,bo)),i,_(j,k,l,k)),bp,_(),bq,_(),br,_(bs,[_(bt,bu,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,bC,l,bD),A,bE,bF,_(bG,bH,bI,bJ),E,_(F,G,H,bK)),bp,_(),bL,_(),bM,bd),_(bt,bN,bv,h,bw,bO,u,bP,bz,bP,bA,bB,z,_(A,bQ,i,_(j,bC,l,bR),bF,_(bG,bH,bI,bJ),J,null),bp,_(),bL,_(),bS,_(bT,bU)),_(bt,bV,bv,h,bw,bO,u,bP,bz,bP,bA,bB,z,_(A,bQ,i,_(j,bW,l,bX),bF,_(bG,bY,bI,bZ),J,null),bp,_(),bL,_(),bS,_(bT,ca)),_(bt,cb,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cc,l,cd),A,ce,bF,_(bG,cf,bI,cg),ch,D,ci,cj),bp,_(),bL,_(),bM,bd),_(bt,ck,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,bC,l,cl),A,bE,bF,_(bG,bH,bI,cm)),bp,_(),bL,_(),bM,bd),_(bt,cn,bv,h,bw,co,u,cp,bz,cp,bA,bB,z,_(),bp,_(),bL,_(),cq,[_(bt,cr,bv,h,bw,cs,u,by,bz,by,bA,bB,z,_(i,_(j,ct,l,ct),A,cu,bF,_(bG,cv,bI,cw)),bp,_(),bL,_(),bS,_(bT,cx),bM,bd),_(bt,cy,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cz,l,cA),A,ce,bF,_(bG,cB,bI,cC),ci,cD),bp,_(),bL,_(),bM,bd)],cE,bd),_(bt,cF,bv,h,bw,cs,u,by,bz,by,bA,bB,z,_(i,_(j,ct,l,ct),A,cu,bF,_(bG,cG,bI,cw)),bp,_(),bL,_(),bS,_(bT,cH),bM,bd),_(bt,cI,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cJ,l,cA),A,ce,bF,_(bG,cK,bI,cC),ci,cD),bp,_(),bL,_(),bM,bd),_(bt,cL,bv,h,bw,cs,u,by,bz,by,bA,bB,z,_(i,_(j,ct,l,ct),A,cu,bF,_(bG,cM,bI,cw)),bp,_(),bL,_(),bS,_(bT,cN),bM,bd),_(bt,cO,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cz,l,cA),A,ce,bF,_(bG,cP,bI,cC),ci,cD),bp,_(),bL,_(),bM,bd),_(bt,cQ,bv,h,bw,cs,u,by,bz,by,bA,bB,z,_(i,_(j,ct,l,ct),A,cu,bF,_(bG,cR,bI,cw)),bp,_(),bL,_(),bS,_(bT,cS),bM,bd),_(bt,cT,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cz,l,cA),A,ce,bF,_(bG,cU,bI,cC),ci,cD),bp,_(),bL,_(),bM,bd),_(bt,cV,bv,h,bw,cs,u,by,bz,by,bA,bB,z,_(i,_(j,ct,l,ct),A,cu,bF,_(bG,cW,bI,cw)),bp,_(),bL,_(),bS,_(bT,cX),bM,bd),_(bt,cY,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cz,l,cA),A,ce,bF,_(bG,cZ,bI,cC),ci,cD),bp,_(),bL,_(),bM,bd),_(bt,da,bv,h,bw,bO,u,bP,bz,bP,bA,bB,z,_(i,_(j,bC,l,db),A,bQ,J,null,bF,_(bG,bH,bI,dc)),bp,_(),bL,_(),bS,_(bT,dd)),_(bt,de,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(df,dg,i,_(j,dh,l,di),A,dj,bF,_(bG,dk,bI,dl)),bp,_(),bL,_(),bM,bd),_(bt,dm,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,dn,l,dp),A,bE,bF,_(bG,dq,bI,dr),Z,ds),bp,_(),bL,_(),bM,bd),_(bt,dt,bv,h,bw,cs,u,by,bz,by,bA,bB,z,_(i,_(j,du,l,du),A,cu,bF,_(bG,dv,bI,dw)),bp,_(),bL,_(),bS,_(bT,dx),bM,bd),_(bt,dy,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,dz,l,cA),A,ce,bF,_(bG,dA,bI,dB),ci,cD),bp,_(),bL,_(),bM,bd),_(bt,dC,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(df,dg,i,_(j,dD,l,dE),A,ce,bF,_(bG,dF,bI,dG)),bp,_(),bL,_(),bM,bd),_(bt,dH,bv,h,bw,cs,u,by,bz,by,bA,bB,z,_(i,_(j,du,l,du),A,cu,bF,_(bG,dI,bI,dw)),bp,_(),bL,_(),bS,_(bT,dJ),bM,bd),_(bt,dK,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,dz,l,cA),A,ce,bF,_(bG,dL,bI,dB),ci,cD),bp,_(),bL,_(),bM,bd),_(bt,dM,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,dn,l,dp),A,bE,bF,_(bG,dq,bI,dN),Z,ds),bp,_(),bL,_(),bM,bd),_(bt,dO,bv,h,bw,cs,u,by,bz,by,bA,bB,z,_(i,_(j,du,l,du),A,cu,bF,_(bG,dv,bI,dP)),bp,_(),bL,_(),bS,_(bT,dQ),bM,bd),_(bt,dR,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,dz,l,cA),A,ce,bF,_(bG,dA,bI,dS),ci,cD),bp,_(),bL,_(),bM,bd),_(bt,dT,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(df,dg,i,_(j,dD,l,dE),A,ce,bF,_(bG,dF,bI,dU)),bp,_(),bL,_(),bM,bd),_(bt,dV,bv,h,bw,cs,u,by,bz,by,bA,bB,z,_(i,_(j,du,l,du),A,cu,bF,_(bG,dW,bI,dP)),bp,_(),bL,_(),bS,_(bT,dX),bM,bd),_(bt,dY,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cg,l,cA),A,ce,bF,_(bG,dZ,bI,dS),ci,cD),bp,_(),bL,_(),bM,bd),_(bt,ea,bv,h,bw,cs,u,by,bz,by,bA,bB,z,_(i,_(j,du,l,du),A,cu,bF,_(bG,dW,bI,dw)),bp,_(),bL,_(),bS,_(bT,eb),bM,bd),_(bt,ec,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,dz,l,cA),A,ce,bF,_(bG,ed,bI,dB),ci,cD),bp,_(),bL,_(),bM,bd),_(bt,ee,bv,h,bw,cs,u,by,bz,by,bA,bB,z,_(i,_(j,du,l,du),A,cu,bF,_(bG,dI,bI,dP)),bp,_(),bL,_(),bS,_(bT,ef),bM,bd),_(bt,eg,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,dz,l,cA),A,ce,bF,_(bG,dL,bI,dS),ci,cD),bp,_(),bL,_(),bM,bd),_(bt,eh,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,ei,l,ej),A,ek,bF,_(bG,el,bI,em),ci,cj),bp,_(),bL,_(),bM,bd)])),en,_(),eo,_(ep,_(eq,er),es,_(eq,et),eu,_(eq,ev),ew,_(eq,ex),ey,_(eq,ez),eA,_(eq,eB),eC,_(eq,eD),eE,_(eq,eF),eG,_(eq,eH),eI,_(eq,eJ),eK,_(eq,eL),eM,_(eq,eN),eO,_(eq,eP),eQ,_(eq,eR),eS,_(eq,eT),eU,_(eq,eV),eW,_(eq,eX),eY,_(eq,eZ),fa,_(eq,fb),fc,_(eq,fd),fe,_(eq,ff),fg,_(eq,fh),fi,_(eq,fj),fk,_(eq,fl),fm,_(eq,fn),fo,_(eq,fp),fq,_(eq,fr),fs,_(eq,ft),fu,_(eq,fv),fw,_(eq,fx),fy,_(eq,fz),fA,_(eq,fB),fC,_(eq,fD),fE,_(eq,fF),fG,_(eq,fH)));}; 
var b="url",c="教学.html",d="generationDate",e=new Date(1750408319191.74),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=0,l="height",m="adaptiveViews",n="sketchKeys",o="s0",p="variables",q="OnLoadVariable",r="page",s="packageId",t="9871e9a2c06b4243aa9752f3b1d9c3a8",u="type",v="Axure:Page",w="教学",x="notes",y="annotations",z="style",A="baseStyle",B="627587b6038d43cca051c114ac41ad32",C="pageAlignment",D="center",E="fill",F="fillType",G="solid",H="color",I=0xFFFFFFFF,J="image",K="imageAlignment",L="near",M="imageRepeat",N="auto",O="favicon",P="sketchFactor",Q="0",R="colorStyle",S="appliedColor",T="fontName",U="Applied Font",V="borderWidth",W="borderVisibility",X="borderFill",Y=0xFF797979,Z="cornerRadius",ba="cornerVisibility",bb="outerShadow",bc="on",bd=false,be="offsetX",bf=5,bg="offsetY",bh="blurRadius",bi="spread",bj="r",bk=0,bl="g",bm="b",bn="a",bo=0.349019607843137,bp="adaptiveStyles",bq="interactionMap",br="diagram",bs="objects",bt="id",bu="3b14fc79c26b4a648372fc4405f811c3",bv="label",bw="friendlyType",bx="矩形",by="vectorShape",bz="styleType",bA="visible",bB=true,bC=417,bD=751,bE="93950f64c5104d7fbe432f744db64e34",bF="location",bG="x",bH=119,bI="y",bJ=41,bK=0xFFF5F5F5,bL="imageOverrides",bM="generateCompound",bN="2f696600653c4383a036c500b62a1c5e",bO="图片 ",bP="imageBox",bQ="********************************",bR=62,bS="images",bT="normal~",bU="images/教学/u1.png",bV="45bacc38471b4d438e2410c7c414452f",bW=71,bX=35,bY=292,bZ=68,ca="images/教学/u2.png",cb="62c68d79185b4d01977be61bf8623168",cc=32,cd=18,ce="f8c70a63ec8c4ded9e1c2963c0e658a5",cf=312,cg=72,ch="horizontalAlignment",ci="fontSize",cj="16px",ck="546a03e27be1428989a6657b3555171d",cl=53,cm=739,cn="568f39e01841429cab91de378038b5df",co="组合",cp="layer",cq="objs",cr="530f770945bf4f9d8460c07665d86ca6",cs="占位符",ct=25,cu="d47d8120d97741469da4152217605972",cv=149,cw=744,cx="images/教学/u6.svg",cy="a904657cddbe475b972ab5629a0f7af2",cz=24,cA=14,cB=150,cC=773,cD="12px",cE="propagate",cF="50b8481dea5d413583603ba753bd49ec",cG=230,cH="images/教学/u8.svg",cI="3b14e4b1fcb24f56adfd08f581790bac",cJ=36,cK=225,cL="2d6c6e9822944a97bcd4b62e9bc7216c",cM=315,cN="images/教学/u10.svg",cO="5b736d48f4cb4d61a3f161805be1c6d4",cP=316,cQ="1a07fa9aa2624e248b0ab94d0036267b",cR=393,cS="images/教学/u12.svg",cT="90e8eb45e2b14660b23d15200f92a79c",cU=394,cV="61707fbaa44d49fcb50c59352f67fca7",cW=470,cX="images/教学/u14.svg",cY="0fad95a9d37545a6a79feee792ea1b75",cZ=471,da="6466e12f6a2648b7a3b2fc6ea391baeb",db=140,dc=103,dd="images/教学/u16.svg",de="a22854b5035640238c82dc35a3b5c61c",df="fontWeight",dg="700",dh=97,di=21,dj="8c7a4c5ad69a4369a5f7788171ac0b32",dk=279,dl=163,dm="e58e40ac76d942ba8d9ebb3fc18555ef",dn=395,dp=122,dq=130,dr=254,ds="15",dt="0bad7edae5804fcd86a67740e189d783",du=40,dv=162,dw=295,dx="images/教学/u19.svg",dy="07c1e809b23b4542b976afb1e55f39fc",dz=48,dA=158,dB=339,dC="fa8febd1ee4a4a99a63028f2252ffd7c",dD=56,dE=16,dF=146,dG=262,dH="b22e0591b1644647bf73b9c2d5cd4c29",dI=255,dJ="images/教学/u22.svg",dK="8fe2a72784fe473191c5d599c3795c76",dL=251,dM="6cd44561845c40ba8ce0602d019a90e8",dN=391,dO="c8ddfc37cce647f1b4803e34a7cc85f4",dP=432,dQ="images/教学/u25.svg",dR="1dfc7e90fd6a4dff80597a1f608e92bb",dS=476,dT="59bc89a206b44830a4bcf52aaf71e723",dU=399,dV="b93280cbc1bc4fd7b7cc11291a3e9625",dW=349,dX="images/教学/u28.svg",dY="0803c8b088424e0b936c3fcc1dd62cad",dZ=333,ea="495297c552bf4eb0a1f8e845df8a5935",eb="images/教学/u30.svg",ec="ebbd3c6024c44833b9fc7bb2e3648c15",ed=345,ee="bfe300c1a4014a41b4ad1617e30035a6",ef="images/教学/u32.svg",eg="97df0da9d41d42a49245b9f046dca96a",eh="a27a63fd4b234f7a86a6d81015102375",ei=605,ej=221,ek="31e8887730cc439f871dc77ac74c53b6",el=567,em=120,en="masters",eo="objectPaths",ep="3b14fc79c26b4a648372fc4405f811c3",eq="scriptId",er="u0",es="2f696600653c4383a036c500b62a1c5e",et="u1",eu="45bacc38471b4d438e2410c7c414452f",ev="u2",ew="62c68d79185b4d01977be61bf8623168",ex="u3",ey="546a03e27be1428989a6657b3555171d",ez="u4",eA="568f39e01841429cab91de378038b5df",eB="u5",eC="530f770945bf4f9d8460c07665d86ca6",eD="u6",eE="a904657cddbe475b972ab5629a0f7af2",eF="u7",eG="50b8481dea5d413583603ba753bd49ec",eH="u8",eI="3b14e4b1fcb24f56adfd08f581790bac",eJ="u9",eK="2d6c6e9822944a97bcd4b62e9bc7216c",eL="u10",eM="5b736d48f4cb4d61a3f161805be1c6d4",eN="u11",eO="1a07fa9aa2624e248b0ab94d0036267b",eP="u12",eQ="90e8eb45e2b14660b23d15200f92a79c",eR="u13",eS="61707fbaa44d49fcb50c59352f67fca7",eT="u14",eU="0fad95a9d37545a6a79feee792ea1b75",eV="u15",eW="6466e12f6a2648b7a3b2fc6ea391baeb",eX="u16",eY="a22854b5035640238c82dc35a3b5c61c",eZ="u17",fa="e58e40ac76d942ba8d9ebb3fc18555ef",fb="u18",fc="0bad7edae5804fcd86a67740e189d783",fd="u19",fe="07c1e809b23b4542b976afb1e55f39fc",ff="u20",fg="fa8febd1ee4a4a99a63028f2252ffd7c",fh="u21",fi="b22e0591b1644647bf73b9c2d5cd4c29",fj="u22",fk="8fe2a72784fe473191c5d599c3795c76",fl="u23",fm="6cd44561845c40ba8ce0602d019a90e8",fn="u24",fo="c8ddfc37cce647f1b4803e34a7cc85f4",fp="u25",fq="1dfc7e90fd6a4dff80597a1f608e92bb",fr="u26",fs="59bc89a206b44830a4bcf52aaf71e723",ft="u27",fu="b93280cbc1bc4fd7b7cc11291a3e9625",fv="u28",fw="0803c8b088424e0b936c3fcc1dd62cad",fx="u29",fy="495297c552bf4eb0a1f8e845df8a5935",fz="u30",fA="ebbd3c6024c44833b9fc7bb2e3648c15",fB="u31",fC="bfe300c1a4014a41b4ad1617e30035a6",fD="u32",fE="97df0da9d41d42a49245b9f046dca96a",fF="u33",fG="a27a63fd4b234f7a86a6d81015102375",fH="u34";
return _creator();
})());