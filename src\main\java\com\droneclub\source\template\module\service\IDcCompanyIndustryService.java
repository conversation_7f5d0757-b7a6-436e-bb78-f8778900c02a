package com.droneclub.source.template.module.service;

import com.droneclub.source.template.entity.DcCompanyIndustry;
import com.droneclub.source.template.module.pojo.DcCompanyIndustryListSearch;
import com.droneclub.source.template.module.pojo.DcCompanyIndustryVO;

import java.util.List;

public interface IDcCompanyIndustryService {

    List<DcCompanyIndustryVO> getDcCompanyIndustryList(DcCompanyIndustryListSearch params);

    DcCompanyIndustryVO getDcCompanyIndustryById(Integer id);

    DcCompanyIndustry createDcCompanyIndustry(DcCompanyIndustry data);

    boolean saveDcCompanyIndustry(Integer companyId, String industries);

    boolean updateDcCompanyIndustry(DcCompanyIndustry data);

    boolean deleteDcCompanyIndustryById(Integer id);
}
