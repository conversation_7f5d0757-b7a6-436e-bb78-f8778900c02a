package com.droneclub.source.template.common.interceptor;

import cn.soulspark.source.common.constants.RedisPrefix;
import cn.soulspark.source.common.execption.ZkException;
import cn.soulspark.source.common.utils.StringUtils;
import cn.soulspark.source.starter.common.redis.RedisUtils;
import com.alibaba.fastjson.JSONObject;
import com.droneclub.source.template.common.cache.CompanyCache;
import com.droneclub.source.template.common.model.TemplateCurrentUser;
import com.droneclub.source.template.common.model.TemplateResultCode;
import com.droneclub.source.template.common.utils.CompanyUtils;
import com.droneclub.source.template.common.utils.TemplateSessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class SessionInterceptor implements HandlerInterceptor {

    private final List<String> urlWhiteList;
    private final List<String> urlPrefixWhiteList;
    private final String apiPrefix;

    public SessionInterceptor(List<String> urlWhiteList, String apiPrefix) {
        this.urlWhiteList = urlWhiteList.stream()
                .filter(v -> !v.contains("*"))
                .collect(Collectors.toList());

        this.urlPrefixWhiteList = urlWhiteList.stream()
                .filter(v -> v.contains("*"))
                .map(s -> s.substring(0, s.indexOf("*")))
                .collect(Collectors.toList());

        this.apiPrefix = apiPrefix;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) {
        String requestURI = request.getRequestURI().replaceFirst(apiPrefix, "");
        log.info("访问接口: {}", requestURI);
        String openApiToken = request.getHeader("OpenApiToken");
        if (StringUtils.isValid(openApiToken)) {
            if ("50f7d15896e245d19860130ddb420962".equals(openApiToken)) {
                return true;
            }
            throw new ZkException("401", "Token不正确.");
        }
        String token = request.getHeader("Authorization");
        if (urlWhiteList.contains(requestURI) || urlPrefixWhiteList.stream().anyMatch(requestURI::startsWith)) {
            if (StringUtils.isValid(token)) {
                Object obj = RedisUtils.get(RedisPrefix.USER_SESSION + token);
                if (obj != null) {
                    TemplateCurrentUser currentUser = JSONObject.parseObject(JSONObject.toJSONString(obj), TemplateCurrentUser.class);
                    TemplateSessionUtils.setCurrentUser(currentUser);
                }
            }
            return true;
        }
        if (StringUtils.isInvalid(token)) {
            throw new ZkException(TemplateResultCode.LOGIN_TIMEOUT.getCode(), "暂未登录，请登录后使用。");
        }
        Object obj = RedisUtils.get(RedisPrefix.USER_SESSION + token);
        if (obj == null) {
            throw new ZkException(TemplateResultCode.LOGIN_TIMEOUT.getCode(), "登录时间过长，请重新登录。");
        }
        TemplateCurrentUser currentUser = JSONObject.parseObject(JSONObject.toJSONString(obj), TemplateCurrentUser.class);
       /* if (currentUser == null || !currentUser.getUserAuths()
                .stream()
                .map(UserAuthDTO::getApiPath)
                .collect(Collectors.toSet()).contains(requestURI)
        ) {
            throw new ZkException("当前用户无此功能访问权限。");
        }*/
        TemplateSessionUtils.setCurrentUser(currentUser);
        String channel = request.getHeader("channel");
        if (StringUtils.isValid(channel)) {
            Boolean check = CompanyUtils.isSystemValid(channel);
            if (check == null) {
                throw new ZkException("OTHER_SYSTEM_FAIL", "未开通系统独立运行权限, 请联系管理员续费.");
            }
            if (!check) {
                throw new ZkException("OTHER_SYSTEM_FAIL", "系统已过期, 请联系管理员续费.");
            }
            CompanyCache.setDcCompany(CompanyUtils.getCompany(channel));
        }
        return true;
    }
}
