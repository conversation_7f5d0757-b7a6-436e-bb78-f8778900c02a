package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.droneclub.source.template.common.utils.ResourceUtils;
import com.droneclub.source.template.entity.DcCompany;
import com.droneclub.source.template.entity.DcCompanyIndustry;
import com.droneclub.source.template.mapper.DcCompanyMapper;
import com.droneclub.source.template.module.pojo.DcCompanyIndustryListSearch;
import com.droneclub.source.template.module.pojo.DcCompanyIndustryVO;
import com.droneclub.source.template.module.service.IDcCompanyIndustryService;
import com.droneclub.source.template.single.region.utils.PinyinUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/dcCompanyIndustry")
public class DEL_DcCompanyIndustryController {

    private final IDcCompanyIndustryService dcCompanyIndustryService;
    private static final String[] DATA_JSON = new String[]{"temp-data1.json", "temp-data2.json", "temp-data3.json"};

    @GetMapping("/getDcCompanyIndustryList")
    public RestResult<List<DcCompanyIndustryVO>> getDcCompanyIndustryList(DcCompanyIndustryListSearch params) {
        return RestResult.success(dcCompanyIndustryService.getDcCompanyIndustryList(params));
    }

    @GetMapping("/getDcCompanyIndustryById")
    public RestResult<DcCompanyIndustryVO> getDcCompanyIndustryById(Integer id) {
        return RestResult.success(dcCompanyIndustryService.getDcCompanyIndustryById(id));
    }

    @PostMapping("/createDcCompanyIndustry")
    public RestResult<DcCompanyIndustry> createDcCompanyIndustry(@RequestBody DcCompanyIndustry data) {
        return RestResult.success(dcCompanyIndustryService.createDcCompanyIndustry(data));
    }

    @PutMapping("/updateDcCompanyIndustry")
    public RestResult<Boolean> updateDcCompanyIndustry(@RequestBody DcCompanyIndustry data) {
        return RestResult.success(dcCompanyIndustryService.updateDcCompanyIndustry(data));
    }

    @DeleteMapping("/deleteDcCompanyIndustryById")
    public RestResult<Boolean> deleteDcCompanyIndustryById(Integer id) {
        return RestResult.success(dcCompanyIndustryService.deleteDcCompanyIndustryById(id));
    }

    private final DcCompanyMapper dcCompanyMapper;


    /**
     * 刷新公司拼音
     * @return 公司列表
     */
    @GetMapping("/dealCompanyNamePinyin")
    public RestResult<Boolean> dealCompanyNamePinyin() {
        List<DcCompany> companies = dcCompanyMapper.selectList(null); // 查询所有数据
        companies.forEach(company -> {
            // 使用 PinyinUtils 填充首字母和全拼
            company.setFirstChar(PinyinUtils.getFirstLetter(company.getCompanyName()));
            company.setPinyin(PinyinUtils.getFullPinyin(company.getCompanyName()));
            dcCompanyMapper.updateById(company); // 更新数据
        });
        return RestResult.success(true);
    }

    @GetMapping("/dealData")
    public RestResult<Boolean> dealData() {
        Map<String, String[]> mapData = new HashMap<>();
        ClassLoader classLoader = ResourceUtils.class.getClassLoader();
        try (InputStream inputStream = classLoader.getResourceAsStream("otherdata/地理map.txt")) {
            if (inputStream == null) {
                log.error("资源不存在: {}", "otherdata/地理map.txt");
                throw new IOException("Resource not found: " + "otherdata/地理map.txt");
            }

            StringBuilder stringBuilder = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    String[] keyValuePairs = line.split("=");
                    if (keyValuePairs.length != 2) {
                        throw new IllegalArgumentException("Invalid input format");
                    }

                    String key = keyValuePairs[0].trim();
                    String value = keyValuePairs[1].trim();

                    // 按逗号分割值并转换为数组
                    String[] values = value.split("\\s*,\\s*");

                    mapData.put(key, values);
                }

            }

        } catch (IOException e) {
            log.error("加载: {} 失败", "otherdata/地理map.txt", e);
        }
        for (String orgData : DATA_JSON) {
            JSONObject json = JSONObject.parseObject(ResourceUtils.loadResource("otherdata/" + orgData));
            JSONArray rows = json.getJSONArray("rows");
            for (int i = 0; i < rows.size(); i++) {
                JSONObject item = rows.getJSONObject(i);
                String city = "";
                try {
                    dcCompanyMapper.insert(DcCompany.builder()
                            // 默认不在列表展示
                            .showList(0)
                            // 默认机构状态是审批通过
                            .companyStatus(2)
                            .companyName(item.getString("danwmc"))
                            .linkman(item.getString("xunlfzrxm"))
                            .contactWay(item.getString("xunlfzrlxdh"))
                            .province(item.getString("area"))
                            .baseAddress(item.getString("zhuyjd"))
                            .creditCode(item.getString("usc"))
                            .legalPerson(item.getString("farmc"))
                            .city(mapData.get(item.getString("bangsdz")) != null ? mapData.get(item.getString("bangsdz"))[1] : null)
                            .detailAddress(item.getString("bangsdz"))
                            .build());
                    System.out.println(item.getString("bangsdz"));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        return RestResult.success(true);
    }
}
