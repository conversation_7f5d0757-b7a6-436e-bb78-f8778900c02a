<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.droneclub.source.template.mapper.SequenceMapper">

    <insert id="init">
        INSERT INTO sys_sequence (module_code, `date`, current_value)
        VALUES (#{moduleCode}, #{date}, #{currentValue})
    </insert>

    <select id="getCurrentValue" resultType="java.lang.Integer">
        SELECT current_value FROM sys_sequence
        WHERE module_code = #{moduleCode} AND `date` = #{date}
        <if test="lock">
            FOR UPDATE
        </if>
    </select>

    <update id="progress">
        UPDATE sys_sequence
        SET current_value = #{newValue}
        WHERE module_code = #{moduleCode}
          AND `date` = #{date}
          AND current_value = #{oldValue}
    </update>

</mapper>