package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("dc_company_student")
public class DcCompanyStudent {
    
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 公司id
     */
    private Integer companyId;

    /**
     * 教练id
     */
    private Integer coachId;
    /**
     * 学生id
     */
    private Integer studentId;

    private String studentName;
    
    private String studentCourse;
    
    private String studentStage;

    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;

    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer updateUser;

    private LocalDateTime updateTime;
    
    private Integer isDelete;
} 