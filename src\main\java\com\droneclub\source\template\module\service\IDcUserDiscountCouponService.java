package com.droneclub.source.template.module.service;

import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcUserDiscountCoupon;
import com.droneclub.source.template.module.pojo.DcUserDiscountCouponVO;
import com.droneclub.source.template.module.pojo.DcUserDiscountCouponListSearch;

public interface IDcUserDiscountCouponService {
    
    ListData<DcUserDiscountCouponVO> getDcUserDiscountCouponList(DcUserDiscountCouponListSearch params);

    DcUserDiscountCouponVO getDcUserDiscountCouponById(Integer id);

    DcUserDiscountCoupon createDcUserDiscountCoupon(DcUserDiscountCoupon data);

    boolean updateDcUserDiscountCoupon(DcUserDiscountCoupon data);

    boolean deleteDcUserDiscountCouponById(Integer id);
}
