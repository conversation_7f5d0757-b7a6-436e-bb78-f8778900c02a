package com.droneclub.source.template.decrypt;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdvice;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;


/**
 * 解密请求体, 仅支持 application/json
 */
@ControllerAdvice
public class DecryptRequestBodyAdvice implements RequestBodyAdvice {

    @Override
    public boolean supports(MethodParameter parameter,
                            Type targetType,
                            Class<? extends HttpMessageConverter<?>> converterType) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String contentType = request.getContentType();
        return contentType != null && contentType.startsWith("application/json");
    }

    @Override
    public HttpInputMessage beforeBodyRead(HttpInputMessage inputMessage,
                                           MethodParameter parameter,
                                           Type targetType,
                                           Class<? extends HttpMessageConverter<?>> converterType)
            throws IOException {
        String requestBodyStr = readInputStream(inputMessage);
        JSONObject cipherJSON = JSONObject.parseObject(requestBodyStr);
        if (!cipherJSON.containsKey("cipherText")) {
            // 直接返回数据，不做任何处理
            return new DecryptedHttpInputMessage(cipherJSON.toJSONString(), inputMessage.getHeaders());
        }
        String cipherText = cipherJSON.getString("cipherText");
        String plainText = decrypt(cipherText);
        return new DecryptedHttpInputMessage(plainText, inputMessage.getHeaders());
    }

    @Override
    public Object afterBodyRead(Object body,
                                HttpInputMessage inputMessage,
                                MethodParameter parameter,
                                Type targetType,
                                Class<? extends HttpMessageConverter<?>> converterType) {
        // 可根据需要进行处理，这里直接返回解密后的主体
        return body;
    }

    @Override
    public Object handleEmptyBody(Object defaultValue, HttpInputMessage httpInputMessage,
                                  MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
        return defaultValue; // 如果请求体为空，返回默认值
    }

    private String readInputStream(HttpInputMessage inputMessage) throws IOException {
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputMessage.getBody(), StandardCharsets.UTF_8));
        StringBuilder stringBuilder = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            stringBuilder.append(line);
        }
        return stringBuilder.toString();
    }

    private String decrypt(String cipherText) {
        // 解密逻辑
        return SM2Utils.decrypt(cipherText);
    }

    @Data
    private static class DecryptedHttpInputMessage implements HttpInputMessage {
        private final ByteArrayInputStream body;
        private final HttpHeaders headers;

        public DecryptedHttpInputMessage(String decryptedBody, HttpHeaders headers1) {
            this.body = new ByteArrayInputStream(decryptedBody.getBytes(StandardCharsets.UTF_8));
            this.headers = headers1;
        }
    }
}