<html>
<head>
    <title>Axure RP - Safari Local File Restrictions</title>
    <style type="text/css">
        *
        {
            font-family: Helvetica, Arial, sans-serif;
        }
        body
        {
            text-align: center;
            background-color: #fafafa;
        }
        p
        {
            font-size: 14px;
            line-height: 18px;
            color: #333333;
        }
        div.container
        {
            width: 980px;
            margin-left: auto;
            margin-right: auto;
            text-align: left;
        }
        a
        {
            text-decoration: none;
            color: #009dda;
        }
        .button
        {
            background: #A502B3;
            font: normal 16px Arial, sans-serif;
            color: #FFFFFF;
            padding: 10px 30px 10px 30px;
            border: 2px solid #A502B3;
            display: inline-block;
            margin-top: 10px;
            text-transform: uppercase;
            font-size: 14px;
	    border-radius: 4px;
        }
        a:hover.button
        {
            border: 2px solid #A502B3;
	    color: #A502B3;
	    background-color: #FFFFFF;
        }
        div.left
        {
            width: 400px;
            float: left;
            margin-right: 80px;
        }
        div.right
        {
            width: 400px;
            float: left;
        }
        div.buttonContainer
        {
            text-align: center;
        }
        h1
        {
            font-size: 36px;
            color: #333333;
            line-height: 50px;
            margin-bottom: 20px;
            font-weight: normal;
        }
        h2
        {
            font-size: 24px;
            font-weight: normal;
            color: #08639C;
            text-align: center;
        }
        h3
        {
            font-size: 16px;
            line-height: 24px;
            color: #333333;
            font-weight: normal;
        }
        .heading
        {
            border-bottom: 1px solid black;
	    height: 36px;
            line-height: 36px;
            font-size: 22px;
            color: #000000;
        }
        span.faq
        {
            font-size: 16px;
            font-weight: normal;
	    text-transform: uppercase;
            color: #333333;
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <br />
        <br />
        <img src="axure_logo.png" alt="axure" />
        <br />
        <h1>
            SAFARI LOCAL FILE RESTRICTIONS</h1>
        <p style="font-size: 16px; line-height: 24px; color: #666666; margin-top: 10px;">
            To view locally stored projects in Safari, you will need to "disable local file restrictions". Alternatively,
            you can upload your RP file to <a href="https://www.axure.cloud">Axure Cloud</a> or publish the local files to a web server. You can also Preview from Axure RP.</p>
            <img src="preview-rp.png" alt="preview"/>
        <h3 class="heading">
            VIEW LOCAL PROJECTS IN SAFARI</h3>
        <div class="">
            <h3>
                1. Open "Safari > Preferences > Advanced" from the top menu, and check the option to "Show Develop menu in menu bar"</h3>
            <img src="safari_advanced.png" alt="advanced" />
        </div>
        <div style="clear: both; height: 20px;">
            &nbsp;
        </div>
        <div class="">
            <h3>
                2. In the Develop menu that appears in the menu bar, click "Develop > Disable Local File Restrictions" to un-select the menu option</h3>
            <img src="safari_restrictions.png" alt="extensions" />
        </div>
        <div style="clear: both; height: 20px;">
            &nbsp;</div>
        <div class="left">
            <h3>
                3. Click the button below
            </h3>
            <div class="buttonContainer">
                <a class="button" href="../../start.html">View in Safari</a>
            </div>
        </div>
        <div style="clear: both; height: 20px;">
        </div>
        <h3 class="heading">
            We're Here to Help</h3>
        <p>
            Need help or have any questions? Drop us a line at <a href="mailto:<EMAIL>">
                <EMAIL></a>.
        </p>
        <div style="clear: both; height: 20px;">
        </div>
    </div>
</body>
</html>
