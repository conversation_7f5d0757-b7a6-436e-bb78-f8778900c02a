<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="INFO" monitorInterval="30" additivity="false">
    <Properties>
        <Property name="ServiceName">drone-club</Property>
        <Property name="LOG_HOME">./logs</Property>
        <Property name="LOG_NAME">drone-club</Property>
        <property name="LOG_FORMAT">[%-level] %d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %logger{36} - %msg%n</property>
        <property name="BACKUP_HOME">${LOG_HOME}/backup</property>
        <property name="BACK_Hz">%d{yyyy-MM-dd}</property>
    </Properties>
    <Appenders>
        <!-- 日志输出至控制台 -->
        <console NAME="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout charset="UTF-8">
                <pattern>%highlight{[%-level] %d{yyyy-MM-dd HH:mm:ss.SSS}} [%t] %logger{36} - %highlight{%msg}%n
                </pattern>
            </PatternLayout>
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMisMatch="NEUTRAL"/>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMisMatch="DENY"/>
            </Filters>
        </console>

        <!-- 日志等级:INFO 备份频率:每天备份  -->
        <RollingFile name="InfoLogRecord" fileName="${LOG_HOME}/${LOG_NAME}_info.log"
                     filePattern="${BACKUP_HOME}/info/${LOG_NAME}_${BACK_Hz}_%i.log.gz">
            <PatternLayout pattern="${LOG_FORMAT}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="${BACKUP_HOME}/info/" maxDepth="1">
                    <IfFileName glob="*.log.gz"/>
                    <IfLastModified age="180d"/>
                </Delete>
            </DefaultRolloverStrategy>
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMisMatch="NEUTRAL"/>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMisMatch="DENY"/>
            </Filters>
        </RollingFile>

        <!-- 日志等级:ERROR 备份频率:每天备份  -->
        <RollingFile name="ErrorLogRecord" fileName="${LOG_HOME}/${LOG_NAME}_error.log"
                     filePattern="${BACKUP_HOME}/error/${LOG_NAME}_${BACK_Hz}_%i.log.gz">
            <PatternLayout pattern="${LOG_FORMAT}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="${BACKUP_HOME}/error/" maxDepth="1">
                    <IfFileName glob="*.log.gz"/>
                    <IfLastModified age="180d"/>
                </Delete>
            </DefaultRolloverStrategy>
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMisMatch="DENY"/>
            </Filters>
        </RollingFile>
    </Appenders>

    <Loggers>
        <Root level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="InfoLogRecord"/>
            <AppenderRef ref="ErrorLogRecord"/>
        </Root>
    </Loggers>
</Configuration>
