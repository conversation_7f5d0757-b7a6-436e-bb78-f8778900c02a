package com.droneclub.source.template.timedtask;

import cn.soulspark.source.common.enums.HttpMethod;
import cn.soulspark.source.common.enums.MediaTypes;
import cn.soulspark.source.common.http.HttpToolkit;
import cn.soulspark.source.common.http.Response;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.droneclub.source.template.common.utils.DateUtils;
import com.droneclub.source.template.entity.DcMemberOpen;
import com.droneclub.source.template.entity.PublishInfo;
import com.droneclub.source.template.mapper.DcMemberOpenMapper;
import com.droneclub.source.template.mapper.PublishInfoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.GZIPInputStream;

import static com.droneclub.source.template.timedtask.GetJobSp.buildSql;


@Slf4j
@Component
@EnableScheduling
@RequiredArgsConstructor
public class TemplateTimed {

    private final PublishInfoMapper publishInfoMapper;
    private final DcMemberOpenMapper dcMemberOpenMapper;

    @Scheduled(fixedRate = 1000 * 60 * 5)
    public void checkAndUpdateMemberStatus() {
        log.info("开始检查会员过期状态...");
        LambdaQueryWrapper<DcMemberOpen> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.lt(DcMemberOpen::getEndTime, DateUtils.getNowDateTime())
                .eq(DcMemberOpen::getStatus, 1);
        List<DcMemberOpen> opens = dcMemberOpenMapper.selectList(queryWrapper);
        for (DcMemberOpen dcMemberOpen : opens) {
            dcMemberOpen.setStatus(0);
            dcMemberOpenMapper.updateById(dcMemberOpen);
            log.info("会员ID: {}, 过期时间: {}, 当前已过期.", dcMemberOpen.getId(), dcMemberOpen.getEndTime());
        }
    }

    /**
     * 判断字符串是否为数字（包括整数、小数、科学计数法）
     *
     * @param str 要判断的字符串
     * @return 如果是数字返回true，否则返回false
     */
    public static boolean isNumeric(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        // 正则表达式，允许整数、小数、科学计数法，允许前导符号
        String regex = "^[+-]?(?:\\d+\\.?\\d*|\\.\\d+)(?:[eE][+-]?\\d+)?$";
        return str.matches(regex);
    }

    // @Scheduled(cron = "0 0/1 * * * ?")
    @Scheduled(cron = "0 15 20 * * *")
    public void getInfo() {
        PublishInfo publishInfo = publishInfoMapper.getLatestPublishInfo();
        JSONObject resultJson = getWrjfsInfo();
        JSONArray arrayJson = resultJson.getJSONArray("rows");
        for (int i = 0; i < arrayJson.size(); i++) {
            JSONObject item = arrayJson.getJSONObject(i);
            if (item.getString("id").equals(publishInfo.getBindUrl())) {
                break;
            }
            String name = item.getString("company");

            String address = item.getString("address");
            String startTime = item.getString("startTime");
            String content = item.getString("detail");
            String extraSkill = item.getString("无");
            String bindUrl = item.getString("id");
            JSONObject phoneJson = getWrjfsPhone(bindUrl);
            String phone = phoneJson.getJSONObject("data").getString("phone");
            Integer salary = -1;
            if (isNumeric(phoneJson.getJSONObject("data").getString("salary"))) {
                salary = phoneJson.getJSONObject("data").getInteger("salary");
            }
            String insertSql = buildSql(name, phone, address, startTime, content, extraSkill, bindUrl, salary);
            // 执行生成的sql语句
            publishInfoMapper.executeUrl(insertSql);
            log.info("执行插入语句: {}", insertSql);
        }

    }

    public static void main(String[] args) {
        getWrjfsInfo();
    }

    public static JSONObject getWrjfsInfo() {
        Map<String, String> params = new HashMap<>();
        params.put("Host", "www.wrjfs.com");
        params.put("Connection", "keep-alive");
        params.put("xweb_xhr", "1");
        params.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c11)XWEB/11275");
        params.put("Content-Type", "application/json");
        params.put("Accept", "*/*");
        params.put("Sec-Fetch-Site", "cross-site");
        params.put("Sec-Fetch-Mode", "cors");
        params.put("Sec-Fetch-Dest", "empty");
        params.put("Referer", "https://servicewechat.com/wxb7ab992867c1ea36/34/page-frame.html");
        params.put("Accept-Encoding", "gzip, deflate, br");
        params.put("Accept-Language", "zh-CN,zh;q=0.9");
        Response response = HttpToolkit.request("https://www.wrjfs.com/api/mallApi/jobs/list", HttpMethod.POST)
                .addHeader(params)
                .addRaw("{\"state\":1,\"city\":\"南京市\",\"province\":\"\",\"pageNum\":0,\"pageSize\":20,\"isAsc\":\"desc\",\"ctype\":1,\"name\":\"飞手_WHZEs8\"}")
                .rawRequest(MediaTypes.APPLICATION_JSON)
                .sync();

        byte[] bytes = null;
        try {
            bytes = decompress(response.getBody());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        String strResult = new String(bytes);
        return JSONObject.parseObject(strResult);
    }

    public static JSONObject getWrjfsPhone(String id) {
        Map<String, String> params = new HashMap<>();
        Response response = HttpToolkit.request("https://www.wrjfs.com/api/mallApi/jobs/get/" + id + "/飞手_WHZEs8/1", HttpMethod.GET)
                .addHeader(params)
                .paramsRequest()
                .sync();

        return JSONObject.parseObject(response.getString());
    }

    public static byte[] decompress(byte[] compressedData) throws IOException {
        try (GZIPInputStream gzipInputStream = new GZIPInputStream(new ByteArrayInputStream(compressedData));
             ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[1024];
            int len;
            while ((len = gzipInputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, len);
            }
            return byteArrayOutputStream.toByteArray();
        }
    }
}
