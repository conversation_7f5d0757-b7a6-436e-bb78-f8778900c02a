package com.droneclub.source.template.system.service.impl;

import cn.hutool.core.io.file.FileReader;
import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import com.droneclub.source.template.system.service.ITemplateService;
import freemarker.template.Template;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.freemarker.FreeMarkerProperties;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

import java.io.File;
import java.io.IOException;
import java.io.StringWriter;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Map;


@Slf4j
@Service
@RequiredArgsConstructor
public class TemplateServiceImpl implements ITemplateService {

    private final FreeMarkerConfigurer freeMarkerConfigurer;
    private final FreeMarkerProperties freeMarkerProperties;

    public String getStringTemplate(Map<String, Object> fillData, String templateName) {
        StringWriter stringWriter = new StringWriter();
        try {
            Template template = freeMarkerConfigurer.getConfiguration().getTemplate(templateName);
            template.process(fillData, stringWriter);
        } catch (Exception e) {
            log.error("字符模板套打操作失败, {} {}", templateName, fillData, e);
            throw new ZkException(ResultCode.FAIL.getCode(), "模板套打操作失败");
        }
        stringWriter.flush();
        return stringWriter.toString();
    }

    @Override
    public String getTemplateContent(String name) {
        FileReader fileReader = new FileReader(getTemplateFilePath(name));
        return fileReader.readString();
    }

    @Override
    public boolean createTemplateContent(String name, String content) {
        try {
            String path = getTemplateFilePath(name);
            log.info("开始创建模板文件: {}", path);
            Files.write(Paths.get(path), content.getBytes());
            return true;
        } catch (IOException e) {
            log.error("创建模板内容失败, name: {} content: {}", name, content, e);
        }
        return false;
    }

    @Override
    public boolean deleteService(String name) {
        String path = getTemplateFilePath(name);
        log.info("开始删除模板文件: {}", path);
        File file = new File(path);
        if (file.exists()) {
            return file.delete();
        }
        return false;
    }

    private String getTemplateFilePath(String name) {
        String path = freeMarkerProperties.getTemplateLoaderPath()[0] + File.separator + name + freeMarkerProperties.getSuffix();
        return path.replaceAll("file:", "");
    }
}
