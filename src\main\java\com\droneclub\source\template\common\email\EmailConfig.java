package com.droneclub.source.template.common.email;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "email")
public class EmailConfig {
    private String username;
    private String password;
    private String receiver;
}
