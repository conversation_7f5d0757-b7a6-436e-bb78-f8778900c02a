package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.${Module};
import com.droneclub.source.template.mapper.${Module}Mapper;
import com.droneclub.source.template.module.pojo.${Module}ListSearch;
import com.droneclub.source.template.module.pojo.${Module}VO;
import com.droneclub.source.template.module.service.I${Module}Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ${Module}ServiceImpl implements I${Module}Service {

    private final ${Module}Mapper ${module}Mapper;

    @Override
    public ListData<${Module}VO> get${Module}List(${Module}ListSearch params) {
        QueryWrapper<${Module}> queryWrapper = new QueryWrapper<>();
        // 查询总数
        Long total = ${module}Mapper.selectCount(queryWrapper);

        // 分页查询
        Page<${Module}> page = new Page<>(params.getPageNo(), params.getPageSize());
        IPage<${Module}> ${module}Page = ${module}Mapper.selectPage(page, queryWrapper);
        List<${Module}> list = ${module}Page.getRecords();
        List<${Module}VO> listVO = list.stream()
                .map(${module} -> JSONObject.parseObject(JSONObject.toJSONString(${module}), ${Module}VO.class))
                .collect(Collectors.toList());
        return new ListData<>(listVO, total, params.getPageNo(), params.getPageSize());
    }

    @Override
    public ${Module}VO get${Module}ById(Integer id) {
        ${Module} ${module} = ${module}Mapper.selectById(id);
        if (${module} == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        return JSONObject.parseObject(JSONObject.toJSONString(${module}), ${Module}VO.class);
    }


    @Override
    public ${Module} create${Module}(${Module} data) {
        boolean rs = ${module}Mapper.insert(data) > 0;
        log.info("创建 ${Module}: {}", rs ? "成功" : "失败");
        return data;
    }

    @Override
    public boolean update${Module}(${Module} data) {
        boolean rs = ${module}Mapper.updateById(data) > 0;
        log.info("更新 ${Module}: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public boolean delete${Module}ById(Integer id) {
        boolean rs = ${module}Mapper.deleteById(id) > 0;
        log.info("删除 ${Module}: {}", rs ? "成功" : "失败");
        return rs;
    }
}
