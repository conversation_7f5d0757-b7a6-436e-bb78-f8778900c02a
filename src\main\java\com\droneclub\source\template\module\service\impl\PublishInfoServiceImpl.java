package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import cn.soulspark.source.common.utils.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.droneclub.source.template.common.email.EmailConfig;
import com.droneclub.source.template.common.email.EmailSender;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.common.utils.ResourceUtils;
import com.droneclub.source.template.common.utils.TemplateSessionUtils;
import com.droneclub.source.template.entity.DcFile;
import com.droneclub.source.template.entity.DcOperationRecord;
import com.droneclub.source.template.entity.PublishInfo;
import com.droneclub.source.template.entity.User;
import com.droneclub.source.template.mapper.PublishInfoMapper;
import com.droneclub.source.template.mapper.TmUserMapper;
import com.droneclub.source.template.module.pojo.PublishInfoListSearch;
import com.droneclub.source.template.module.pojo.PublishInfoVO;
import com.droneclub.source.template.module.service.IDcFileService;
import com.droneclub.source.template.module.service.IDcOperationRecordService;
import com.droneclub.source.template.module.service.IPublishInfoService;
import com.droneclub.source.template.wechat.service.IWechatSecurityAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PublishInfoServiceImpl implements IPublishInfoService {

    private final PublishInfoMapper publishInfoMapper;
    private final IDcFileService dcFileService;
    private final TmUserMapper userMapper;
    private final IDcOperationRecordService operationRecordService;
    private final EmailSender emailSender;
    private final EmailConfig emailConfig;
    private final IWechatSecurityAuditService wechatSecurityAuditService;

    @Override
    public ListData<PublishInfoVO> getPublishInfoList(PublishInfoListSearch params) {
        LambdaQueryWrapper<PublishInfo> queryWrapper = new LambdaQueryWrapper<>();
        if (params.getInfoType() != null) {
            queryWrapper.eq(PublishInfo::getInfoType, params.getInfoType());
        }
        if (params.getInfoStatus() != null) {
            queryWrapper.eq(PublishInfo::getInfoStatus, params.getInfoStatus());
        }
        if (params.getCreateUserId() != null) {
            queryWrapper.eq(PublishInfo::getCreateUser, params.getCreateUserId());
        }
        if (StringUtils.isValid(params.getJobType())) {
            queryWrapper.eq(PublishInfo::getJobType, params.getJobType());
        }
        if (StringUtils.isValid(params.getArea())) {
            queryWrapper.and(w -> w.like(PublishInfo::getDevicePlace, params.getArea())
                    .or()
                    .like(PublishInfo::getWorkPlace, params.getArea())
                    .or()
                    .like(PublishInfo::getWorkArea, params.getArea()));
        }
        if (StringUtils.isValid(params.getSearchKey())) {
            queryWrapper.and(w -> w.like(PublishInfo::getJobContent, params.getSearchKey())
                    .or()
                    .like(PublishInfo::getExpectJobContent, params.getSearchKey())
                    .or()
                    .like(PublishInfo::getDeviceDescription, params.getSearchKey()));
        }
        // 查询总数
        Long total = publishInfoMapper.selectCount(queryWrapper);

        // 分页查询
        queryWrapper.orderByDesc(PublishInfo::getCreateTime);
        Page<PublishInfo> page = new Page<>(params.getPageNo(), params.getPageSize());
        IPage<PublishInfo> publishInfoPage = publishInfoMapper.selectPage(page, queryWrapper);
        List<PublishInfo> list = publishInfoPage.getRecords();
        List<PublishInfoVO> listVO = list.stream()
                .map(publishInfo -> JSONObject.parseObject(JSONObject.toJSONString(publishInfo), PublishInfoVO.class))
                .collect(Collectors.toList());
        // 关联发布人姓名
        Set<Integer> userIds = listVO.stream().map(PublishInfoVO::getCreateUser).collect(Collectors.toSet());
        Map<Integer, User> idUserNameMap = new HashMap<>();
        if (userIds.size() > 0) {
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            wrapper.in("id", userIds);
            List<User> existUserList = userMapper.selectList(wrapper);
            idUserNameMap = existUserList.stream()
                    .collect(Collectors.toMap(User::getId, v -> v));
        }
        // 绑定影像信息
        for (PublishInfoVO vo : listVO) {
            bindFileInfo(vo);
            vo.setCreateUserName(idUserNameMap.get(vo.getCreateUser()).getUserName());
            vo.setCreateUserAccount(idUserNameMap.get(vo.getCreateUser()).getAccount());
            vo.setActiveState(idUserNameMap.get(vo.getCreateUser()).getActiveState());
        }
        return new ListData<>(listVO, total, params.getPageNo(), params.getPageSize());
    }

    private void bindFileInfo(PublishInfoVO vo) {
        List<DcFile> files = dcFileService.getFileList("publish_info", vo.getId());
        Map<String, List<DcFile>> fileTypeMap = files.stream().collect(Collectors.groupingBy(DcFile::getFileType));
        JSONObject filesJson = new JSONObject();
        for (String fileType : fileTypeMap.keySet()) {
            filesJson.put(fileType, fileTypeMap.get(fileType).stream()
                    .map(DcFile::getFilePath)
                    .collect(Collectors.toList()));
        }
        vo.setFiles(filesJson);
    }

    @Override
    public PublishInfoVO getPublishInfoById(Integer id) {
        PublishInfo publishInfo = publishInfoMapper.selectById(id);
        if (publishInfo == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        // 记录浏览操作
        operationRecordService.createDcOperationRecord(DcOperationRecord.builder()
                .operationType("look_publish_info")
                .businessId(id)
                .businessCreateBy(publishInfo.getCreateUser())
                .build());
        PublishInfoVO publishInfoVO = JSONObject.parseObject(JSONObject.toJSONString(publishInfo), PublishInfoVO.class);
        // 返回当前发布信息是否被当前用户收藏的状态
        boolean operationRecordExist = false;
        if (TemplateSessionUtils.getCurrentUser() != null) {
            operationRecordExist = operationRecordService.checkOperationRecordExist(DcOperationRecord.builder()
                    .operationType("collect_publish_info")
                    .operationUser(TemplateSessionUtils.getCurrentUser().getId())
                    .businessId(id)
                    .build());
        }
        // 绑定文件信息
        bindFileInfo(publishInfoVO);
        publishInfoVO.setCollected(operationRecordExist);
        publishInfoVO.setQRCode("https://7775-wurenjizhijia-6g7yx2x36ba376c4-1326943040.tcb.qcloud.la/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20240602124223.jpg?sign=1b614523f10917ebda0cd61dc77782f9&t=1717303357");
        return publishInfoVO;
    }


    @Override
    public PublishInfo createPublishInfo(PublishInfo data) {
        wechatSecurityAuditService.contentAudit(data.getOpenId(), JSONObject.toJSONString(data), null);
        data.setInfoStatus(1);
        boolean rs = publishInfoMapper.insert(data) > 0;
        log.info("创建 PublishInfo: {}", rs ? "成功" : "失败");
        saveFiles(data);
        if (StringUtils.isValid(data.getContactPerson())) {
            String emailTemplate = ResourceUtils.loadResource("emailTemplate/fabu-notice.html");
            if (emailTemplate != null) {
                emailTemplate = emailTemplate.replaceAll("#\\{name}", data.getContactPerson());
                // 发布信息类型 1:兼职 2:飞手 3: 租赁
                String type = data.getInfoType() == 1 ? "兼职" : (data.getInfoType() == 2 ? "飞手" : "租赁");
                emailTemplate = emailTemplate.replaceAll("#\\{type}", type);
                String subject = String.format("新的信息发布-%s", data.getContactPerson());
                emailSender.createMessage(emailConfig.getReceiver())
                        .subject(subject)
                        .htmlContent(emailTemplate)
                        .send();
                log.info("邮件通知成功: {}", subject);
            }
        }
        return data;
    }

    private void saveFiles(PublishInfo data) {
        if (data.getFiles() != null && data.getFiles().size() > 0) {
            for (String key : data.getFiles().keySet()) {
                dcFileService.bindFile("publish_info", key, data.getId(),
                        JSONArray.parseArray(data.getFiles().getJSONArray(key).toJSONString(), String.class)
                );
            }
        }
    }

    @Override
    public boolean updatePublishInfo(PublishInfo data) {
        wechatSecurityAuditService.contentAudit(data.getOpenId(), JSONObject.toJSONString(data), null);
        boolean rs = publishInfoMapper.updateById(data) > 0;
        log.info("更新 PublishInfo: {}", rs ? "成功" : "失败");
        saveFiles(data);
        return rs;
    }

    @Override
    public boolean deletePublishInfoById(Integer id) {
        boolean rs = publishInfoMapper.deleteById(id) > 0;
        log.info("删除 PublishInfo: {}", rs ? "成功" : "失败");
        return rs;
    }
}
