package com.droneclub.source.template.single.region.pojo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 区县信息VO
 */
@Data
public class SysDistrictVO {
    /**
     * 数据ID
     */
    private Long id;
    
    /**
     * 区县编码
     */
    private String districtCode;
    
    /**
     * 区县名称
     */
    private String districtName;
    
    /**
     * 首字母
     */
    private String firstChar;
    
    /**
     * 等级
     */
    private String level;
    
    /**
     * 中心点坐标
     */
    private String center;
    
    /**
     * 所属城市编码
     */
    private String cityCode;
    
    /**
     * 所属省份编码
     */
    private String provinceCode;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 