<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.droneclub.source.template.mapper.DcExamTopicRecordMapper">

    <select id="selectExamTopicRecordWithChapter" resultType="com.droneclub.source.template.entity.DcExamTopicRecord">
        SELECT r.*, t.chapter_name as chapterName
        FROM dc_exam_topic_record r
        LEFT JOIN dc_topic t ON r.topic_id = t.id
        WHERE r.exam_id = #{examId}
    </select>
</mapper>