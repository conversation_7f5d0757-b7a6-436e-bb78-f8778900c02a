package com.droneclub.source.template.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("dc_company")
public class DcCompany {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String companyName;
    private String linkman;
    private String contactWay;
    /**
     * 所属行业, 多个使用逗号分隔
     */
    @TableField(exist = false)
    private String industry;
    private String city;
    private String detailAddress;
    private String companyProfiles;
    private Integer companyStatus;
    private Integer lookNum;
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    private String createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer updateUser;
    private String updateTime;
    private String isDelete;
    /**
     * 文件信息
     */
    @TableField(exist = false)
    private JSONObject files;
    /**
     * 开通管理员权限标记
     */
    private Integer openAdminFlag;
    /**
     * 是否在列表展示
     */
    private Integer showList;
    private String province;
    private String baseAddress;
    private String creditCode;
    private String legalPerson;
    /**
     * 首字母
     */
    private String firstChar;
    /**
     * 拼音
     */
    private String pinyin;
    /**
     * 公司编码，数据库分配用于独立部署
     */
    private String companyCode;
    /**
     * 系统有效期
     */
    private String systemValidityPeriod;
    /**
     * 微信配置
     */
    private String wxConfig;

}
