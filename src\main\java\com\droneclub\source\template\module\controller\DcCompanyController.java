package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcCompany;
import com.droneclub.source.template.module.pojo.DcCompanyListSearch;
import com.droneclub.source.template.module.pojo.DcCompanyVO;
import com.droneclub.source.template.module.service.IDcCompanyService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dcCompany")
public class DcCompanyController {

    private final IDcCompanyService dcCompanyService;

    @GetMapping("/getDcCompanyOption")
    public RestResult<List<DcCompany>> getDcCompanyOption(DcCompanyListSearch params) {
        return RestResult.success(dcCompanyService.getDcCompanyOption(params));
    }

    @GetMapping("/getPxCompanyOption")
    public RestResult<Map<String, List<DcCompany>>> getPxCompanyOption(DcCompanyListSearch params) {
        return RestResult.success(dcCompanyService.getPxCompanyOption(params));
    }

    @GetMapping("/getDcCompanyList")
    public RestResult<ListData<DcCompanyVO>> getDcCompanyList(DcCompanyListSearch params) {
        return RestResult.success(dcCompanyService.getDcCompanyList(params));
    }

    @GetMapping("/refreshCompanySystemConfig")
    public RestResult<Boolean> refreshCompanySystemConfig() {
        dcCompanyService.refreshCompanySystemConfig();
        return RestResult.success(true);
    }

    @GetMapping("/getDcCompanyById")
    public RestResult<DcCompanyVO> getDcCompanyById(Integer id) {
        return RestResult.success(dcCompanyService.getDcCompanyById(id));
    }

    @PostMapping("/createDcCompany")
    public RestResult<DcCompany> createDcCompany(@RequestBody DcCompany data) {
        return RestResult.success(dcCompanyService.createDcCompany(data));
    }

    @PutMapping("/updateDcCompany")
    public RestResult<Boolean> updateDcCompany(@RequestBody DcCompany data) {
        return RestResult.success(dcCompanyService.updateDcCompany(data));
    }

    @DeleteMapping("/deleteDcCompanyById")
    public RestResult<Boolean> deleteDcCompanyById(Integer id) {
        return RestResult.success(dcCompanyService.deleteDcCompanyById(id));
    }
}
