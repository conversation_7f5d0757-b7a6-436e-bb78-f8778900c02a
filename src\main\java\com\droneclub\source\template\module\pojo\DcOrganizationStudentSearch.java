package com.droneclub.source.template.module.pojo;

import com.droneclub.source.template.common.model.BaseSearchParams;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DcOrganizationStudentSearch  extends BaseSearchParams {
    
    private Integer companyId;
    
    private Integer coachId;
    
    private String studentName;
    
    private String studentCourse;
    
    private String studentStage;
    /**
     * 考试试题类型 base: 基础试题 zh: 综合试题
     */
    private String examTopicType;
} 