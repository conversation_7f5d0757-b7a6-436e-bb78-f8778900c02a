package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.constants.RedisPrefix;
import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import cn.soulspark.source.common.utils.StringUtils;
import cn.soulspark.source.starter.common.redis.RedisUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.common.model.TemplateCurrentUser;
import com.droneclub.source.template.common.utils.DateUtils;
import com.droneclub.source.template.common.utils.TemplateSessionUtils;
import com.droneclub.source.template.entity.DcExamUserRank;
import com.droneclub.source.template.entity.User;
import com.droneclub.source.template.mapper.DcExamUserRankMapper;
import com.droneclub.source.template.mapper.TmUserMapper;
import com.droneclub.source.template.module.pojo.DcExamUserRankListSearch;
import com.droneclub.source.template.module.pojo.DcExamUserRankVO;
import com.droneclub.source.template.module.service.IDcExamUserRankService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcExamUserRankServiceImpl implements IDcExamUserRankService {

    private final DcExamUserRankMapper dcExamUserRankMapper;
    private final TmUserMapper userMapper;

    @Override
    public ListData<DcExamUserRankVO> getDcExamUserRankList(DcExamUserRankListSearch params) {
        LambdaQueryWrapper<DcExamUserRank> examUserRankLambdaQueryWrapper = new LambdaQueryWrapper<>();

        if (StringUtils.isValid(params.getExamType())) {
            Object[] examTypes = "教员".equals(params.getExamType())
                    ? new String[]{"教员"}
                    : new String[]{"视距内", "超视距"};
            examUserRankLambdaQueryWrapper.in(DcExamUserRank::getExamType, examTypes);
        }

        if (StringUtils.isValid(params.getExamTopicType()) && "zh".equals(params.getExamTopicType())) {
            examUserRankLambdaQueryWrapper.eq(DcExamUserRank::getExamTopicType, "zh");
        } else {
            examUserRankLambdaQueryWrapper.and(wrapper ->
                    wrapper.eq(DcExamUserRank::getExamTopicType, "base")
                            .or()
                            .isNull(DcExamUserRank::getExamTopicType)
            );
        }
        List<DcExamUserRank> examUserRanks = dcExamUserRankMapper.selectList(examUserRankLambdaQueryWrapper);
        Collection<DcExamUserRank> filteredRanks = examUserRanks.stream()
                .collect(Collectors.groupingBy(DcExamUserRank::getUserId)) // 按 userId 分组
                .values().stream()
                .map(group -> group.stream()
                        .max(Comparator.comparing((DcExamUserRank rank) -> Double.parseDouble(rank.getScore()))
                                .thenComparing(rank -> Integer.parseInt(rank.getExamTime()))) // 分数高优先，时间短优先
                        .orElse(null))
                .filter(Objects::nonNull) // 去掉空值
                .collect(Collectors.toList());

        // 按分数倒序 + 时间正序排序
        List<DcExamUserRank> sortedRanks = new ArrayList<>(filteredRanks);

        // 为 rank 字段赋值
        for (int i = 0; i < sortedRanks.size(); i++) {
            sortedRanks.get(i).setRank(i + 1); // 排名从 1 开始
        }
        List<DcExamUserRankVO> listVO = sortedRanks.stream()
                .map(dcExamUserRank -> JSONObject.parseObject(JSONObject.toJSONString(dcExamUserRank), DcExamUserRankVO.class))
                .collect(Collectors.toList());
        Set<Integer> userIds = listVO.stream().map(DcExamUserRankVO::getUserId).collect(Collectors.toSet());
        Map<Integer, User> idUserNameMap = new HashMap<>();
        if (userIds.size() > 0) {
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            wrapper.in("id", userIds);
            List<User> users = userMapper.selectList(wrapper);
            idUserNameMap = users.stream()
                    .collect(Collectors.toMap(User::getId, v -> v));
        }
        TemplateCurrentUser currentUser = TemplateSessionUtils.getCurrentUser();
        for (DcExamUserRankVO dcExamUserRankVO : listVO) {
            User user = idUserNameMap.get(dcExamUserRankVO.getUserId());
            if (user != null) {
                dcExamUserRankVO.setUserName(user.getUserName());
                dcExamUserRankVO.setUserAvatar(user.getAvatar());
            }
            if (currentUser != null && Objects.equals(dcExamUserRankVO.getUserId(), currentUser.getId())) {
                dcExamUserRankVO.setMyFlag(true);
            }
        }
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String redisKey = "TEST_DATA:" + ("教员".equals(params.getExamType()) ? "YY:" : "NYY:") + currentDate.format(formatter);
        JSONArray testArray = new JSONArray();
        if (RedisUtils.get(redisKey) != null) {
            String testData = (String) RedisUtils.get(redisKey);
            testArray = JSONArray.parseArray(testData);
        }
        if (testArray.size() == 0) {
            testArray = generateExamData(params.getExamType(), "教员".equals(params.getExamType()) ? generateRandomIntInRange(2, 5)
                    : generateRandomIntInRange(10, 35));
            RedisUtils.set(redisKey, testArray.toJSONString(), 2, TimeUnit.DAYS);
        }
        List<DcExamUserRankVO> testListVO = JSONObject.parseArray(testArray.toJSONString(), DcExamUserRankVO.class);
        // 非综合练习再添加测试数据
        if (StringUtils.isValid(params.getExamTopicType()) && "zh".equals(params.getExamTopicType())) {
        } else {
            listVO.addAll(testListVO);
        }
        listVO.sort(Comparator.comparing((DcExamUserRank rank) -> Double.parseDouble(rank.getScore())).reversed()
                .thenComparing(rank -> Integer.parseInt(rank.getExamTime())));
        for (int i = 0; i < listVO.size(); i++) {
            listVO.get(i).setRank(i + 1);
            listVO.get(i).setExamTime(DateUtils.convertTime(listVO.get(i).getExamTime()));
        }
        // 只保留前30
        return new ListData<>(listVO.size() > 30 ? listVO.subList(0, 30) : listVO, 20L, params.getPageNo(), params.getPageSize());
    }

    public static int generateRandomIntInRange(int min, int max) {
        Random random = new Random();
        if (min > max) {
            throw new IllegalArgumentException("min should be less than or equal to max");
        }
        return random.nextInt(max - min + 1) + min;  // 包含min和max
    }

    public static String generateRandomPhoneNumber() {
        Random random = new Random();
        // 以 1 开头，第二位是 3~9 中的一个数字
        StringBuilder phoneNumber = new StringBuilder("1");
        String[] secondDigitOptions = {"3", "4", "5", "6", "7", "8", "9"};
        phoneNumber.append(secondDigitOptions[random.nextInt(secondDigitOptions.length)]);

        // 后面 9 位数字随机生成
        for (int i = 0; i < 9; i++) {
            phoneNumber.append(random.nextInt(10)); // 随机数字 0-9
        }

        return phoneNumber.toString();
    }

    public static JSONArray generateExamData(String examType, int dataCount) {
        JSONArray dataArray = new JSONArray();
        Random random = new Random();

        for (int i = 0; i < dataCount; i++) {
            JSONObject dataObject = new JSONObject();
            dataObject.put("id", i + 1);
            dataObject.put("chapterType", null);
            dataObject.put("examType", examType);
            dataObject.put("userId", -1);
            dataObject.put("userName", generateRandomPhoneNumber()); // Random userName
            dataObject.put("userAvatar", null);
            dataObject.put("score", 40 + random.nextInt(51)); // Random score between 40 and 90
            dataObject.put("rank", 5); // Assuming fixed rank 5
            int examTimeInSeconds = 10 * 60 + random.nextInt(41 * 60); // Random exam time between 10-50 minutes
            dataObject.put("examTime", examTimeInSeconds);
            dataObject.put("isDelete", "0");
            dataObject.put("myFlag", false);

            dataArray.add(dataObject);
        }

        return dataArray;
    }

    @Override
    public DcExamUserRankVO getDcExamUserRankById(Integer id) {
        DcExamUserRank dcExamUserRank = dcExamUserRankMapper.selectById(id);
        if (dcExamUserRank == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        return JSONObject.parseObject(JSONObject.toJSONString(dcExamUserRank), DcExamUserRankVO.class);
    }


    @Override
    public DcExamUserRank createDcExamUserRank(DcExamUserRank data) {
        boolean rs = dcExamUserRankMapper.insert(data) > 0;
        log.info("创建 DcExamUserRank: {}", rs ? "成功" : "失败");
        return data;
    }

    @Override
    public boolean updateDcExamUserRank(DcExamUserRank data) {
        boolean rs = dcExamUserRankMapper.updateById(data) > 0;
        log.info("更新 DcExamUserRank: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public boolean deleteDcExamUserRankById(Integer id) {
        boolean rs = dcExamUserRankMapper.deleteById(id) > 0;
        log.info("删除 DcExamUserRank: {}", rs ? "成功" : "失败");
        return rs;
    }
}
