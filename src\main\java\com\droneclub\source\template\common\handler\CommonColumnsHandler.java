package com.droneclub.source.template.common.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.droneclub.source.template.common.config.SystemConfig;
import com.droneclub.source.template.common.model.TemplateCurrentUser;
import com.droneclub.source.template.common.utils.TemplateSessionUtils;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.reflection.MetaObject;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

@Component
@RequiredArgsConstructor
public class CommonColumnsHandler implements MetaObjectHandler {

    private final SystemConfig systemConfig;
    private final HttpServletRequest request;
    @Value("${server.servlet.context-path}")
    private String apiPrefix;

    @Override
    public void insertFill(MetaObject metaObject) {
        TemplateCurrentUser currentUser = getCurrentUser();
        if (!request.getRequestURI().endsWith("login") && currentUser != null) {
            this.strictInsertFill(metaObject, "createUser", Integer.class, Objects.requireNonNull(currentUser).getId());
            this.strictInsertFill(metaObject, "updateUser", Integer.class, currentUser.getId());
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        TemplateCurrentUser currentUser = getCurrentUser();
        if (!request.getRequestURI().endsWith("login") && currentUser != null) {
            this.strictUpdateFill(metaObject, "updateUser", Integer.class, Objects.requireNonNull(getCurrentUser()).getId());
        }
    }

    @Nullable
    private TemplateCurrentUser getCurrentUser() {
        TemplateCurrentUser currentUser = TemplateSessionUtils.getCurrentUser();
        if (currentUser == null) {
            RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                String requestURI = request.getRequestURI().replaceFirst(apiPrefix, "");
                List<String> whiteList = systemConfig.getWhiteList();
                if (whiteList.contains(requestURI)) {
                    currentUser = new TemplateCurrentUser();
                    currentUser.setId(-1);
                }
            } else {
                currentUser = new TemplateCurrentUser();
                currentUser.setId(-1);
            }
        }
        return currentUser;
    }
}
