﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,k)),m,[],n,_(h,o),p,[q],r,_(s,t,u,v,g,w,x,_(),y,[],z,_(A,B,C,D,E,_(F,G,H,I),J,null,K,L,L,M,N,O,null,P,Q,R,S,T,U,V,Q,W,X,_(F,G,H,Y),Z,Q,ba,bb,_(bc,bd,be,bf,bg,bf,bh,bf,bi,k,H,_(bj,bk,bl,bk,bm,bk,bn,bo)),i,_(j,k,l,k)),bp,_(),bq,_(),br,_(bs,[_(bt,bu,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,bC,l,bD),A,bE,bF,_(bG,bH,bI,bJ),E,_(F,G,H,bK)),bp,_(),bL,_(),bM,bd),_(bt,bN,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,bC,l,bO),A,bE,bF,_(bG,bH,bI,bJ)),bp,_(),bL,_(),bM,bd),_(bt,bP,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,bQ,l,bR),A,bS,bF,_(bG,bT,bI,bU)),bp,_(),bL,_(),bM,bd),_(bt,bV,bv,h,bw,bW,u,bX,bz,bX,bA,bB,z,_(A,bY,i,_(j,bZ,l,bZ),bF,_(bG,ca,bI,cb),J,null),bp,_(),bL,_(),cc,_(cd,ce)),_(bt,cf,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cg,l,ch),A,ci,bF,_(bG,ca,bI,cj),Z,ck),bp,_(),bL,_(),bM,bd),_(bt,cl,bv,h,bw,cm,u,by,bz,cn,bA,bB,z,_(i,_(j,co,l,bR),A,cp,bF,_(bG,cq,bI,cr),V,cs),bp,_(),bL,_(),cc,_(cd,ct),bM,bd),_(bt,cu,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cv,l,bR),A,bS,bF,_(bG,cw,bI,cr)),bp,_(),bL,_(),bM,bd),_(bt,cx,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cv,l,bR),A,bS,bF,_(bG,cy,bI,cz)),bp,_(),bL,_(),bM,bd),_(bt,cA,bv,h,bw,cB,u,by,bz,cC,bA,bB,z,_(i,_(j,cD,l,cE),A,cp,bF,_(bG,cq,bI,cF)),bp,_(),bL,_(),cc,_(cd,cG),bM,bd),_(bt,cH,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cv,l,bR),A,bS,bF,_(bG,cI,bI,cz)),bp,_(),bL,_(),bM,bd),_(bt,cJ,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cK,l,bR),A,bS,bF,_(bG,cy,bI,cL)),bp,_(),bL,_(),bM,bd),_(bt,cM,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cN,l,bR),A,bS,bF,_(bG,cO,bI,cL)),bp,_(),bL,_(),bM,bd),_(bt,cP,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cg,l,cQ),A,ci,bF,_(bG,ca,bI,cR),Z,ck),bp,_(),bL,_(),bM,bd),_(bt,cS,bv,h,bw,cm,u,by,bz,cn,bA,bB,z,_(i,_(j,co,l,bR),A,cp,bF,_(bG,cq,bI,cT),V,cs),bp,_(),bL,_(),cc,_(cd,ct),bM,bd),_(bt,cU,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cv,l,bR),A,bS,bF,_(bG,cw,bI,cT)),bp,_(),bL,_(),bM,bd),_(bt,cV,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cv,l,bR),A,bS,bF,_(bG,cW,bI,cX)),bp,_(),bL,_(),bM,bd),_(bt,cY,bv,h,bw,cB,u,by,bz,cC,bA,bB,z,_(i,_(j,cD,l,cE),A,cp,bF,_(bG,cy,bI,cZ)),bp,_(),bL,_(),cc,_(cd,cG),bM,bd),_(bt,da,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cv,l,bR),A,bS,bF,_(bG,cI,bI,cX)),bp,_(),bL,_(),bM,bd),_(bt,db,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cv,l,bR),A,bS,bF,_(bG,cW,bI,dc)),bp,_(),bL,_(),bM,bd),_(bt,dd,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,ch,l,bR),A,bS,bF,_(bG,cW,bI,de)),bp,_(),bL,_(),bM,bd),_(bt,df,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(A,dg,i,_(j,dh,l,bR),di,dj,bF,_(bG,cW,bI,dk)),bp,_(),bL,_(),bM,bd),_(bt,dl,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,dm,l,dn),A,dp,bF,_(bG,dq,bI,dr),di,ds),bp,_(),bL,_(),bM,bd),_(bt,dt,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,cv,l,bR),A,bS,bF,_(bG,cW,bI,du)),bp,_(),bL,_(),bM,bd),_(bt,dv,bv,h,bw,cB,u,by,bz,cC,bA,bB,z,_(i,_(j,cD,l,cE),A,cp,bF,_(bG,cy,bI,dw)),bp,_(),bL,_(),cc,_(cd,cG),bM,bd),_(bt,dx,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,dy,l,bR),A,bS,bF,_(bG,dz,bI,du)),bp,_(),bL,_(),bM,bd),_(bt,dA,bv,h,bw,bx,u,by,bz,by,bA,bB,z,_(i,_(j,dB,l,dy),A,dC,bF,_(bG,dD,bI,dE),Z,dF),bp,_(),bL,_(),bM,bd)])),dG,_(),dH,_(dI,_(dJ,dK),dL,_(dJ,dM),dN,_(dJ,dO),dP,_(dJ,dQ),dR,_(dJ,dS),dT,_(dJ,dU),dV,_(dJ,dW),dX,_(dJ,dY),dZ,_(dJ,ea),eb,_(dJ,ec),ed,_(dJ,ee),ef,_(dJ,eg),eh,_(dJ,ei),ej,_(dJ,ek),el,_(dJ,em),en,_(dJ,eo),ep,_(dJ,eq),er,_(dJ,es),et,_(dJ,eu),ev,_(dJ,ew),ex,_(dJ,ey),ez,_(dJ,eA),eB,_(dJ,eC),eD,_(dJ,eE),eF,_(dJ,eG),eH,_(dJ,eI)));}; 
var b="url",c="未完成作业详情.html",d="generationDate",e=new Date(1750408319446.74),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=0,l="height",m="adaptiveViews",n="sketchKeys",o="s0",p="variables",q="OnLoadVariable",r="page",s="packageId",t="8eb826defb6c40f0aa070c05b79ac01a",u="type",v="Axure:Page",w="未完成作业详情",x="notes",y="annotations",z="style",A="baseStyle",B="627587b6038d43cca051c114ac41ad32",C="pageAlignment",D="center",E="fill",F="fillType",G="solid",H="color",I=0xFFFFFFFF,J="image",K="imageAlignment",L="near",M="imageRepeat",N="auto",O="favicon",P="sketchFactor",Q="0",R="colorStyle",S="appliedColor",T="fontName",U="Applied Font",V="borderWidth",W="borderVisibility",X="borderFill",Y=0xFF797979,Z="cornerRadius",ba="cornerVisibility",bb="outerShadow",bc="on",bd=false,be="offsetX",bf=5,bg="offsetY",bh="blurRadius",bi="spread",bj="r",bk=0,bl="g",bm="b",bn="a",bo=0.349019607843137,bp="adaptiveStyles",bq="interactionMap",br="diagram",bs="objects",bt="id",bu="6d33a37aa7584508a182841e0737ade9",bv="label",bw="friendlyType",bx="矩形",by="vectorShape",bz="styleType",bA="visible",bB=true,bC=375,bD=758,bE="60d87ff5e0934fb5a735f21d2a268c7d",bF="location",bG="x",bH=32,bI="y",bJ=25,bK=0xFFF5F5F5,bL="imageOverrides",bM="generateCompound",bN="b7d0d53b60d5488691b4829bb2488fef",bO=45,bP="5a149bbfcf974a6080d40b4cf2e83625",bQ=84,bR=16,bS="f8c70a63ec8c4ded9e1c2963c0e658a5",bT=189,bU=40,bV="0d4bcf09f60e4d299fb0be83a41ab633",bW="图片 ",bX="imageBox",bY="********************************",bZ=20,ca=44,cb=38,cc="images",cd="normal~",ce="images/作业布置/u38.png",cf="0b40a6c0c67b4d29acb6067cc70b72f0",cg=351,ch=116,ci="93950f64c5104d7fbe432f744db64e34",cj=78,ck="15",cl="5fdaf4be14534839a28f29eda7b7cb75",cm="垂直线",cn="verticalLine",co=4,cp="1df8bc12869c446989a07f36813b37ee",cq=54,cr=95,cs="4",ct="images/创建作业/u147.svg",cu="a5710c19ab3343248ccc9c9a61bcf3f8",cv=56,cw=65,cx="9f633bf727ce4b75843767dbc539ba4f",cy=59,cz=133,cA="4b05b6da9a944a6a831b378927ffc0a3",cB="线段",cC="horizontalLine",cD=331,cE=1,cF=158,cG="images/创建作业/u150.svg",cH="7c31de5b2bdc48e192ec33cb6d9f29b2",cI=319,cJ="6510ae5a95e24b81bcb0444d24ee5456",cK=98,cL=164,cM="531b2ea430cb41e59a2e79dacc0ed64f",cN=72,cO=303,cP="70f043d4a8384f83a11414ef0c7a529f",cQ=282,cR=204,cS="bb619695d708410b958cdca64292aaf2",cT=221,cU="38cde557867e4c54abf0dd4dc5e43ceb",cV="079b75e80a5c430e966afedf3e8ef225",cW=64,cX=246,cY="49381490766c4a6a89975f8df30f71b9",cZ=271,da="dfacffdfe333471c9cbfc6886297a4d5",db="a2570dc0bfcf41c99df7859dfa78d7af",dc=320,dd="9c2aedfde7a74f64a1441e0e0ed85962",de=352,df="cda68ce216ec4c24a4b5a26fc50f79b7",dg="4988d43d80b44008a4a415096f1632af",dh=130,di="fontSize",dj="14px",dk=384,dl="8acd7eb941a64026a4cb4d0edec0594f",dm=992,dn=231,dp="31e8887730cc439f871dc77ac74c53b6",dq=465,dr=48,ds="16px",dt="e5f636f2f992452eacf8ef8ec43078b5",du=284,dv="51e7f98c8efc40feab3fd1340ff4290e",dw=309,dx="fb500acf717848bc9aa0e45fdd0c42e9",dy=42,dz=333,dA="458ecd5bfabe49d184350fb15361f3b8",dB=286,dC="c26e509c01924380b00a973a82019677",dD=77,dE=696,dF="70",dG="masters",dH="objectPaths",dI="6d33a37aa7584508a182841e0737ade9",dJ="scriptId",dK="u217",dL="b7d0d53b60d5488691b4829bb2488fef",dM="u218",dN="5a149bbfcf974a6080d40b4cf2e83625",dO="u219",dP="0d4bcf09f60e4d299fb0be83a41ab633",dQ="u220",dR="0b40a6c0c67b4d29acb6067cc70b72f0",dS="u221",dT="5fdaf4be14534839a28f29eda7b7cb75",dU="u222",dV="a5710c19ab3343248ccc9c9a61bcf3f8",dW="u223",dX="9f633bf727ce4b75843767dbc539ba4f",dY="u224",dZ="4b05b6da9a944a6a831b378927ffc0a3",ea="u225",eb="7c31de5b2bdc48e192ec33cb6d9f29b2",ec="u226",ed="6510ae5a95e24b81bcb0444d24ee5456",ee="u227",ef="531b2ea430cb41e59a2e79dacc0ed64f",eg="u228",eh="70f043d4a8384f83a11414ef0c7a529f",ei="u229",ej="bb619695d708410b958cdca64292aaf2",ek="u230",el="38cde557867e4c54abf0dd4dc5e43ceb",em="u231",en="079b75e80a5c430e966afedf3e8ef225",eo="u232",ep="49381490766c4a6a89975f8df30f71b9",eq="u233",er="dfacffdfe333471c9cbfc6886297a4d5",es="u234",et="a2570dc0bfcf41c99df7859dfa78d7af",eu="u235",ev="9c2aedfde7a74f64a1441e0e0ed85962",ew="u236",ex="cda68ce216ec4c24a4b5a26fc50f79b7",ey="u237",ez="8acd7eb941a64026a4cb4d0edec0594f",eA="u238",eB="e5f636f2f992452eacf8ef8ec43078b5",eC="u239",eD="51e7f98c8efc40feab3fd1340ff4290e",eE="u240",eF="fb500acf717848bc9aa0e45fdd0c42e9",eG="u241",eH="458ecd5bfabe49d184350fb15361f3b8",eI="u242";
return _creator();
})());