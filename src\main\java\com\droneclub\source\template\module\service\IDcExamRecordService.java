package com.droneclub.source.template.module.service;

import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcExamRecord;
import com.droneclub.source.template.module.pojo.DcExamRecordVO;
import com.droneclub.source.template.module.pojo.DcExamRecordListSearch;

public interface IDcExamRecordService {
    
    ListData<DcExamRecordVO> getDcExamRecordList(DcExamRecordListSearch params);

    DcExamRecordVO getDcExamRecordById(Integer id);

    DcExamRecord createDcExamRecord(DcExamRecord data);

    DcExamRecord updateDcExamRecord(DcExamRecord data);

    boolean deleteDcExamRecordById(Integer id);
}
