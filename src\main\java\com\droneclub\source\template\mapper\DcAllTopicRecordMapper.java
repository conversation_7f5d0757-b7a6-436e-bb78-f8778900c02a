package com.droneclub.source.template.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.droneclub.source.template.entity.DcAllTopicRecord;
import com.droneclub.source.template.module.pojo.AnswerStats;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface DcAllTopicRecordMapper extends BaseMapper<DcAllTopicRecord> {

    @Select("SELECT COUNT(DISTINCT datr.topic_id) \n" +
            "        FROM dc_topic_answer_record datr\n" +
            "        LEFT JOIN dc_topic dt ON dt.id = datr.topic_id\n" +
            "        WHERE dt.topic_status=1 and datr.answer_id = #{userId} " +
            "AND dt.chapter_type = #{chapterType}")
    int getUserDoneTopicNum(Integer userId, String chapterType);


    AnswerStats getAnswerStatsByUserId(@Param("userId") Integer userId, @Param("chapterNames") List<String> chapterNames);
}
