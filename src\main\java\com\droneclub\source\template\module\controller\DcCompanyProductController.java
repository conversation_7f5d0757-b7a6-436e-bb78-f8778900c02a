package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.entity.DcCompanyProduct;
import com.droneclub.source.template.module.pojo.DcCompanyProductListSearch;
import com.droneclub.source.template.module.pojo.DcCompanyProductVO;
import com.droneclub.source.template.module.service.IDcCompanyProductService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dcCompanyProduct")
public class DcCompanyProductController {

    private final IDcCompanyProductService dcCompanyProductService;

    @GetMapping("/getDcCompanyProductList")
    public RestResult<List<DcCompanyProductVO>> getDcCompanyProductList(DcCompanyProductListSearch params) {
        return RestResult.success(dcCompanyProductService.getDcCompanyProductList(params));
    }

    @GetMapping("/getDcCompanyProductById")
    public RestResult<DcCompanyProductVO> getDcCompanyProductById(Integer id) {
        return RestResult.success(dcCompanyProductService.getDcCompanyProductById(id));
    }

    @PostMapping("/createDcCompanyProduct")
    public RestResult<DcCompanyProduct> createDcCompanyProduct(@RequestBody DcCompanyProduct data) {
        return RestResult.success(dcCompanyProductService.createDcCompanyProduct(data));
    }

    @PutMapping("/updateDcCompanyProduct")
    public RestResult<Boolean> updateDcCompanyProduct(@RequestBody DcCompanyProduct data) {
        return RestResult.success(dcCompanyProductService.updateDcCompanyProduct(data));
    }

    @DeleteMapping("/deleteDcCompanyProductById")
    public RestResult<Boolean> deleteDcCompanyProductById(Integer id) {
        return RestResult.success(dcCompanyProductService.deleteDcCompanyProductById(id));
    }
}
