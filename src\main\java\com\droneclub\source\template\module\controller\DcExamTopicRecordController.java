package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcExamTopicRecord;
import com.droneclub.source.template.module.pojo.DcExamTopicRecordVO;
import com.droneclub.source.template.module.pojo.DcExamTopicRecordListSearch;
import com.droneclub.source.template.module.service.IDcExamTopicRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dcExamTopicRecord")
public class DcExamTopicRecordController {

    private final IDcExamTopicRecordService dcExamTopicRecordService;

    @GetMapping("/getDcExamTopicRecordList")
    public RestResult<ListData<DcExamTopicRecordVO>> getDcExamTopicRecordList(DcExamTopicRecordListSearch params) {
        return RestResult.success(dcExamTopicRecordService.getDcExamTopicRecordList(params));
    }

    @GetMapping("/getDcExamTopicRecordById")
    public RestResult<DcExamTopicRecordVO> getDcExamTopicRecordById(Integer id) {
        return RestResult.success(dcExamTopicRecordService.getDcExamTopicRecordById(id));
    }

    @PostMapping("/createDcExamTopicRecord")
    public RestResult<DcExamTopicRecord> createDcExamTopicRecord(@RequestBody DcExamTopicRecord data) {
        return RestResult.success(dcExamTopicRecordService.createDcExamTopicRecord(data));
    }

    @PutMapping("/updateDcExamTopicRecord")
    public RestResult<Boolean> updateDcExamTopicRecord(@RequestBody DcExamTopicRecord data) {
        return RestResult.success(dcExamTopicRecordService.updateDcExamTopicRecord(data));
    }

    @DeleteMapping("/deleteDcExamTopicRecordById")
    public RestResult<Boolean> deleteDcExamTopicRecordById(Integer id) {
        return RestResult.success(dcExamTopicRecordService.deleteDcExamTopicRecordById(id));
    }
}
