package com.droneclub.source.template.system.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import cn.soulspark.source.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.common.model.TemplateCurrentUser;
import com.droneclub.source.template.common.utils.DateUtils;
import com.droneclub.source.template.common.utils.TemplateSessionUtils;
import com.droneclub.source.template.entity.DcMemberOpen;
import com.droneclub.source.template.entity.User;
import com.droneclub.source.template.mapper.DcMemberOpenMapper;
import com.droneclub.source.template.mapper.TmUserMapper;
import com.droneclub.source.template.system.pojo.OpenMember;
import com.droneclub.source.template.system.pojo.UserListSearch;
import com.droneclub.source.template.system.pojo.UserVO;
import com.droneclub.source.template.system.service.IUserService;
import com.droneclub.source.template.wechat.service.IWechatSecurityAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements IUserService {

    private final TmUserMapper userMapper;
    private final DcMemberOpenMapper memberOpenMapper;
    private final IWechatSecurityAuditService wechatSecurityAuditService;

    @Override
    public List<User> getUserOption(UserListSearch params) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(User::getId, User::getUserName);
        return userMapper.selectList(queryWrapper);
    }

    @Override
    public ListData<UserVO> getUserList(UserListSearch params) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        // 查询总数
        Long total = userMapper.selectCount(queryWrapper);

        // 分页查询
        Page<User> page = new Page<>(params.getPageNo(), params.getPageSize());
        IPage<User> userPage = userMapper.selectPage(page, queryWrapper);
        List<User> list = userPage.getRecords();
        List<UserVO> listVO = list.stream()
                .map(user -> JSONObject.parseObject(JSONObject.toJSONString(user), UserVO.class))
                .collect(Collectors.toList());
        return new ListData<>(listVO, total, params.getPageNo(), params.getPageSize());
    }

    @Override
    public UserVO getUserById(String id) {
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        return JSONObject.parseObject(JSONObject.toJSONString(user), UserVO.class);
    }


    @Override
    public User createUser(User data) {
        if (StringUtils.isValid(data.getOpenId()) && StringUtils.isValid(data.getUserName())) {
            wechatSecurityAuditService.contentAudit(data.getOpenId(), data.getUserName(), data.getUserName());
        }
        /*if (StringUtils.isValid(data.getAvatar())) {
            wechatSecurityAuditService.imageAudit(data.getOpenId(), data.getAvatar());
        }*/
        boolean rs = userMapper.insert(data) > 0;
        log.info("创建 User: {}", rs ? "成功" : "失败");
        return data;
    }

    @Override
    public boolean updateUser(User data) {
        if (StringUtils.isValid(data.getOpenId()) && StringUtils.isValid(data.getUserName())) {
            wechatSecurityAuditService.contentAudit(data.getOpenId(), data.getUserName(), data.getUserName());
        }


        boolean rs = userMapper.updateById(data) > 0;
        log.info("更新 User: {}", rs ? "成功" : "失败");/*if (StringUtils.isValid(data.getOpenId()) && StringUtils.isValid(data.getAvatar())) {
            wechatSecurityAuditService.imageAudit(data.getOpenId(), data.getAvatar());
        }*/
        // 修改成功剔除用户会话
        /*if (rs) {
            authService.logout(data.getId());
        }*/
        return rs;
    }

    @Override
    public boolean deleteUserById(String id) {
        boolean rs = userMapper.deleteById(id) > 0;
        log.info("删除 User: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public User getUserByAuthId(Integer authId) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("auth_id", authId);
        return userMapper.selectOne(queryWrapper);
    }

    @Override
    public Boolean openMember(OpenMember openMember) {
        TemplateCurrentUser currentUser = TemplateSessionUtils.getCurrentUser();
        LambdaQueryWrapper<DcMemberOpen> queryWrapper = new LambdaQueryWrapper<>();
        if (openMember.getCompanyId() != null) {
            queryWrapper.eq(DcMemberOpen::getCompanyId, openMember.getCompanyId());
        } else {
            queryWrapper.eq(DcMemberOpen::getUserId, currentUser.getId());
        }
        queryWrapper.eq(DcMemberOpen::getStatus, 1);
        // 存在匹配类型的有效开通记录则再加一个月有效期
        int addDays = 0;
        if ("month".equals(openMember.getMemberType()) || "month_gj".equals(openMember.getMemberType())) {
            addDays = 30;
        } else if ("quarter".equals(openMember.getMemberType()) || "quarter_gj".equals(openMember.getMemberType())) {
            addDays = 91;
        } else if ("year".equals(openMember.getMemberType()) || "year_gj".equals(openMember.getMemberType())) {
            addDays = 365;
        }
        queryWrapper.eq(DcMemberOpen::getMemberType, openMember.getMemberType());
        DcMemberOpen memberOpen = memberOpenMapper.selectOne(queryWrapper);
        if (memberOpen != null) {
            // 延长一个月有效期
            memberOpen.setEndTime(DateUtils.getNextDay(memberOpen.getEndTime(), addDays));
            memberOpen.setMemberType(openMember.getMemberType());
            memberOpenMapper.updateById(memberOpen);
            return true;
        } else {
            memberOpen = new DcMemberOpen();
            if (openMember.getCompanyId() != null) {
                memberOpen.setCompanyId(openMember.getCompanyId());
            } else {
                memberOpen.setUserId(currentUser.getId());
            }
            memberOpen.setMemberType(openMember.getMemberType());
            memberOpen.setStartTime(DateUtils.getNowDateTime());
            memberOpen.setEndTime(DateUtils.getNextDay(addDays));
            memberOpen.setStatus(1);
            return memberOpenMapper.insert(memberOpen) > 0;
        }
    }
}
