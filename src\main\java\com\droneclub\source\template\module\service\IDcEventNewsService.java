package com.droneclub.source.template.module.service;

import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcEventNews;
import com.droneclub.source.template.module.pojo.DcEventNewsListSearch;
import com.droneclub.source.template.module.pojo.DcEventNewsVO;

public interface IDcEventNewsService {

    ListData<DcEventNewsVO> getDcEventNewsList(DcEventNewsListSearch params);

    DcEventNewsVO getDcEventNewsById(Integer id);

    DcEventNews createDcEventNews(DcEventNews data);

    boolean updateDcEventNews(DcEventNews data);

    boolean deleteDcEventNewsById(Integer id);
}
