package com.droneclub.source.template.single.region.controller;

import com.droneclub.source.template.single.region.pojo.RegionTreeVO;
import com.droneclub.source.template.single.region.pojo.SysCityVO;
import com.droneclub.source.template.single.region.pojo.SysDistrictVO;
import com.droneclub.source.template.single.region.pojo.SysProvinceVO;
import com.droneclub.source.template.single.region.service.ISysRegionService;
import com.keelcloud.sdk.javabase.model.RestResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 区域控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/region")
public class RegionController {

    private final ISysRegionService sysRegionService;

    /**
     * 同步高德地图省市区数据
     *
     * @return 同步结果
     */
    @PostMapping("/sync")
    public RestResult<Boolean> syncRegionData() {
        log.info("开始同步省市区数据");
        boolean result = sysRegionService.syncRegionData();
        return result ? RestResult.success() : RestResult.failure("同步省市区数据失败");
    }

    /**
     * 查询省份列表
     *
     * @return 省份列表
     */
    @GetMapping("/provinces/list")
    public RestResult<List<SysProvinceVO>> listProvinces() {
        return RestResult.success(sysRegionService.listProvinces());
    }

    /**
     * 根据省份编码获取所有城市
     *
     * @param provinceCode 省份编码
     * @return 城市列表
     */
    @GetMapping("/cities/byProvince/{provinceCode}")
    public RestResult<List<SysCityVO>> listCitiesByProvinceCode(@PathVariable("provinceCode") String provinceCode) {
        return RestResult.success(sysRegionService.listCitiesByProvinceCode(provinceCode));
    }

    /**
     * 获取首字母分组城市列表
     *
     * @return 城市列表
     */
    @GetMapping("/firstCharCities")
    public RestResult<Map<String, List<SysCityVO>>> firstCharCities() {
        return RestResult.success(sysRegionService.firstCharCities());
    }

    /**
     * 获取热门城市列表
     *
     * @return 城市列表
     */
    @GetMapping("/hotCities")
    public RestResult<List<SysCityVO>> hotCities() {
        return RestResult.success(sysRegionService.hotCities());
    }

    /**
     * 根据城市编码获取所有区县
     *
     * @param cityCode 城市编码
     * @return 区县列表
     */
    @GetMapping("/districts/byCity/{cityCode}")
    public RestResult<List<SysDistrictVO>> listDistrictsByCityCode(@PathVariable("cityCode") String cityCode) {
        return RestResult.success(sysRegionService.listDistrictsByCityCode(cityCode));
    }

    /**
     * 获取完整地区信息（三级分类结构）
     * 所有数据按照首字母排序
     *
     * @return 完整地区信息
     */
    @GetMapping("/completeTree")
    public RestResult<List<RegionTreeVO>> getCompleteRegionTree() {
        return RestResult.success(sysRegionService.getCompleteRegionTree());
    }
} 