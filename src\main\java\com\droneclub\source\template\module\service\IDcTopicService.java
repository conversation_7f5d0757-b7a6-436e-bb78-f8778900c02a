package com.droneclub.source.template.module.service;

import com.alibaba.fastjson.JSONObject;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcTopic;
import com.droneclub.source.template.module.pojo.*;

import java.util.List;

public interface IDcTopicService {

    ListData<DcTopicVO> getDcTopicList(DcTopicListSearch params);

    ListData<DcTopicVO> getKsDcTopicList(DcTopicListSearch params);


    DcTopicVO getDcTopicById(Integer id);

    DcTopic createDcTopic(DcTopic data);

    boolean updateDcTopic(DcTopic data);

    boolean deleteDcTopicById(Integer id);

    Boolean updateDcStatus(JSONObject data);

    List<TopicChapterItem> getTopicChapterList(String chapterType);

    List<TopicChapterItem> getKsTopicChapterList(String chapterType);

    TopicIndexVO getTopicIndex(String chapterType);

    Boolean dealAiTopicAnalysis();

    Boolean dealAiTopicDetailAnalysis();

    DcTopic getTopicAiAnalysis(int topicId);

    List<QuestionBreakdown> getQuestionBreakdown(String chapterName);

    boolean checkAIAnswer();

    YCTopic getDcYcTopicList();
}
