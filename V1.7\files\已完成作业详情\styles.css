﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-32px;
  width:1221px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u243_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:758px;
  background:inherit;
  background-color:rgba(245, 245, 245, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u243 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:25px;
  width:375px;
  height:758px;
  display:flex;
}
#u243 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u243_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u244_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:45px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u244 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:25px;
  width:375px;
  height:45px;
  display:flex;
}
#u244 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u244_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u245_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u245 {
  border-width:0px;
  position:absolute;
  left:189px;
  top:40px;
  width:84px;
  height:16px;
  display:flex;
}
#u245 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u245_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u246_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u246 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:38px;
  width:20px;
  height:20px;
  display:flex;
}
#u246 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u246_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u247_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:116px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u247 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:78px;
  width:351px;
  height:116px;
  display:flex;
}
#u247 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u247_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u248_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:9px;
  height:21px;
}
#u248 {
  border-width:0px;
  position:absolute;
  left:54px;
  top:95px;
  width:4px;
  height:16px;
  display:flex;
}
#u248 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u248_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u249_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u249 {
  border-width:0px;
  position:absolute;
  left:65px;
  top:95px;
  width:56px;
  height:16px;
  display:flex;
}
#u249 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u249_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u250_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u250 {
  border-width:0px;
  position:absolute;
  left:59px;
  top:133px;
  width:56px;
  height:16px;
  display:flex;
}
#u250 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u250_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u251_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:332px;
  height:2px;
}
#u251 {
  border-width:0px;
  position:absolute;
  left:54px;
  top:158px;
  width:331px;
  height:1px;
  display:flex;
}
#u251 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u251_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u252_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u252 {
  border-width:0px;
  position:absolute;
  left:319px;
  top:133px;
  width:56px;
  height:16px;
  display:flex;
}
#u252 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u252_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u253_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u253 {
  border-width:0px;
  position:absolute;
  left:59px;
  top:164px;
  width:98px;
  height:16px;
  display:flex;
}
#u253 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u253_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u254_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u254 {
  border-width:0px;
  position:absolute;
  left:303px;
  top:164px;
  width:72px;
  height:16px;
  display:flex;
}
#u254 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u254_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u255_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:192px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u255 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:204px;
  width:351px;
  height:192px;
  display:flex;
}
#u255 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u255_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u256_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:9px;
  height:21px;
}
#u256 {
  border-width:0px;
  position:absolute;
  left:54px;
  top:221px;
  width:4px;
  height:16px;
  display:flex;
}
#u256 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u256_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u257_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u257 {
  border-width:0px;
  position:absolute;
  left:65px;
  top:221px;
  width:56px;
  height:16px;
  display:flex;
}
#u257 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u257_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u258_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u258 {
  border-width:0px;
  position:absolute;
  left:64px;
  top:246px;
  width:56px;
  height:16px;
  display:flex;
}
#u258 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u258_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u259_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:332px;
  height:2px;
}
#u259 {
  border-width:0px;
  position:absolute;
  left:59px;
  top:271px;
  width:331px;
  height:1px;
  display:flex;
}
#u259 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u259_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u260_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u260 {
  border-width:0px;
  position:absolute;
  left:319px;
  top:246px;
  width:56px;
  height:16px;
  display:flex;
}
#u260 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u260_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u261_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u261 {
  border-width:0px;
  position:absolute;
  left:64px;
  top:280px;
  width:56px;
  height:16px;
  display:flex;
}
#u261 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u261_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u262_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u262 {
  border-width:0px;
  position:absolute;
  left:64px;
  top:312px;
  width:116px;
  height:16px;
  display:flex;
}
#u262 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u262_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u263_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u263 {
  border-width:0px;
  position:absolute;
  left:273px;
  top:312px;
  width:94px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u263 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u263_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u264_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u264 {
  border-width:0px;
  position:absolute;
  left:64px;
  top:346px;
  width:116px;
  height:16px;
  display:flex;
}
#u264 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u264_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u265_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u265 {
  border-width:0px;
  position:absolute;
  left:273px;
  top:346px;
  width:102px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u265 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u265_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u266_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:533px;
  height:231px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-size:16px;
}
#u266 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:810px;
  width:533px;
  height:231px;
  display:flex;
  font-size:16px;
}
#u266 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u266_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u267_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:758px;
  background:inherit;
  background-color:rgba(245, 245, 245, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u267 {
  border-width:0px;
  position:absolute;
  left:457px;
  top:25px;
  width:375px;
  height:758px;
  display:flex;
}
#u267 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u267_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u268_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:45px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u268 {
  border-width:0px;
  position:absolute;
  left:457px;
  top:25px;
  width:375px;
  height:45px;
  display:flex;
}
#u268 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u268_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u269_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u269 {
  border-width:0px;
  position:absolute;
  left:614px;
  top:40px;
  width:84px;
  height:16px;
  display:flex;
}
#u269 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u269_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u270_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u270 {
  border-width:0px;
  position:absolute;
  left:469px;
  top:38px;
  width:20px;
  height:20px;
  display:flex;
}
#u270 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u270_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u271_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:116px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u271 {
  border-width:0px;
  position:absolute;
  left:469px;
  top:78px;
  width:351px;
  height:116px;
  display:flex;
}
#u271 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u271_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u272_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:9px;
  height:21px;
}
#u272 {
  border-width:0px;
  position:absolute;
  left:479px;
  top:95px;
  width:4px;
  height:16px;
  display:flex;
}
#u272 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u272_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u273_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u273 {
  border-width:0px;
  position:absolute;
  left:490px;
  top:95px;
  width:56px;
  height:16px;
  display:flex;
}
#u273 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u273_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u274_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u274 {
  border-width:0px;
  position:absolute;
  left:484px;
  top:133px;
  width:56px;
  height:16px;
  display:flex;
}
#u274 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u274_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u275_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:332px;
  height:2px;
}
#u275 {
  border-width:0px;
  position:absolute;
  left:479px;
  top:158px;
  width:331px;
  height:1px;
  display:flex;
}
#u275 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u275_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u276_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u276 {
  border-width:0px;
  position:absolute;
  left:744px;
  top:133px;
  width:56px;
  height:16px;
  display:flex;
}
#u276 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u276_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u277_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u277 {
  border-width:0px;
  position:absolute;
  left:484px;
  top:164px;
  width:98px;
  height:16px;
  display:flex;
}
#u277 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u277_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u278_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u278 {
  border-width:0px;
  position:absolute;
  left:728px;
  top:164px;
  width:72px;
  height:16px;
  display:flex;
}
#u278 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u278_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u279_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:141px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u279 {
  border-width:0px;
  position:absolute;
  left:469px;
  top:204px;
  width:351px;
  height:141px;
  display:flex;
}
#u279 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u279_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u280_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:9px;
  height:21px;
}
#u280 {
  border-width:0px;
  position:absolute;
  left:479px;
  top:221px;
  width:4px;
  height:16px;
  display:flex;
}
#u280 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u280_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u281_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u281 {
  border-width:0px;
  position:absolute;
  left:490px;
  top:221px;
  width:56px;
  height:16px;
  display:flex;
}
#u281 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u281_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u282_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u282 {
  border-width:0px;
  position:absolute;
  left:489px;
  top:246px;
  width:56px;
  height:16px;
  display:flex;
}
#u282 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u282_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u283_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:332px;
  height:2px;
}
#u283 {
  border-width:0px;
  position:absolute;
  left:484px;
  top:271px;
  width:331px;
  height:1px;
  display:flex;
}
#u283 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u283_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u284_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u284 {
  border-width:0px;
  position:absolute;
  left:744px;
  top:246px;
  width:56px;
  height:16px;
  display:flex;
}
#u284 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u284_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u285_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u285 {
  border-width:0px;
  position:absolute;
  left:489px;
  top:280px;
  width:56px;
  height:16px;
  display:flex;
}
#u285 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u285_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u286_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u286 {
  border-width:0px;
  position:absolute;
  left:758px;
  top:280px;
  width:42px;
  height:16px;
  display:flex;
}
#u286 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u286_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u287_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:332px;
  height:2px;
}
#u287 {
  border-width:0px;
  position:absolute;
  left:484px;
  top:302px;
  width:331px;
  height:1px;
  display:flex;
}
#u287 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u287_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u288_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u288 {
  border-width:0px;
  position:absolute;
  left:489px;
  top:312px;
  width:56px;
  height:16px;
  display:flex;
}
#u288 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u288_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u289_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u289 {
  border-width:0px;
  position:absolute;
  left:762px;
  top:312px;
  width:38px;
  height:16px;
  display:flex;
}
#u289 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u289_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u290_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:758px;
  background:inherit;
  background-color:rgba(245, 245, 245, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u290 {
  border-width:0px;
  position:absolute;
  left:878px;
  top:25px;
  width:375px;
  height:758px;
  display:flex;
}
#u290 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u290_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u291_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:45px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u291 {
  border-width:0px;
  position:absolute;
  left:878px;
  top:25px;
  width:375px;
  height:45px;
  display:flex;
}
#u291 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u291_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u292_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u292 {
  border-width:0px;
  position:absolute;
  left:1035px;
  top:40px;
  width:84px;
  height:16px;
  display:flex;
}
#u292 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u292_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u293_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u293 {
  border-width:0px;
  position:absolute;
  left:890px;
  top:38px;
  width:20px;
  height:20px;
  display:flex;
}
#u293 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u293_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u294_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:116px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u294 {
  border-width:0px;
  position:absolute;
  left:890px;
  top:78px;
  width:351px;
  height:116px;
  display:flex;
}
#u294 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u294_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u295_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:9px;
  height:21px;
}
#u295 {
  border-width:0px;
  position:absolute;
  left:900px;
  top:95px;
  width:4px;
  height:16px;
  display:flex;
}
#u295 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u295_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u296_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u296 {
  border-width:0px;
  position:absolute;
  left:911px;
  top:95px;
  width:56px;
  height:16px;
  display:flex;
}
#u296 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u296_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u297_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u297 {
  border-width:0px;
  position:absolute;
  left:905px;
  top:133px;
  width:56px;
  height:16px;
  display:flex;
}
#u297 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u297_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u298_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:332px;
  height:2px;
}
#u298 {
  border-width:0px;
  position:absolute;
  left:900px;
  top:158px;
  width:331px;
  height:1px;
  display:flex;
}
#u298 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u298_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u299_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u299 {
  border-width:0px;
  position:absolute;
  left:1165px;
  top:133px;
  width:56px;
  height:16px;
  display:flex;
}
#u299 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u299_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u300_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u300 {
  border-width:0px;
  position:absolute;
  left:905px;
  top:164px;
  width:98px;
  height:16px;
  display:flex;
}
#u300 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u300_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u301_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u301 {
  border-width:0px;
  position:absolute;
  left:1149px;
  top:164px;
  width:72px;
  height:16px;
  display:flex;
}
#u301 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u301_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u302_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:109px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u302 {
  border-width:0px;
  position:absolute;
  left:890px;
  top:204px;
  width:351px;
  height:109px;
  display:flex;
}
#u302 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u302_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u303_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:9px;
  height:21px;
}
#u303 {
  border-width:0px;
  position:absolute;
  left:900px;
  top:221px;
  width:4px;
  height:16px;
  display:flex;
}
#u303 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u303_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u304_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u304 {
  border-width:0px;
  position:absolute;
  left:911px;
  top:221px;
  width:56px;
  height:16px;
  display:flex;
}
#u304 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u304_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u305_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u305 {
  border-width:0px;
  position:absolute;
  left:910px;
  top:246px;
  width:56px;
  height:16px;
  display:flex;
}
#u305 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u305_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u306_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:332px;
  height:2px;
}
#u306 {
  border-width:0px;
  position:absolute;
  left:905px;
  top:271px;
  width:331px;
  height:1px;
  display:flex;
}
#u306 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u306_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u307_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u307 {
  border-width:0px;
  position:absolute;
  left:1165px;
  top:246px;
  width:56px;
  height:16px;
  display:flex;
}
#u307 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u307_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u308_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u308 {
  border-width:0px;
  position:absolute;
  left:910px;
  top:283px;
  width:56px;
  height:16px;
  display:flex;
}
#u308 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u308_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u309_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u309 {
  border-width:0px;
  position:absolute;
  left:1191px;
  top:283px;
  width:30px;
  height:16px;
  display:flex;
}
#u309 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u309_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
