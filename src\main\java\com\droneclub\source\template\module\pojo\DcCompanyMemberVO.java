package com.droneclub.source.template.module.pojo;

import com.droneclub.source.template.entity.TmRelUserRole;
import com.droneclub.source.template.entity.User;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class DcCompanyMemberVO {
    
    private Integer id;

    private Integer companyId;

    // private String companyName;

    private Integer userId;

    private String memberName;

    private List<TmRelUserRole> roles;

    private User user;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
} 