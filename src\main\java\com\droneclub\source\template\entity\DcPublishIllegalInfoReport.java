package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("dc_publish_illegal_info_report")
public class DcPublishIllegalInfoReport {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer publishInfoId;
    private String reportMsg;
    private String reportStatus;
    private String auditMsg;
    private String reportType;
    private Integer reportUser;
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    private String createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer updateUser;
    private String updateTime;
    private String isDelete;

}
