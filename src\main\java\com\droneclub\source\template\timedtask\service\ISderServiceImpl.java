package com.droneclub.source.template.timedtask.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.droneclub.source.template.entity.DcTopic;
import com.droneclub.source.template.entity.SfWrjQuestion;
import com.droneclub.source.template.mapper.DcTopicMapper;
import com.droneclub.source.template.mapper.SfWrjQuestionMapper;
import com.droneclub.source.template.timedtask.WxfSder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class ISderServiceImpl implements ISderService {

    private final SfWrjQuestionMapper sfWrjQuestionMapper;
    private final DcTopicMapper dcTopicMapper;

    @Override
    public Object initWxfData() {
        for (int i = 0; i < 11; i++) {
            log.info("开始处理章节ID: {}", i);
            log.info("开始记录原始数据: {}", i);
            List<SfWrjQuestion> tmList = WxfSder.getTmList(i);
            for (SfWrjQuestion question : tmList) {
                sfWrjQuestionMapper.replaceInto(question);
            }
            log.info("开始处理topic数据: {}", i);
            LambdaQueryWrapper<SfWrjQuestion> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SfWrjQuestion::getChapId, i);
            List<SfWrjQuestion> wrjQuestions = sfWrjQuestionMapper.selectList(queryWrapper);
            doTransferWxfData("多旋翼", wrjQuestions);
        }
        return true;
    }

    @Override
    public Object transferWxfData(int zjId, String chapterType) {

        LambdaQueryWrapper<SfWrjQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SfWrjQuestion::getChapId, zjId);
        List<SfWrjQuestion> wrjQuestions = sfWrjQuestionMapper.selectList(queryWrapper);
        doTransferWxfData(chapterType, wrjQuestions);
        return true;
    }

    private void doTransferWxfData(String chapterType, List<SfWrjQuestion> wrjQuestions) {
        for (SfWrjQuestion question : wrjQuestions) {
            LambdaQueryWrapper<DcTopic> dcTopicLambdaQueryWrapper = new LambdaQueryWrapper<>();
            dcTopicLambdaQueryWrapper.eq(DcTopic::getSfId, question.getTiId());
            List<DcTopic> dcTopics = dcTopicMapper.selectList(dcTopicLambdaQueryWrapper);
            // 存在则更新
            DcTopic dcTopic = new DcTopic();
            dcTopic.setSfId(question.getTiId());
            dcTopic.setChapterType(chapterType);
            dcTopic.setChapterName(question.getChapName());
            dcTopic.setTopicName(question.getQuestion());
            dcTopic.setTopicStatus(1);
            dcTopic.setTopicOption(buildTopicOption(question.getOptList()));
            dcTopic.setQuestionAnswer(question.getAnswer());
            dcTopic.setQuestionAnalysis(question.getAnalysis());
            dcTopic.setCreateUser(4);
            dcTopic.setUpdateUser(4);
            if (dcTopics.size() > 0) {
                DcTopic first = dcTopics.get(0);
                // 有数据变化在更新
                if (first.hasChanges(dcTopic)) {
                    dcTopic.setId(first.getId());
                    dcTopic.setTopicRank(first.getTopicRank());
                    dcTopicMapper.updateById(dcTopic);
                }
            } else {
                dcTopicMapper.insert(dcTopic);
                dcTopic.setTopicRank(dcTopic.getId());
                dcTopicMapper.updateById(dcTopic);
            }
        }
    }

    @Override
    public Object transferWxfUpdateData() {
        // 获取今天的日期
        LocalDate today = LocalDate.now();
        String todayStart = today.atStartOfDay().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);  // 获取今天00:00:00
        String todayEnd = today.plusDays(1).atStartOfDay().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);  // 获取明天00:00:00

        // 构建查询条件
        LambdaQueryWrapper<SfWrjQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.between(SfWrjQuestion::getMyUpdateTime, todayStart, todayEnd);
        List<SfWrjQuestion> wrjQuestions = sfWrjQuestionMapper.selectList(queryWrapper);
        doTransferWxfData("多旋翼", wrjQuestions);
        return true;
    }

    public String buildTopicOption(String input) {
        // 解析为 JSONArray
        JSONArray jsonArray = JSONArray.parseArray(input);

        // 创建新的结果列表
        List<String> resultList = new ArrayList<>();

        // 遍历每个元素，构建新的格式
        for (Object obj : jsonArray) {
            JSONObject jsonObject = (JSONObject) obj;
            String no = jsonObject.getString("no");
            String opt = jsonObject.getString("opt");
            resultList.add(no + "." + opt);
        }

        // 转换为 JSON 字符串输出
        return JSONArray.toJSONString(resultList);
    }
}
