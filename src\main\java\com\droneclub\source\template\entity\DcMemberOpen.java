package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("dc_member_open") // 指定表名
public class DcMemberOpen {

    @TableId(value = "id", type = IdType.AUTO) // 主键自增
    private Integer id;

    @TableField("user_id") // 用户ID
    private Integer userId;

    @TableField("company_id") // 用户ID
    private Integer companyId;

    @TableField("member_type") // 会员类型
    private String memberType;

    @TableField("start_time") // 开通开始时间
    private String startTime;

    @TableField("end_time") // 开通结束时间
    private String endTime;

    @TableField("status") // 状态：0-已关闭，1-已开通
    private Integer status;

    @TableField("create_user") // 创建人
    private Integer createUser;

    @TableField("create_time") // 创建时间
    private Date createTime;

    @TableField("update_user") // 更新人
    private Integer updateUser;

    @TableField("update_time") // 更新时间
    private Date updateTime;

    @TableField("is_delete") // 是否删除：0-未删除，1-已删除
    private Integer isDelete;
}