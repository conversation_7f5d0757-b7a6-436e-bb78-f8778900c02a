package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("dc_sys_enums")
public class DcSysEnums {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String aliasCode;
    private String enumValue;
    private String enumLabel;
    private Integer rank;
    private String remark;
    private String updateTime;
    private String isDelete;

}
