package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.droneclub.source.template.entity.DcCompanyDiscounts;
import com.droneclub.source.template.mapper.DcCompanyDiscountsMapper;
import com.droneclub.source.template.module.pojo.DcCompanyDiscountsListSearch;
import com.droneclub.source.template.module.pojo.DcCompanyDiscountsVO;
import com.droneclub.source.template.module.service.IDcCompanyDiscountsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcCompanyDiscountsServiceImpl implements IDcCompanyDiscountsService {

    private final DcCompanyDiscountsMapper dcCompanyDiscountsMapper;

    @Override
    public List<DcCompanyDiscountsVO> getDcCompanyDiscountsList(DcCompanyDiscountsListSearch params) {
        LambdaQueryWrapper<DcCompanyDiscounts> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcCompanyDiscounts::getCompanyId, params.getCompanyId());
        queryWrapper.orderByDesc(DcCompanyDiscounts::getCreateTime);
        List<DcCompanyDiscounts> list = dcCompanyDiscountsMapper.selectList(queryWrapper);
        return list.stream()
                .map(dcCompanyDiscounts -> JSONObject.parseObject(JSONObject.toJSONString(dcCompanyDiscounts), DcCompanyDiscountsVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public DcCompanyDiscountsVO getDcCompanyDiscountsById(Integer id) {
        DcCompanyDiscounts dcCompanyDiscounts = dcCompanyDiscountsMapper.selectById(id);
        if (dcCompanyDiscounts == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        return JSONObject.parseObject(JSONObject.toJSONString(dcCompanyDiscounts), DcCompanyDiscountsVO.class);
    }


    @Override
    public DcCompanyDiscounts createDcCompanyDiscounts(DcCompanyDiscounts data) {
        data.setDiscountsStatus(1);
        boolean rs = dcCompanyDiscountsMapper.insert(data) > 0;
        log.info("创建 DcCompanyDiscounts: {}", rs ? "成功" : "失败");
        return data;
    }

    @Override
    public boolean updateDcCompanyDiscounts(DcCompanyDiscounts data) {
        boolean rs = dcCompanyDiscountsMapper.updateById(data) > 0;
        log.info("更新 DcCompanyDiscounts: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public boolean deleteDcCompanyDiscountsById(Integer id) {
        boolean rs = dcCompanyDiscountsMapper.deleteById(id) > 0;
        log.info("删除 DcCompanyDiscounts: {}", rs ? "成功" : "失败");
        return rs;
    }
}
