package com.droneclub.source.template.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("dc_auth_info")
public class DcAuthInfo {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String authType;
    private String authName;
    private String serviceScope;
    private String contactPerson;
    private String contactPhone;
    private String belongCompany;
    private Integer authStatus;
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    private String createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer updateUser;
    private String updateTime;
    private String isDelete;
    /**
     * 文件信息
     */
    @TableField(exist = false)
    private JSONObject files;
}
