package com.droneclub.source.template.single.region.pojo;

import lombok.Data;

import java.util.List;

/**
 * 地区树形结构VO
 */
@Data
public class RegionTreeVO {
    /**
     * 省份编码
     */
    private String provinceCode;
    
    /**
     * 省份名称
     */
    private String provinceName;
    
    /**
     * 首字母
     */
    private String firstChar;
    
    /**
     * 城市列表
     */
    private List<CityVO> cities;
    
    /**
     * 城市VO
     */
    @Data
    public static class CityVO {
        /**
         * 城市编码
         */
        private String cityCode;
        
        /**
         * 城市名称
         */
        private String cityName;
        
        /**
         * 首字母
         */
        private String firstChar;
        
        /**
         * 区县列表
         */
        private List<DistrictVO> districts;
    }
    
    /**
     * 区县VO
     */
    @Data
    public static class DistrictVO {
        /**
         * 区县编码
         */
        private String districtCode;
        
        /**
         * 区县名称
         */
        private String districtName;
        
        /**
         * 首字母
         */
        private String firstChar;
    }
} 