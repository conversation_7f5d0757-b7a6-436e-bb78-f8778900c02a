package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.entity.DcCompanyDiscounts;
import com.droneclub.source.template.module.pojo.DcCompanyDiscountsListSearch;
import com.droneclub.source.template.module.pojo.DcCompanyDiscountsVO;
import com.droneclub.source.template.module.service.IDcCompanyDiscountsService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dcCompanyDiscounts")
public class DcCompanyDiscountsController {

    private final IDcCompanyDiscountsService dcCompanyDiscountsService;

    @GetMapping("/getDcCompanyDiscountsList")
    public RestResult<List<DcCompanyDiscountsVO>> getDcCompanyDiscountsList(DcCompanyDiscountsListSearch params) {
        return RestResult.success(dcCompanyDiscountsService.getDcCompanyDiscountsList(params));
    }

    @GetMapping("/getDcCompanyDiscountsById")
    public RestResult<DcCompanyDiscountsVO> getDcCompanyDiscountsById(Integer id) {
        return RestResult.success(dcCompanyDiscountsService.getDcCompanyDiscountsById(id));
    }

    @PostMapping("/createDcCompanyDiscounts")
    public RestResult<DcCompanyDiscounts> createDcCompanyDiscounts(@RequestBody DcCompanyDiscounts data) {
        return RestResult.success(dcCompanyDiscountsService.createDcCompanyDiscounts(data));
    }

    @PutMapping("/updateDcCompanyDiscounts")
    public RestResult<Boolean> updateDcCompanyDiscounts(@RequestBody DcCompanyDiscounts data) {
        return RestResult.success(dcCompanyDiscountsService.updateDcCompanyDiscounts(data));
    }

    @DeleteMapping("/deleteDcCompanyDiscountsById")
    public RestResult<Boolean> deleteDcCompanyDiscountsById(Integer id) {
        return RestResult.success(dcCompanyDiscountsService.deleteDcCompanyDiscountsById(id));
    }
}
