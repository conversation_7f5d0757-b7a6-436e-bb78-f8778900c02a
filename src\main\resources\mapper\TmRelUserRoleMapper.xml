<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.droneclub.source.template.mapper.TmRelUserRoleMapper">

    <select id="getUserRoleByUserIds" resultType="com.droneclub.source.template.entity.TmRelUserRole">

        SELECT DISTINCT tur.user_id, tr.role_name, tr.role_code
        FROM tm_rel_user_role tur
        INNER JOIN tm_role tr ON tr.role_code = tur.role_code
        where tur.user_id IN
        <foreach collection="userIds" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="getUserIdByRole" resultType="java.lang.Integer">
        SELECT DISTINCT tur.user_id
        FROM tm_rel_user_role tur
        where tur.role_code IN
        <foreach collection="roleCodes" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>


</mapper>