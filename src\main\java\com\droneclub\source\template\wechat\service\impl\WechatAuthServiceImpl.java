package com.droneclub.source.template.wechat.service.impl;

import cn.soulspark.source.common.enums.HttpMethod;
import cn.soulspark.source.common.http.HttpToolkit;
import com.alibaba.fastjson.JSONObject;
import com.droneclub.source.template.common.cache.CompanyCache;
import com.droneclub.source.template.common.config.WechatConfig;
import com.droneclub.source.template.wechat.service.IWechatAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class WechatAuthServiceImpl implements IWechatAuthService {

    @Override
    public String getAccessToken() {
        String appId = WechatConfig.config.getAppId();
        String secret = WechatConfig.config.getSecret();
        Map<String, String> params = new HashMap<>();
        if (CompanyCache.getDcCompany() != null) {
            JSONObject wxConfigJson = JSONObject.parseObject(CompanyCache.getDcCompany().getWxConfig());
            appId = wxConfigJson.getString("appId");
            secret = wxConfigJson.getString("secret");
        }
        params.put("appid", appId);
        params.put("secret", secret);
        params.put("grant_type", "client_credential");
        JSONObject json = new JSONObject();
        try {
            log.info("请求微信 access token url: {} 参数: {}", WechatConfig.config.getAccessTokenUrl(), params);
            json = HttpToolkit.request(WechatConfig.config.getAccessTokenUrl(), HttpMethod.GET)
                    .addParam(params)
                    .paramsRequest()
                    .sync()
                    .getJSONObject();
        } catch (Exception e) {
            log.error("请求微信 access token 异常", e);
        }
        return json.getString("access_token");
    }
}
