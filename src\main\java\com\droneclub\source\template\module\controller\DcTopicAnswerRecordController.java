package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.model.RestResult;
import cn.soulspark.source.common.utils.StringUtils;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcTopicAnswerRecord;
import com.droneclub.source.template.module.pojo.DcTopicAnswerRecordListSearch;
import com.droneclub.source.template.module.pojo.DcTopicAnswerRecordVO;
import com.droneclub.source.template.module.service.IDcTopicAnswerRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dcTopicAnswerRecord")
public class DcTopicAnswerRecordController {

    private final IDcTopicAnswerRecordService dcTopicAnswerRecordService;

    @GetMapping("/getDcTopicAnswerRecordList")
    public RestResult<ListData<DcTopicAnswerRecordVO>> getDcTopicAnswerRecordList(DcTopicAnswerRecordListSearch params) {
        return RestResult.success(dcTopicAnswerRecordService.getDcTopicAnswerRecordList(params));
    }

    @GetMapping("/getDcTopicAnswerRecordById")
    public RestResult<DcTopicAnswerRecordVO> getDcTopicAnswerRecordById(Integer id) {
        return RestResult.success(dcTopicAnswerRecordService.getDcTopicAnswerRecordById(id));
    }

    /**
     * 获取当前用户最新一条答题记录
     *
     * @return 当前用户最新一条答题记录
     */
    @GetMapping("/getDcTopicAnswerLatestRecord")
    public RestResult<DcTopicAnswerRecord> getDcTopicAnswerLatestRecord(String chapterType, Integer pageSize) {
        return RestResult.success(dcTopicAnswerRecordService.getDcTopicAnswerLatestRecord(chapterType, pageSize));
    }

    @PostMapping("/createDcTopicAnswerRecord")
    public RestResult<DcTopicAnswerRecord> createDcTopicAnswerRecord(@RequestBody DcTopicAnswerRecord data) {
        if (data.getTopicId() == null || StringUtils.isInvalid(data.getAnswerResult())) {
            return RestResult.failure(ResultCode.LACK_PARAMS);
        }
        return RestResult.success(dcTopicAnswerRecordService.createDcTopicAnswerRecord(data));
    }

    @PutMapping("/updateDcTopicAnswerRecord")
    public RestResult<Boolean> updateDcTopicAnswerRecord(@RequestBody DcTopicAnswerRecord data) {
        return RestResult.success(dcTopicAnswerRecordService.updateDcTopicAnswerRecord(data));
    }

    @DeleteMapping("/deleteDcTopicAnswerRecordById")
    public RestResult<Boolean> deleteDcTopicAnswerRecordById(Integer id) {
        return RestResult.success(dcTopicAnswerRecordService.deleteDcTopicAnswerRecordById(id));
    }
}
