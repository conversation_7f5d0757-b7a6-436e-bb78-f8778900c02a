package com.droneclub.source.template.module.service;

import com.droneclub.source.template.entity.DcOperationRecord;
import com.droneclub.source.template.module.pojo.DcOperationRecordListSearch;
import com.droneclub.source.template.module.pojo.DcOperationRecordVO;

public interface IDcOperationRecordService {

    Object getDcOperationRecordList(DcOperationRecordListSearch params);

    DcOperationRecordVO getDcOperationRecordById(Integer id);

    DcOperationRecord createDcOperationRecord(DcOperationRecord data);

    boolean updateDcOperationRecord(DcOperationRecord data);

    boolean deleteDcOperationRecordById(Integer id);

    boolean checkOperationRecordExist(DcOperationRecord conditions);


    boolean deleteDcOperationRecord(DcOperationRecord data);
}
