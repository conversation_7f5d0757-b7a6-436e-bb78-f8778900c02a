package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcTrainingApply;
import com.droneclub.source.template.module.pojo.DcTrainingApplyListSearch;
import com.droneclub.source.template.module.pojo.DcTrainingApplyVO;
import com.droneclub.source.template.module.service.IDcTrainingApplyService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dcTrainingApply")
public class DcTrainingApplyController {
    private final IDcTrainingApplyService dcTrainingApplyService;

    @GetMapping("/getDcTrainingApplyList")
    public RestResult<ListData<DcTrainingApplyVO>> getDcTrainingApplyList(DcTrainingApplyListSearch params) {
        return RestResult.success(dcTrainingApplyService.getDcTrainingApplyList(params));
    }

    @GetMapping("/getDcTrainingApplyById")
    public RestResult<DcTrainingApplyVO> getDcTrainingApplyById(Integer id) {
        return RestResult.success(dcTrainingApplyService.getDcTrainingApplyById(id));
    }

    @PostMapping("/createDcTrainingApply")
    public RestResult<DcTrainingApply> createDcTrainingApply(@RequestBody DcTrainingApply data) {
        return RestResult.success(dcTrainingApplyService.createDcTrainingApply(data));
    }

    @PutMapping("/updateDcTrainingApply")
    public RestResult<Boolean> updateDcTrainingApply(@RequestBody DcTrainingApply data) {
        return RestResult.success(dcTrainingApplyService.updateDcTrainingApply(data));
    }

    @DeleteMapping("/deleteDcTrainingApplyById")
    public RestResult<Boolean> deleteDcTrainingApplyById(Integer id) {
        return RestResult.success(dcTrainingApplyService.deleteDcTrainingApplyById(id));
    }
}
