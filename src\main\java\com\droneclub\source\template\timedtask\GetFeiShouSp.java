package com.droneclub.source.template.timedtask;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.IOException;
import java.util.Random;

public class GetFeiShouSp {
    private static final String LIST_URL = "https://jiuye.bj-zkhd.com/resume/index.php?all=&tp=0&page=";
    private static final String DETAIL_URL = "https://jiuye.bj-zkhd.com/resume/index.php?c=show&id=";

    private static final String[] CERTIFICATE_LEVEL = new String[]{"[\"垂直起降固定翼、超视距驾驶员、四类（25kg以上）\",\"\"]",
            "[\"多旋翼、视距内驾驶员、三类（25kg以下）\",\"\"]", "[\"多旋翼、超视距驾驶员、四类（25kg以上）\",\"\"]"};

    public static void main(String[] args) {
        String url = "https://jiuye.bj-zkhd.com/resume/index.php?all=&tp=0&page=2";

        try {
            Document doc = Jsoup.connect(url).get();
            Elements jobs = doc.select(".search_job_list");
            for (Element job : jobs) {
                String id = job.select(".yunjoblist_new_icon").attr("pid");
                getDetailPage(id);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void getDetailPage(String uId) {
        String url = DETAIL_URL + uId;
        try {
            Document doc = Jsoup.connect(url)
                    .cookie("uid", "3041")
                    .cookie("shell", "38a40f15a0e61b48ab5ac92a5320d37a")
                    .cookie("usertype", "2")
                    .cookie("userdid", "0")
                    .cookie("amtype", "0")
                    .cookie("PHPSESSID", "71se4f6ftfna44qr75qogiuus9")
                    .cookie("couponOT", "3041")
                    .cookie("lookresume", "1423")
                    .get();

            String name = doc.select(".yun_newedition_resume_username").text();
            String phone = doc.select(".tcktouch_box_p_sj").text();
            String level = getLevel();
            Elements infos = doc.select(".yun_newedition_yx_list li");
            String money = "";
            String content = "";
            String address = "";
            for (Element info : infos) {
                String itemName = info.select("span.yun_newedition_yx_name").text();
                String itemValue = info.ownText();

                if (itemName.contains("期望薪资")) {
                    money = itemValue;
                }
                if (itemName.contains("意向岗位")) {
                    content = itemValue;
                }
                if (itemName.contains("工作地点")) {
                    address = itemValue;
                }
            }
            buildSql(name, phone, level, money, content, address, url);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static String buildSql(String name, String phone, String level, String money, String content, String address, String bindUrl) {
        String sql = String.format("INSERT INTO `drone-club`.dc_publish_info \n" +
                        "(info_type,info_status,contact_person,contact_phone,certificate_level,\n" +
                        "uav_type,uav_weight,total_flight_time,extra_skill,expect_job_content,\n" +
                        "job_expense,work_area,work_place,work_start_time,work_end_time,\n" +
                        "job_content,pay_money,require_certificate_level,provide_food,provide_room,\n" +
                        "require_extra_skill,device_type,device_description,device_rent,device_place,\n" +
                        "lease_start_time,lease_end_time,\n" +
                        "create_user,create_time,update_user,update_time,is_delete,bind_url)\n" +
                        "VALUES\n" +
                        "(1,2,'%s','%s','%s',\n" +
                        "NULL,NULL,'%s','%s','%s',\n" +
                        "NULL,'%s',NULL,NULL,NULL,\n" +
                        "NULL,'%s',NULL,NULL,NULL,\n" +
                        "NULL,NULL,NULL,NULL,NULL,\n" +
                        "NULL,NULL,\n" +
                        "4,NOW(),4,NOW(),0,'%s');",
                name, phone, level,
                getFlyTime(), content, content,
                address,
                money,
                bindUrl
        );
        System.out.println(sql.replaceAll("\n", ""));
        return sql;
    }

    public static int getFlyTime() {
        Random random = new Random();

        int lowerBound = 1000;
        int upperBound = 5000;
        int count = 100;

        for (int i = 0; i < count; i++) {
            int randomNum = random.nextInt(upperBound - lowerBound + 1) + lowerBound;
            return randomNum;
        }
        return lowerBound;
    }

    public static String getLevel() {
        Random random = new Random();
        int randomIndex = random.nextInt(CERTIFICATE_LEVEL.length);

        return CERTIFICATE_LEVEL[randomIndex];
    }
}
