package com.droneclub.source.template.single.region.pojo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 城市信息VO
 */
@Data
public class SysCityVO {
    /**
     * 数据ID
     */
    private Long id;
    
    /**
     * 城市编码
     */
    private String cityCode;
    
    /**
     * 城市名称
     */
    private String cityName;
    
    /**
     * 等级
     */
    private String level;
    
    /**
     * 首字母
     */
    private String firstChar;
    
    /**
     * 区号
     */
    private String cityAreaCode;
    
    /**
     * 中心点坐标
     */
    private String center;
    
    /**
     * 所属省份编码
     */
    private String provinceCode;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 