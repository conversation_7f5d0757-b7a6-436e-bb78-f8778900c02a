package com.droneclub.source.template.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.droneclub.source.template.entity.DcOperationRecord;
import com.droneclub.source.template.module.pojo.DcOperationRecordListSearch;
import com.droneclub.source.template.module.pojo.PublishInfoVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DcOperationRecordMapper extends BaseMapper<DcOperationRecord> {

    long countShowListData(DcOperationRecordListSearch params);

    List<PublishInfoVO> getPublishInfoVOList(DcOperationRecordListSearch params);

}
