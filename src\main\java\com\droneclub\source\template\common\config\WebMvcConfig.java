package com.droneclub.source.template.common.config;

import com.droneclub.source.template.common.interceptor.SessionInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 通用权限拦截
 */
@Configuration
@RequiredArgsConstructor
public class WebMvcConfig implements WebMvcConfigurer {

    private final SystemConfig systemConfig;

    @Value("${server.servlet.context-path}")
    private String apiPrefix;


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new SessionInterceptor(systemConfig.getWhiteList(), apiPrefix)).addPathPatterns("/**");
    }
}