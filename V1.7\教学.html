﻿<!DOCTYPE html>
<html>
  <head>
    <title>教学</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/教学/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/教学/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u0" class="ax_default box_1">
        <div id="u0_div" class=""></div>
        <div id="u0_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u1" class="ax_default _图片_">
        <img id="u1_img" class="img " src="images/教学/u1.png"/>
        <div id="u1_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u2" class="ax_default _图片_">
        <img id="u2_img" class="img " src="images/教学/u2.png"/>
        <div id="u2_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3" class="ax_default label">
        <div id="u3_div" class=""></div>
        <div id="u3_text" class="text ">
          <p><span>教学</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4" class="ax_default box_1">
        <div id="u4_div" class=""></div>
        <div id="u4_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u5" class="ax_default" data-left="149" data-top="744" data-width="25" data-height="43">

        <!-- Unnamed (占位符) -->
        <div id="u6" class="ax_default placeholder">
          <img id="u6_img" class="img " src="images/教学/u6.svg"/>
          <div id="u6_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u7" class="ax_default label">
          <div id="u7_div" class=""></div>
          <div id="u7_text" class="text ">
            <p><span>首页</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (占位符) -->
      <div id="u8" class="ax_default placeholder">
        <img id="u8_img" class="img " src="images/教学/u8.svg"/>
        <div id="u8_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u9" class="ax_default label">
        <div id="u9_div" class=""></div>
        <div id="u9_text" class="text ">
          <p><span>资源库</span></p>
        </div>
      </div>

      <!-- Unnamed (占位符) -->
      <div id="u10" class="ax_default placeholder">
        <img id="u10_img" class="img " src="images/教学/u10.svg"/>
        <div id="u10_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u11" class="ax_default label">
        <div id="u11_div" class=""></div>
        <div id="u11_text" class="text ">
          <p><span>发布</span></p>
        </div>
      </div>

      <!-- Unnamed (占位符) -->
      <div id="u12" class="ax_default placeholder">
        <img id="u12_img" class="img " src="images/教学/u12.svg"/>
        <div id="u12_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u13" class="ax_default label">
        <div id="u13_div" class=""></div>
        <div id="u13_text" class="text ">
          <p><span>教学</span></p>
        </div>
      </div>

      <!-- Unnamed (占位符) -->
      <div id="u14" class="ax_default placeholder">
        <img id="u14_img" class="img " src="images/教学/u14.svg"/>
        <div id="u14_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u15" class="ax_default label">
        <div id="u15_div" class=""></div>
        <div id="u15_text" class="text ">
          <p><span>我的</span></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u16" class="ax_default _图片_">
        <img id="u16_img" class="img " src="images/教学/u16.svg"/>
        <div id="u16_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u17" class="ax_default _三级标题">
        <div id="u17_div" class=""></div>
        <div id="u17_text" class="text ">
          <p><span>banner位置</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u18" class="ax_default box_1">
        <div id="u18_div" class=""></div>
        <div id="u18_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (占位符) -->
      <div id="u19" class="ax_default placeholder">
        <img id="u19_img" class="img " src="images/教学/u19.svg"/>
        <div id="u19_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u20" class="ax_default label">
        <div id="u20_div" class=""></div>
        <div id="u20_text" class="text ">
          <p><span>机构信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u21" class="ax_default label">
        <div id="u21_div" class=""></div>
        <div id="u21_text" class="text ">
          <p><span>基础设置</span></p>
        </div>
      </div>

      <!-- Unnamed (占位符) -->
      <div id="u22" class="ax_default placeholder">
        <img id="u22_img" class="img " src="images/教学/u22.svg"/>
        <div id="u22_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u23" class="ax_default label">
        <div id="u23_div" class=""></div>
        <div id="u23_text" class="text ">
          <p><span>课程信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u24" class="ax_default box_1">
        <div id="u24_div" class=""></div>
        <div id="u24_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (占位符) -->
      <div id="u25" class="ax_default placeholder">
        <img id="u25_img" class="img " src="images/教学/u25.svg"/>
        <div id="u25_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u26" class="ax_default label">
        <div id="u26_div" class=""></div>
        <div id="u26_text" class="text ">
          <p><span>学员管理</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u27" class="ax_default label">
        <div id="u27_div" class=""></div>
        <div id="u27_text" class="text ">
          <p><span>教学管理</span></p>
        </div>
      </div>

      <!-- Unnamed (占位符) -->
      <div id="u28" class="ax_default placeholder">
        <img id="u28_img" class="img " src="images/教学/u28.svg"/>
        <div id="u28_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u29" class="ax_default label">
        <div id="u29_div" class=""></div>
        <div id="u29_text" class="text ">
          <p><span>学员练习记录</span></p>
        </div>
      </div>

      <!-- Unnamed (占位符) -->
      <div id="u30" class="ax_default placeholder">
        <img id="u30_img" class="img " src="images/教学/u30.svg"/>
        <div id="u30_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u31" class="ax_default label">
        <div id="u31_div" class=""></div>
        <div id="u31_text" class="text ">
          <p><span>成员管理</span></p>
        </div>
      </div>

      <!-- Unnamed (占位符) -->
      <div id="u32" class="ax_default placeholder">
        <img id="u32_img" class="img " src="images/教学/u32.svg"/>
        <div id="u32_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u33" class="ax_default label">
        <div id="u33_div" class=""></div>
        <div id="u33_text" class="text ">
          <p><span>作业布置</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u34" class="ax_default sticky_1">
        <div id="u34_div" class=""></div>
        <div id="u34_text" class="text ">
          <p><span>1.成员管理新增角色：新增“学员培训管理”角色，该角色拥有作业布置和学员练习记录权限；</span></p><p><span><br></span></p><p><span>2.把运营权限还原成“机构信息”、“课程信息”</span></p><p><span><br></span></p><p><span>3.新增“作业布置”模块</span></p><p><span>（1）关联角色：教员、学员培训管理</span></p><p><span>（2）点击后进入作业布置列表</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
