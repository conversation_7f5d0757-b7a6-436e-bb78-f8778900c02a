package com.droneclub.source.template.single.region.utils;

import cn.soulspark.source.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

/**
 * 拼音工具类
 */
@Slf4j
public class PinyinUtils {

    private static final HanyuPinyinOutputFormat DEFAULT_FORMAT = new HanyuPinyinOutputFormat();

    static {
        DEFAULT_FORMAT.setCaseType(HanyuPinyinCaseType.UPPERCASE); // 默认大写
        DEFAULT_FORMAT.setToneType(HanyuPinyinToneType.WITHOUT_TONE); // 默认不带音调
    }

    /**
     * 获取汉字的首字母
     * 
     * @param chinese 汉字
     * @return 首字母（大写）
     */
    public static String getFirstLetter(String chinese) {
        if (chinese == null || chinese.isEmpty()) {
            return "";
        }
        
        char firstChar = chinese.charAt(0);
        String result = "";
        
        if (Character.toString(firstChar).matches("[\\u4E00-\\u9FA5]+")) {
            try {
                String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(firstChar, DEFAULT_FORMAT);
                if (pinyinArray != null && pinyinArray.length > 0) {
                    result = String.valueOf(pinyinArray[0].charAt(0));
                }
            } catch (BadHanyuPinyinOutputFormatCombination e) {
                log.error("获取汉字首字母失败", e);
            }
        } else if (Character.toString(firstChar).matches("[a-zA-Z]")) {
            result = Character.toString(firstChar).toUpperCase();
        } else {
            result = "#"; // 非汉字和字母，返回#
        }
        
        return result;
    }

    /**
     * 获取汉字的全拼（支持多音字，默认返回第一个读音）
     * @param chinese 输入字符串
     * @return 全拼（大写），非汉字返回原字符
     */
    public static String getFullPinyin(String chinese) {
        if (StringUtils.isInvalid(chinese)) {
            return "";
        }
        StringBuilder result = new StringBuilder();
        for (char c : chinese.toCharArray()) {
            if (isChineseCharacter(c)) {
                try {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, DEFAULT_FORMAT);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        result.append(pinyinArray[0]); // 默认取第一个读音
                    }
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    result.append(c); // 转换失败时保留原字符
                }
            } else {
                result.append(c);
            }
        }
        return result.toString().toLowerCase();
    }

    /**
     * 判断是否为汉字
     */
    private static boolean isChineseCharacter(char c) {
        return Character.toString(c).matches("[\\u4E00-\\u9FA5]");
    }

    /**
     * 判断是否为字母
     */
    private static boolean isLetter(char c) {
        return Character.toString(c).matches("[a-zA-Z]");
    }
} 
