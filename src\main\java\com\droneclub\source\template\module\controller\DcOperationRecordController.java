package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.entity.DcOperationRecord;
import com.droneclub.source.template.module.pojo.DcOperationRecordListSearch;
import com.droneclub.source.template.module.pojo.DcOperationRecordVO;
import com.droneclub.source.template.module.service.IDcOperationRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dcOperationRecord")
public class DcOperationRecordController {

    private final IDcOperationRecordService dcOperationRecordService;

    @GetMapping("/getDcOperationRecordList")
    public RestResult<Object> getDcOperationRecordList(DcOperationRecordListSearch params) {
        return RestResult.success(dcOperationRecordService.getDcOperationRecordList(params));
    }

    @GetMapping("/getDcOperationRecordById")
    public RestResult<DcOperationRecordVO> getDcOperationRecordById(Integer id) {
        return RestResult.success(dcOperationRecordService.getDcOperationRecordById(id));
    }

    @PostMapping("/createDcOperationRecord")
    public RestResult<DcOperationRecord> createDcOperationRecord(@RequestBody DcOperationRecord data) {
        return RestResult.success(dcOperationRecordService.createDcOperationRecord(data));
    }

    @PutMapping("/updateDcOperationRecord")
    public RestResult<Boolean> updateDcOperationRecord(@RequestBody DcOperationRecord data) {
        return RestResult.success(dcOperationRecordService.updateDcOperationRecord(data));
    }

    @DeleteMapping("/deleteDcOperationRecord")
    public RestResult<Boolean> deleteDcOperationRecord(@RequestBody DcOperationRecord data) {
        return RestResult.success(dcOperationRecordService.deleteDcOperationRecord(data));
    }

    @DeleteMapping("/deleteDcOperationRecordById")
    public RestResult<Boolean> deleteDcOperationRecordById(Integer id) {
        return RestResult.success(dcOperationRecordService.deleteDcOperationRecordById(id));
    }
}
