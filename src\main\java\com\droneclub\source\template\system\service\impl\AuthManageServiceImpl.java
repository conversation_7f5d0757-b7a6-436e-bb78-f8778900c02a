package com.droneclub.source.template.system.service.impl;

import com.droneclub.source.template.entity.TmAuthApi;
import com.droneclub.source.template.mapper.AuthManageMapper;
import com.droneclub.source.template.system.dto.UserAuthDTO;
import com.droneclub.source.template.system.service.IAuthManegeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.*;


@Slf4j
@Service
@RequiredArgsConstructor
public class AuthManageServiceImpl implements IAuthManegeService {

    private final static String[] IGNORE_PATH = new String[]{"/auth/login", "/health/check", "/source/builder", "/source/rebuildAuthData"};
    private final AuthManageMapper authManageMapper;
    private final RequestMappingHandlerMapping handlerMapping;

    @Override
    public boolean rebuildAuthData() {
        Map<RequestMappingInfo, HandlerMethod> handlerMethods = handlerMapping.getHandlerMethods();
        List<TmAuthApi> authApis = new ArrayList<>();
        for (RequestMappingInfo info : handlerMethods.keySet()) {
            if (Arrays.stream(info.getDirectPaths().toArray()).count() == 1) {
                String path = String.valueOf(info.getDirectPaths().toArray()[0]);
                if (Arrays.asList(IGNORE_PATH).contains(path)) {
                    continue;
                }
                Object[] methods = info.getMethodsCondition().getMethods().toArray();
                if (Arrays.stream(methods).count() == 1) {
                    String apiCode = path.substring(path.lastIndexOf("/") + 1);
                    TmAuthApi tmAuthApi = TmAuthApi.builder()
                            .apiCode(apiCode)
                            .apiName(apiCode)
                            .apiPath(path)
                            .build();
                    authApis.add(tmAuthApi);
                }
            }
        }
        // 批量添加接口权限
        authManageMapper.batchInsert(authApis);
        // 添加固定权限数据
        authManageMapper.initAuthData();
        return true;
    }

    @Override
    public List<UserAuthDTO> getUserAuthData(Integer userId) {
        return authManageMapper.getUserAuthData(userId);
    }
}
