package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.droneclub.source.template.entity.DcCompanyIndustry;
import com.droneclub.source.template.mapper.DcCompanyIndustryMapper;
import com.droneclub.source.template.module.pojo.DcCompanyIndustryListSearch;
import com.droneclub.source.template.module.pojo.DcCompanyIndustryVO;
import com.droneclub.source.template.module.service.IDcCompanyIndustryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcCompanyIndustryServiceImpl extends ServiceImpl<DcCompanyIndustryMapper, DcCompanyIndustry> implements IDcCompanyIndustryService {

    private final DcCompanyIndustryMapper dcCompanyIndustryMapper;

    @Override
    public List<DcCompanyIndustryVO> getDcCompanyIndustryList(DcCompanyIndustryListSearch params) {
        LambdaQueryWrapper<DcCompanyIndustry> queryWrapper = new LambdaQueryWrapper<>();
        if (params.getCompanyIds() != null && !params.getCompanyIds().isEmpty()) {
            queryWrapper.in(DcCompanyIndustry::getCompanyId, params.getCompanyIds());
        }
        if (params.getCompanyId() != null) {
            queryWrapper.eq(DcCompanyIndustry::getCompanyId, params.getCompanyId());
        }
        List<DcCompanyIndustry> list = dcCompanyIndustryMapper.selectList(queryWrapper);
        return list.stream()
                .map(dcCompanyIndustry -> JSONObject.parseObject(JSONObject.toJSONString(dcCompanyIndustry), DcCompanyIndustryVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public DcCompanyIndustryVO getDcCompanyIndustryById(Integer id) {
        DcCompanyIndustry dcCompanyIndustry = dcCompanyIndustryMapper.selectById(id);
        if (dcCompanyIndustry == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        return JSONObject.parseObject(JSONObject.toJSONString(dcCompanyIndustry), DcCompanyIndustryVO.class);
    }


    @Override
    public DcCompanyIndustry createDcCompanyIndustry(DcCompanyIndustry data) {
        boolean rs = dcCompanyIndustryMapper.insert(data) > 0;
        log.info("创建 DcCompanyIndustry: {}", rs ? "成功" : "失败");
        return data;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDcCompanyIndustry(Integer companyId, String industries) {
        String[] array = industries.split(",");
        // 删除所有历史数据
        LambdaQueryWrapper<DcCompanyIndustry> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcCompanyIndustry::getCompanyId, companyId);
        dcCompanyIndustryMapper.delete(queryWrapper);
        // 批量插入新绑定行业
        List<DcCompanyIndustry> industriesList = new ArrayList<>();
        for (String industry : array) {
            industriesList.add(DcCompanyIndustry.builder()
                    .companyId(companyId)
                    .industry(industry)
                    .build());
        }
        if (!industriesList.isEmpty()) {
            this.saveBatch(industriesList);
        }
        return true;
    }

    @Override
    public boolean updateDcCompanyIndustry(DcCompanyIndustry data) {
        boolean rs = dcCompanyIndustryMapper.updateById(data) > 0;
        log.info("更新 DcCompanyIndustry: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public boolean deleteDcCompanyIndustryById(Integer id) {
        boolean rs = dcCompanyIndustryMapper.deleteById(id) > 0;
        log.info("删除 DcCompanyIndustry: {}", rs ? "成功" : "失败");
        return rs;
    }
}
