package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcChapterDoTopicRecord;
import com.droneclub.source.template.module.pojo.DcChapterDoTopicRecordVO;
import com.droneclub.source.template.module.pojo.DcChapterDoTopicRecordListSearch;
import com.droneclub.source.template.module.service.IDcChapterDoTopicRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dcChapterDoTopicRecord")
public class DcChapterDoTopicRecordController {

    private final IDcChapterDoTopicRecordService dcChapterDoTopicRecordService;

    @GetMapping("/getDcChapterDoTopicRecordList")
    public RestResult<ListData<DcChapterDoTopicRecordVO>> getDcChapterDoTopicRecordList(DcChapterDoTopicRecordListSearch params) {
        return RestResult.success(dcChapterDoTopicRecordService.getDcChapterDoTopicRecordList(params));
    }

    @GetMapping("/getDcChapterDoTopicRecordById")
    public RestResult<DcChapterDoTopicRecordVO> getDcChapterDoTopicRecordById(Integer id) {
        return RestResult.success(dcChapterDoTopicRecordService.getDcChapterDoTopicRecordById(id));
    }

    @PostMapping("/createDcChapterDoTopicRecord")
    public RestResult<DcChapterDoTopicRecord> createDcChapterDoTopicRecord(@RequestBody DcChapterDoTopicRecord data) {
        return RestResult.success(dcChapterDoTopicRecordService.createDcChapterDoTopicRecord(data));
    }

    @PutMapping("/updateDcChapterDoTopicRecord")
    public RestResult<Boolean> updateDcChapterDoTopicRecord(@RequestBody DcChapterDoTopicRecord data) {
        return RestResult.success(dcChapterDoTopicRecordService.updateDcChapterDoTopicRecord(data));
    }

    @DeleteMapping("/deleteDcChapterDoTopicRecordById")
    public RestResult<Boolean> deleteDcChapterDoTopicRecordById(Integer id) {
        return RestResult.success(dcChapterDoTopicRecordService.deleteDcChapterDoTopicRecordById(id));
    }
}
