package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcEventNews;
import com.droneclub.source.template.module.pojo.DcEventNewsListSearch;
import com.droneclub.source.template.module.pojo.DcEventNewsVO;
import com.droneclub.source.template.module.service.IDcEventNewsService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dcEventNews")
public class DcEventNewsController {

    private final IDcEventNewsService dcEventNewsService;

    @GetMapping("/getDcEventNewsList")
    public RestResult<ListData<DcEventNewsVO>> getDcEventNewsList(DcEventNewsListSearch params) {
        return RestResult.success(dcEventNewsService.getDcEventNewsList(params));
    }

    @GetMapping("/getDcEventNewsById")
    public RestResult<DcEventNewsVO> getDcEventNewsById(Integer id) {
        return RestResult.success(dcEventNewsService.getDcEventNewsById(id));
    }

    @PostMapping("/createDcEventNews")
    public RestResult<DcEventNews> createDcEventNews(@RequestBody DcEventNews data) {
        return RestResult.success(dcEventNewsService.createDcEventNews(data));
    }

    @PutMapping("/updateDcEventNews")
    public RestResult<Boolean> updateDcEventNews(@RequestBody DcEventNews data) {
        return RestResult.success(dcEventNewsService.updateDcEventNews(data));
    }

    @DeleteMapping("/deleteDcEventNewsById")
    public RestResult<Boolean> deleteDcEventNewsById(Integer id) {
        return RestResult.success(dcEventNewsService.deleteDcEventNewsById(id));
    }
}
