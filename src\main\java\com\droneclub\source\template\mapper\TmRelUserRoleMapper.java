package com.droneclub.source.template.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.droneclub.source.template.entity.TmRelUserRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface TmRelUserRoleMapper extends BaseMapper<TmRelUserRole> {

    List<TmRelUserRole> getUserRoleByUserIds(@Param("userIds") Set<Integer> userIds);

    Set<Integer> getUserIdByRole(@Param("roleCodes") String[] roleCodes);
}
