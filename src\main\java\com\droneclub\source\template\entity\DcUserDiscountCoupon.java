package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("dc_user_discount_coupon")
public class DcUserDiscountCoupon {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer userId;
    private Integer companyId;
    private String discountCouponCode;
    private Integer discountsId;
    private Integer discountCouponStatus;
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    private String createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer updateUser;
    private String updateTime;
    private String isDelete;

}
