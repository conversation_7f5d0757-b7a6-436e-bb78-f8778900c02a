package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.common.utils.TemplateSessionUtils;
import com.droneclub.source.template.entity.DcTopic;
import com.droneclub.source.template.entity.DcTopicAnswerRecord;
import com.droneclub.source.template.mapper.DcTopicAnswerRecordMapper;
import com.droneclub.source.template.mapper.DcTopicMapper;
import com.droneclub.source.template.module.pojo.DcTopicAnswerRecordListSearch;
import com.droneclub.source.template.module.pojo.DcTopicAnswerRecordVO;
import com.droneclub.source.template.module.service.IDcTopicAnswerRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcTopicAnswerRecordServiceImpl implements IDcTopicAnswerRecordService {

    private final DcTopicAnswerRecordMapper dcTopicAnswerRecordMapper;
    private final DcTopicMapper dcTopicMapper;

    @Override
    public ListData<DcTopicAnswerRecordVO> getDcTopicAnswerRecordList(DcTopicAnswerRecordListSearch params) {
        LambdaQueryWrapper<DcTopicAnswerRecord> queryWrapper = new LambdaQueryWrapper<>();
        // 查询总数
        Long total = dcTopicAnswerRecordMapper.selectCount(queryWrapper);

        // 分页查询
        Page<DcTopicAnswerRecord> page = new Page<>(params.getPageNo(), params.getPageSize());
        IPage<DcTopicAnswerRecord> dcTopicAnswerRecordPage = dcTopicAnswerRecordMapper.selectPage(page, queryWrapper);
        List<DcTopicAnswerRecord> list = dcTopicAnswerRecordPage.getRecords();
        List<DcTopicAnswerRecordVO> listVO = list.stream()
                .map(dcTopicAnswerRecord -> JSONObject.parseObject(JSONObject.toJSONString(dcTopicAnswerRecord), DcTopicAnswerRecordVO.class))
                .collect(Collectors.toList());
        return new ListData<>(listVO, total, params.getPageNo(), params.getPageSize());
    }

    @Override
    public DcTopicAnswerRecordVO getDcTopicAnswerRecordById(Integer id) {
        DcTopicAnswerRecord dcTopicAnswerRecord = dcTopicAnswerRecordMapper.selectById(id);
        if (dcTopicAnswerRecord == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        return JSONObject.parseObject(JSONObject.toJSONString(dcTopicAnswerRecord), DcTopicAnswerRecordVO.class);
    }

    @Override
    public DcTopicAnswerRecord getDcTopicAnswerLatestRecord(String chapterType, Integer pageSize) {
        LambdaQueryWrapper<DcTopicAnswerRecord> queryWrapper = new LambdaQueryWrapper<>();
        Integer currentUser = TemplateSessionUtils.getCurrentUser().getId();

        if (currentUser != null) {
            queryWrapper.eq(DcTopicAnswerRecord::getAnswerId, currentUser);
        }
        // 按rank正序排序
        queryWrapper.orderByDesc(DcTopicAnswerRecord::getId);
        List<DcTopicAnswerRecord> dcTopicAnswerRecords = dcTopicAnswerRecordMapper.selectList(queryWrapper);
        if (dcTopicAnswerRecords.size() > 0) {
            DcTopicAnswerRecordVO dcTopicAnswerRecord = JSONObject.parseObject(JSONObject.toJSONString(dcTopicAnswerRecords.get(0)), DcTopicAnswerRecordVO.class);
            DcTopic dcTopic = dcTopicMapper.selectById(dcTopicAnswerRecord.getTopicId());
            if (dcTopic != null) {
                // 查询题目章节列表，获取当前题目小结
                LambdaQueryWrapper<DcTopic> dcTopicLambdaQueryWrapper = new LambdaQueryWrapper<>();
                dcTopicLambdaQueryWrapper.eq(DcTopic::getChapterType, dcTopic.getChapterType());
                dcTopicLambdaQueryWrapper.eq(DcTopic::getChapterName, dcTopic.getChapterName());
                List<DcTopic> topics = dcTopicMapper.selectList(dcTopicLambdaQueryWrapper);
                topics.sort(Comparator.comparingInt(DcTopic::getTopicRank));
                // 查找目标 id 在排序后的 list 中的索引
                int index = -1;
                for (int i = 0; i < topics.size(); i++) {
                    if (Objects.equals(topics.get(i).getId(), dcTopic.getId())) {
                        index = i;
                        break;
                    }
                }

                if (index != -1) {
                    // 输出目标 id 在 list 中的位置
                    dcTopicAnswerRecord.setChapterInsideRank(index + 1);
                    // 获取前一个元素
                    if (index > 0) {
                        dcTopicAnswerRecord.setBeforeTopicId(topics.get(index - 1).getId());
                    }
                    // 获取后一个元素
                    if (index < topics.size() - 1) {
                        dcTopicAnswerRecord.setAfterTopicId(topics.get(index + 1).getId());
                    }
                }

                dcTopicAnswerRecord.setChapterType(dcTopic.getChapterType());
                dcTopicAnswerRecord.setChapterName(dcTopic.getChapterName());
                dcTopicAnswerRecord.setTopicRank(dcTopic.getTopicRank());
            }
            try {
                dcTopicAnswerRecord.setPageNo(findPageWithId(dcTopic.getId(), chapterType, pageSize));
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            return dcTopicAnswerRecord;
        }
        return null;
    }

    public Integer findPageWithId(Integer targetId, String chapterType, int pageSize) throws InterruptedException {
        // 创建线程池
        ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());

        // 用于保存结果的共享变量
        AtomicInteger resultPageNo = new AtomicInteger(-1);

        // 用于任务同步的 CountDownLatch
        CountDownLatch latch = new CountDownLatch(99);

        // 提交分页查询任务
        for (int i = 1; i <= 99; i++) {
            final int pageNo = i;

            executorService.submit(() -> {
                try {
                    if (resultPageNo.get() != -1) {
                        // 如果已经找到目标页，直接返回
                        return;
                    }

                    // 构造查询条件
                    LambdaQueryWrapper<DcTopic> dcTopicLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    if (chapterType != null && !chapterType.isEmpty()) {
                        dcTopicLambdaQueryWrapper.eq(DcTopic::getChapterType, chapterType);
                    }
                    dcTopicLambdaQueryWrapper.orderByAsc(DcTopic::getTopicRank);
                    dcTopicLambdaQueryWrapper.select(DcTopic::getId);

                    // 分页查询
                    Page<DcTopic> page = new Page<>(pageNo, pageSize);
                    IPage<DcTopic> dcTopicPage = dcTopicMapper.selectPage(page, dcTopicLambdaQueryWrapper);

                    // 提取 ID 并检查是否包含目标 ID
                    Set<Integer> idSet = dcTopicPage.getRecords().stream()
                            .map(DcTopic::getId)
                            .collect(Collectors.toSet());

                    if (idSet.contains(targetId)) {
                        // 如果找到了目标 ID，记录页号
                        resultPageNo.compareAndSet(-1, pageNo);
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有任务完成
        latch.await();

        // 关闭线程池
        executorService.shutdown();

        // 返回找到的页号
        return resultPageNo.get() == -1 ? null : resultPageNo.get();
    }


    @Override
    public DcTopicAnswerRecord createDcTopicAnswerRecord(DcTopicAnswerRecord data) {
        data.setAnswerId(TemplateSessionUtils.getCurrentUser().getId());
        boolean rs = dcTopicAnswerRecordMapper.insert(data) > 0;
        log.info("创建 DcTopicAnswerRecord: {}", rs ? "成功" : "失败");
        return data;
    }

    @Override
    public boolean updateDcTopicAnswerRecord(DcTopicAnswerRecord data) {
        boolean rs = dcTopicAnswerRecordMapper.updateById(data) > 0;
        log.info("更新 DcTopicAnswerRecord: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public boolean deleteDcTopicAnswerRecordById(Integer id) {
        boolean rs = dcTopicAnswerRecordMapper.deleteById(id) > 0;
        log.info("删除 DcTopicAnswerRecord: {}", rs ? "成功" : "失败");
        return rs;
    }
}
