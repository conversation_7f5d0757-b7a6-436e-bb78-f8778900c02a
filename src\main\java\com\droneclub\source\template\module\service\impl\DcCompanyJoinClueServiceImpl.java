package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import cn.soulspark.source.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.droneclub.source.template.common.email.EmailConfig;
import com.droneclub.source.template.common.email.EmailSender;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.common.utils.ResourceUtils;
import com.droneclub.source.template.entity.DcCompany;
import com.droneclub.source.template.entity.DcCompanyJoinClue;
import com.droneclub.source.template.mapper.DcCompanyJoinClueMapper;
import com.droneclub.source.template.module.pojo.DcCompanyJoinClueListSearch;
import com.droneclub.source.template.module.pojo.DcCompanyJoinClueVO;
import com.droneclub.source.template.module.service.IDcCompanyJoinClueService;
import com.droneclub.source.template.module.service.IDcCompanyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcCompanyJoinClueServiceImpl implements IDcCompanyJoinClueService {

    private final DcCompanyJoinClueMapper dcCompanyJoinClueMapper;
    private final IDcCompanyService dcCompanyService;
    private final EmailSender emailSender;
    private final EmailConfig emailConfig;

    @Override
    public ListData<DcCompanyJoinClueVO> getDcCompanyJoinClueList(DcCompanyJoinClueListSearch params) {
        LambdaQueryWrapper<DcCompanyJoinClue> queryWrapper = new LambdaQueryWrapper<>();
        if (params.getCompanyId() != null) {
            queryWrapper.eq(DcCompanyJoinClue::getCompanyId, params.getCompanyId());
        }
        if (params.getCreateUser() != null) {
            queryWrapper.eq(DcCompanyJoinClue::getCreateUser, params.getCreateUser());
        }
        // 按创建时间搜索
        if (StringUtils.isValid(params.getCreateStartTime()) && StringUtils.isValid(params.getCreateEndTime())) {
            queryWrapper.ge(DcCompanyJoinClue::getCreateTime, params.getCreateStartTime())
                    .le(DcCompanyJoinClue::getCreateTime, params.getCreateEndTime());
        }
        queryWrapper.orderByDesc(DcCompanyJoinClue::getCreateTime);
        // 查询总数
        Long total = dcCompanyJoinClueMapper.selectCount(queryWrapper);

        // 分页查询
        Page<DcCompanyJoinClue> page = new Page<>(params.getPageNo(), params.getPageSize());
        IPage<DcCompanyJoinClue> dcCompanyJoinCluePage = dcCompanyJoinClueMapper.selectPage(page, queryWrapper);
        List<DcCompanyJoinClue> list = dcCompanyJoinCluePage.getRecords();
        List<DcCompanyJoinClueVO> listVO = list.stream()
                .map(dcCompanyJoinClue -> JSONObject.parseObject(JSONObject.toJSONString(dcCompanyJoinClue), DcCompanyJoinClueVO.class))
                .collect(Collectors.toList());
        // 添加商户名称
        Set<Integer> companyIds = listVO.stream().map(DcCompanyJoinClueVO::getCompanyId).collect(Collectors.toSet());
        List<DcCompany> companies = dcCompanyService.getCompanyList(companyIds);
        Map<Integer, DcCompany> companyMap = new HashMap<>();
        if (companies.size() > 0) {
            companyMap = companies.stream().collect(Collectors.toMap(DcCompany::getId, v -> v));
        }
        for (DcCompanyJoinClueVO dcCompanyJoinClueVO : listVO) {
            DcCompany dcCompany = companyMap.get(dcCompanyJoinClueVO.getCompanyId());
            if (dcCompany != null) {
                dcCompanyJoinClueVO.setCompanyName(dcCompany.getCompanyName());
            }
        }
        return new ListData<>(listVO, total, params.getPageNo(), params.getPageSize());
    }

    @Override
    public DcCompanyJoinClueVO getDcCompanyJoinClueById(Integer id) {
        DcCompanyJoinClue dcCompanyJoinClue = dcCompanyJoinClueMapper.selectById(id);
        if (dcCompanyJoinClue == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        return JSONObject.parseObject(JSONObject.toJSONString(dcCompanyJoinClue), DcCompanyJoinClueVO.class);
    }


    @Override
    public DcCompanyJoinClue createDcCompanyJoinClue(DcCompanyJoinClue data) {
        boolean rs = dcCompanyJoinClueMapper.insert(data) > 0;
        log.info("创建 DcCompanyJoinClue: {}", rs ? "成功" : "失败");
        sendEmail(data);
        return data;
    }

    private void sendEmail(DcCompanyJoinClue data) {
        if (StringUtils.isValid(data.getLinkman())) {
            String emailTemplate = ResourceUtils.loadResource("emailTemplate/hezuo-notice.html");
            if (emailTemplate != null) {
                emailTemplate = emailTemplate.replaceAll("#\\{name}", data.getLinkman());
                String subject = String.format("新的行业合作线索-%s", data.getLinkman());
                emailSender.createMessage(emailConfig.getReceiver())
                        .subject(subject)
                        .htmlContent(emailTemplate)
                        .send();
                log.info("邮件通知成功: {}", subject);
            }
        }
    }

    @Override
    public boolean updateDcCompanyJoinClue(DcCompanyJoinClue data) {
        boolean rs = dcCompanyJoinClueMapper.updateById(data) > 0;
        log.info("更新 DcCompanyJoinClue: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public boolean deleteDcCompanyJoinClueById(Integer id) {
        boolean rs = dcCompanyJoinClueMapper.deleteById(id) > 0;
        log.info("删除 DcCompanyJoinClue: {}", rs ? "成功" : "失败");
        return rs;
    }
}
