package com.droneclub.source.template.common.model;

import com.droneclub.source.template.entity.DcCompany;
import com.droneclub.source.template.entity.DcMemberOpen;
import com.droneclub.source.template.entity.User;
import com.droneclub.source.template.module.pojo.DcAuthInfoVO;
import com.droneclub.source.template.system.dto.UserAuthDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 当前登录用户信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TemplateCurrentUser implements Serializable {
    private Integer id;
    private String name;
    private Integer authId;
    private Integer authStatus;
    private Integer activeState;
    private String account;
    private String token;
    /**
     * 会员类型
     */
    private String memberType;
    /**
     * 会员到期时间
     */
    private String memberDueDate;
    /**
     * 角色列表
     */
    private Set<String> roleCodes;
    /**
     * 权限数据
     */
    private List<UserAuthDTO> userAuths;
    /**
     * 认证数据
     */
    private DcAuthInfoVO authInfo;
    /**
     * 附属统计信息
     */
    private long viewedNum;
    private long beCollectedNum;
    private long beViewedNum;
    /**
     * 联系手机号: 客服电话
     */
    private String contactPhone;
    private String avatar;
    private String regularPlace;
    private String expertise;
    private String certificateType;
    private String certificateStatus;
    /**
     * 用户所属机构信息
     */
    private DcCompany company;
    /**
     * 用户所属培训机构信息
     */
    private DcCompany pxCompany;

    /**
     * 选择的培训机构ID
     */
    private Integer pxCompanyId;
    /**
     * 培训所在城市
     */
    private String pxCity;
    /**
     * 用户选择的所属培训机构信息
     */
    private DcCompany selectPxCompany;

    /**
     * 个人会员信息
     */
    private DcMemberOpen personalMemberOpen;
    /**
     * 归属企业会员信息
     */
    private DcMemberOpen companyMemberOpen;
    /**
     * 归属培训机构会员信息
     */
    private DcMemberOpen pxCompanyMemberOpen;

    public TemplateCurrentUser(User user) {
        this.id = user.getId();
        this.name = user.getUserName();
        this.authId = user.getAuthId();
        this.activeState = user.getActiveState();
        this.account = user.getAccount();
    }
}
