package com.droneclub.source.template.system.pojo;

import cn.soulspark.source.common.utils.StringUtils;
import com.droneclub.source.template.common.utils.CamelCaseConverterUtils;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class ColumnMetadata {
    private static final Map<String, String> TYPE_MAPPING = new HashMap<>();

    static {
        TYPE_MAPPING.put("int", "Integer");
        TYPE_MAPPING.put("varchar", "String");
        TYPE_MAPPING.put("text", "String");
        TYPE_MAPPING.put("datetime", "String");
    }

    private String field;
    private String type;
    private String javaType;

    public void setField(String field) {
        this.field = CamelCaseConverterUtils.toCamelCase(field);
    }

    public void setType(String columnType) {
        this.type = columnType;
        for (String type : TYPE_MAPPING.keySet()) {
            if (columnType.startsWith(type)) {
                this.javaType = TYPE_MAPPING.get(type);
                break;
            }
        }
        if (StringUtils.isInvalid(this.javaType)) {
            this.javaType = "String";
        }
    }
}
