package com.droneclub.source.template.common.config;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 微信 配置类
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "wechat")
public class WechatConfig {

    public static WechatConfig config;

    /**
     * appId
     */
    private String appId;

    /**
     * secret
     */
    private String secret;

    /**
     * 小程序状态 developer:开发版; trial:体验版; formal:正式版; 默认为正式版
     */
    private String programState;

    /**
     * 小程序首页
     */
    private String indexPage;

    /**
     * 获取访问令牌
     */
    private String accessTokenUrl;
    /**
     * 内容审核
     */
    private String contentAuditUrl;
    /**
     * 音视频审核
     */
    private String mediaAuditUrl;

    /**
     * 获取用户手机号
     */
    private String getUserPhoneUrl;

    /**
     * 获取用户登录session信息
     */
    private String getUserLoginSessionUrl;

    /**
     * 发送消息
     */
    private String sendMessageUrl;

    @PostConstruct
    private void initWechatConfig() {
        config = this;
        log.info("init wechat config success.");
    }
}