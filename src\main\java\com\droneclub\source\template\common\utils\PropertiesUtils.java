package com.droneclub.source.template.common.utils;

import cn.hutool.core.io.resource.ClassPathResource;

import java.io.IOException;
import java.util.Properties;

public class PropertiesUtils {

    /**
     * 根据key获取properties的value
     *
     * @param path 路径
     * @param key  key
     * @return value
     */
    public static String getValueByKey(String path, String key) {
        ClassPathResource resource = new ClassPathResource(path);
        Properties properties = new Properties();
        try {
            properties.load(resource.getStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return (String) properties.get(key);
    }
}
