package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("dc_bx_apply")
public class DcBxApply {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String js;
    private String sz;
    private String rs;
    private String phone;
    private Integer createUser;
    private String createTime;
    private Integer updateUser;
    private String updateTime;
    private String isDelete;

}
