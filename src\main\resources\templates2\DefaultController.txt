package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.${Module};
import com.droneclub.source.template.module.pojo.${Module}VO;
import com.droneclub.source.template.module.pojo.${Module}ListSearch;
import com.droneclub.source.template.module.service.I${Module}Service;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/${module}")
public class ${Module}Controller {

    private final I${Module}Service ${module}Service;

    @GetMapping("/get${Module}List")
    public RestResult<ListData<${Module}VO>> get${Module}List(${Module}ListSearch params) {
        return RestResult.success(${module}Service.get${Module}List(params));
    }

    @GetMapping("/get${Module}ById")
    public RestResult<${Module}VO> get${Module}ById(Integer id) {
        return RestResult.success(${module}Service.get${Module}ById(id));
    }

    @PostMapping("/create${Module}")
    public RestResult<${Module}> create${Module}(@RequestBody ${Module} data) {
        return RestResult.success(${module}Service.create${Module}(data));
    }

    @PutMapping("/update${Module}")
    public RestResult<Boolean> update${Module}(@RequestBody ${Module} data) {
        return RestResult.success(${module}Service.update${Module}(data));
    }

    @DeleteMapping("/delete${Module}ById")
    public RestResult<Boolean> delete${Module}ById(Integer id) {
        return RestResult.success(${module}Service.delete${Module}ById(id));
    }
}
