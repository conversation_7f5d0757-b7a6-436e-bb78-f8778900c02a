package com.droneclub.source.template.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("dc_company_product")
public class DcCompanyProduct {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer companyId;
    private String productName;
    private String productPrice;
    private String productProfiles;
    private Integer productStatus;
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    private String createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer updateUser;
    private String updateTime;
    private String isDelete;
    /**
     * 文件信息
     */
    @TableField(exist = false)
    private JSONObject files;

}
