package com.droneclub.source.template.module.service;

import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcTrainingApply;
import com.droneclub.source.template.module.pojo.DcTrainingApplyListSearch;
import com.droneclub.source.template.module.pojo.DcTrainingApplyVO;

public interface IDcTrainingApplyService {

    ListData<DcTrainingApplyVO> getDcTrainingApplyList(DcTrainingApplyListSearch params);

    DcTrainingApplyVO getDcTrainingApplyById(Integer id);

    DcTrainingApply createDcTrainingApply(DcTrainingApply data);

    boolean updateDcTrainingApply(DcTrainingApply data);

    boolean deleteDcTrainingApplyById(Integer id);
}
