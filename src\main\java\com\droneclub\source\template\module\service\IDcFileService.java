package com.droneclub.source.template.module.service;

import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcFile;
import com.droneclub.source.template.module.pojo.DcFileListSearch;
import com.droneclub.source.template.module.pojo.DcFileVO;

import java.util.List;

public interface IDcFileService {

    ListData<DcFileVO> getDcFileList(DcFileListSearch params);

    DcFileVO getDcFileById(Integer id);

    DcFile createDcFile(DcFile data);

    boolean updateDcFile(DcFile data);

    boolean deleteDcFileById(Integer id);

    void bindFile(String moduleType, String fileType, Integer businessId, List<String> file);

    List<DcFile> getFileList(String moduleType, String fileType, Integer businessId);

    List<DcFile> getFileList(String moduleType, Integer businessId);
}
