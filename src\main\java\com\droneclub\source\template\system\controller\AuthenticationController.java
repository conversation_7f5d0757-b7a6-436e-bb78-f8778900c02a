package com.droneclub.source.template.system.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.common.model.TemplateCurrentUser;
import com.droneclub.source.template.common.utils.TemplateSessionUtils;
import com.droneclub.source.template.system.pojo.LoginResult;
import com.droneclub.source.template.system.service.IAuthService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/auth")
public class AuthenticationController {

    private final IAuthService authenticationService;

    @GetMapping("/wx/login")
    public RestResult<LoginResult> wxLogin(String account) {
        return RestResult.success(authenticationService.wxLogin(account));
    }

    @GetMapping("/logout")
    public RestResult<Boolean> logout() {
        return RestResult.success(authenticationService.logout(TemplateSessionUtils.getCurrentUser().getId()));
    }

    @GetMapping("/getCurrentUser")
    public RestResult<TemplateCurrentUser> getCurrentUser() {
        return RestResult.success(authenticationService.getCurrentUser());
    }
}
