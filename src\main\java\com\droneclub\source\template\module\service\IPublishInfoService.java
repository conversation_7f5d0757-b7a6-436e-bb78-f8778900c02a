package com.droneclub.source.template.module.service;

import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.PublishInfo;
import com.droneclub.source.template.module.pojo.PublishInfoListSearch;
import com.droneclub.source.template.module.pojo.PublishInfoVO;

public interface IPublishInfoService {

    ListData<PublishInfoVO> getPublishInfoList(PublishInfoListSearch params);

    PublishInfoVO getPublishInfoById(Integer id);

    PublishInfo createPublishInfo(PublishInfo data);

    boolean updatePublishInfo(PublishInfo data);

    boolean deletePublishInfoById(Integer id);
}
