package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import cn.soulspark.source.common.utils.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.droneclub.source.template.common.email.EmailConfig;
import com.droneclub.source.template.common.email.EmailSender;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.common.utils.ResourceUtils;
import com.droneclub.source.template.entity.DcDroneInsuranceEntity;
import com.droneclub.source.template.entity.DcFile;
import com.droneclub.source.template.mapper.DcDroneInsuranceMapper;
import com.droneclub.source.template.module.pojo.DcDroneInsuranceDTO;
import com.droneclub.source.template.module.pojo.DcDroneInsuranceQuery;
import com.droneclub.source.template.module.pojo.DcDroneInsuranceVO;
import com.droneclub.source.template.module.service.IDcDroneInsuranceService;
import com.droneclub.source.template.module.service.IDcFileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcDroneInsuranceServiceImpl implements IDcDroneInsuranceService {

    private final DcDroneInsuranceMapper dcDroneInsuranceMapper;
    private final IDcFileService dcFileService;
    private final EmailSender emailSender;
    private final EmailConfig emailConfig;

    @Override
    public ListData<DcDroneInsuranceVO> listDcDroneInsuranceByPage(DcDroneInsuranceQuery params) {
        QueryWrapper<DcDroneInsuranceEntity> queryWrapper = new QueryWrapper<>();
        if (params.getInsuranceStatus() != null) {
            queryWrapper.eq("insurance_status", params.getInsuranceStatus());
        }
        if (StringUtils.isValid(params.getInsuredName())) {
            queryWrapper.like("insured_name", params.getInsuredName());
        }
        if (StringUtils.isValid(params.getMachineSn())) {
            queryWrapper.eq("machine_sn", params.getMachineSn());
        }

        // 查询总数
        Long total = dcDroneInsuranceMapper.selectCount(queryWrapper);
        queryWrapper.orderByDesc("create_time");

        // 分页查询
        Page<DcDroneInsuranceEntity> page = new Page<>(params.getPageNo(), params.getPageSize());
        IPage<DcDroneInsuranceEntity> droneInsurancePage = dcDroneInsuranceMapper.selectPage(page, queryWrapper);
        List<DcDroneInsuranceEntity> list = droneInsurancePage.getRecords();

        // 转换为VO并构建额外信息
        List<DcDroneInsuranceVO> listVO = list.stream()
                .map(entity -> JSONObject.parseObject(JSONObject.toJSONString(entity), DcDroneInsuranceVO.class))
                .collect(Collectors.toList());
        for (DcDroneInsuranceVO vo : listVO) {
            buildExtraInfo(vo);
        }

        return new ListData<>(listVO, total, params.getPageNo(), params.getPageSize());
    }

    private void buildExtraInfo(DcDroneInsuranceVO vo) {
        List<DcFile> files = dcFileService.getFileList("drone_insurance", vo.getId());
        Map<String, List<DcFile>> fileTypeMap = files.stream().collect(Collectors.groupingBy(DcFile::getFileType));
        JSONObject filesJson = new JSONObject();
        for (String fileType : fileTypeMap.keySet()) {
            filesJson.put(fileType, fileTypeMap.get(fileType).stream()
                    .map(DcFile::getFilePath)
                    .collect(Collectors.toList()));
        }
        vo.setFiles(filesJson);
    }

    @Override
    public DcDroneInsuranceVO getDcDroneInsuranceById(Integer id) {
        DcDroneInsuranceEntity entity = dcDroneInsuranceMapper.selectById(id);
        if (entity == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关无人机保险数据");
        }
        DcDroneInsuranceVO vo = JSONObject.parseObject(JSONObject.toJSONString(entity), DcDroneInsuranceVO.class);
        buildExtraInfo(vo);
        return vo;
    }

    @Override
    public DcDroneInsuranceEntity createDcDroneInsurance(DcDroneInsuranceDTO saveData) {
        DcDroneInsuranceEntity data = JSONObject.parseObject(JSONObject.toJSONString(saveData), DcDroneInsuranceEntity.class);
        // 设置初始状态为待审核
        data.setInsuranceStatus("1");

        boolean success = dcDroneInsuranceMapper.insert(data) > 0;
        log.info("创建无人机保险记录: {}", success ? "成功" : "失败");

        if (success) {
            // 绑定文件
            bindFile(data);
            // 发送通知邮件
            sendInsuranceEmail(data);
        }

        return data;
    }

    private void sendInsuranceEmail(DcDroneInsuranceEntity data) {
        if (StringUtils.isValid(data.getInsuredName()) && StringUtils.isValid(data.getInsuranceType())) {
            String emailTemplate = ResourceUtils.loadResource("emailTemplate/drone-insurance-notice.html");
            if (emailTemplate != null) {
                emailTemplate = emailTemplate.replaceAll("#\\{phone}", data.getPhone());

                String subject = String.format("新的无人机投保线索-%s", data.getInsuredName());
                emailSender.createMessage(emailConfig.getReceiver())
                        .subject(subject)
                        .htmlContent(emailTemplate)
                        .send();
                log.info("新的无人机投保线索通知邮件发送成功: {}", subject);
            }
        }
    }

    private void bindFile(DcDroneInsuranceEntity data) {
        if (data.getFiles() == null || data.getFiles().isEmpty()) {
            return;
        }
        for (String key : data.getFiles().keySet()) {
            dcFileService.bindFile("drone_insurance", key, data.getId(),
                    JSONArray.parseArray(data.getFiles().getJSONArray(key).toJSONString(), String.class)
            );
        }
    }

    @Override
    public boolean updateDcDroneInsurance(DcDroneInsuranceDTO updateData) {
        DcDroneInsuranceEntity data = JSONObject.parseObject(JSONObject.toJSONString(updateData), DcDroneInsuranceEntity.class);
        boolean success = dcDroneInsuranceMapper.updateById(data) > 0;
        log.info("更新无人机保险记录: {}", success ? "成功" : "失败");

        if (success) {
            bindFile(data);
        }

        return success;
    }

    @Override
    public boolean deleteDcDroneInsurance(Integer id) {
        boolean success = dcDroneInsuranceMapper.deleteById(id) > 0;
        log.info("删除无人机保险记录: {}", success ? "成功" : "失败");
        return success;
    }
}