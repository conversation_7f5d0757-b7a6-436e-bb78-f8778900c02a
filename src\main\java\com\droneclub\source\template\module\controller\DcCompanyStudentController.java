package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.alibaba.fastjson.JSONObject;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcCompanyStudent;
import com.droneclub.source.template.module.pojo.DcOrganizationStudentSearch;
import com.droneclub.source.template.module.pojo.DcOrganizationStudentVO;
import com.droneclub.source.template.module.service.IDcCompanyStudentService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dcCompanyStudent")
public class DcCompanyStudentController {

    private final IDcCompanyStudentService studentService;

    @GetMapping("/getStudentList")
    public RestResult<ListData<DcOrganizationStudentVO>> getStudentList(DcOrganizationStudentSearch params) {
        return RestResult.success(studentService.getStudentList(params));
    }

    @GetMapping("/getStudentById")
    public RestResult<DcOrganizationStudentVO> getStudentById(Integer id) {
        return RestResult.success(studentService.getStudentById(id));
    }

    @PostMapping("/createStudent")
    public RestResult<DcCompanyStudent> createStudent(@RequestBody DcCompanyStudent data) {
        return RestResult.success(studentService.createStudent(data));
    }

    @PutMapping("/updateStudent")
    public RestResult<Boolean> updateStudent(@RequestBody DcCompanyStudent data) {
        return RestResult.success(studentService.updateStudent(data));
    }

    @DeleteMapping("/deleteStudentById")
    public RestResult<Boolean> deleteStudentById(Integer id) {
        return RestResult.success(studentService.deleteStudentById(id));
    }
} 