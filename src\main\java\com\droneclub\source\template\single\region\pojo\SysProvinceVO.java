package com.droneclub.source.template.single.region.pojo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 省份信息VO
 */
@Data
public class SysProvinceVO {
    /**
     * 数据ID
     */
    private Long id;
    
    /**
     * 省份编码
     */
    private String provinceCode;
    
    /**
     * 省份名称
     */
    private String provinceName;
    
    /**
     * 首字母
     */
    private String firstChar;
    
    /**
     * 区号
     */
    private String cityCode;
    
    /**
     * 中心点坐标
     */
    private String center;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 