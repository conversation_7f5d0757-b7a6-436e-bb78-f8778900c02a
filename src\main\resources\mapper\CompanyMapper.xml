<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.droneclub.source.template.mapper.DcCompanyMapper">

    <insert id="addLookNum">
        UPDATE dc_company
        SET look_num = look_num + 1
        WHERE id = #{companyId};
    </insert>
    <!-- 公共查询条件 -->
    <sql id="dcCompanyQueryConditions">
        where is_delete = 0 AND show_list != 0
        <if test="companyId != null">
            AND id = #{companyId}
        </if>
        <if test="companyName != null and companyName != ''">
            AND company_name LIKE concat('%', #{companyName}, '%')
        </if>
        <if test="contactWay != null and contactWay != ''">
            AND contact_way = #{contactWay}
        </if>
        <if test="industry != null and industry != ''">
            AND id IN (
            SELECT company_id
            FROM dc_company_industry
            WHERE industry LIKE concat('%', #{industry}, '%')
            )
        </if>
        <if test="industries != null and industries.size() > 0">
            AND id IN (
            SELECT company_id
            FROM dc_company_industry
            WHERE industry IN
            <foreach item="item" collection="industries" separator="," open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="city != null and city != ''">
            AND city LIKE concat('%', #{city}, '%')
        </if>
        <if test="companyStatus != null">
            AND company_status = #{companyStatus}
        </if>
        <if test="createStartTime != null and createStartTime != '' and createEndTime != null and createEndTime != ''">
            AND create_time &gt;= #{createStartTime}
            AND create_time &lt;= #{createEndTime}
        </if>
    </sql>

    <!-- 查询公司数量 -->
    <select id="getDcCompanyCount" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM dc_company
        <include refid="dcCompanyQueryConditions"/>
    </select>

    <!-- 查询公司列表 -->
    <select id="getDcCompanyList" resultType="com.droneclub.source.template.entity.DcCompany">
        SELECT *
        FROM dc_company
        <include refid="dcCompanyQueryConditions"/>
        <if test="sortType != null">
            ORDER BY
            <choose>
                <when test="sortType == 'default'">
                    create_time ASC
                </when>
                <when test="sortType == 'default_desc'">
                    create_time DESC
                </when>
                <when test="sortType == 'lookNum'">
                    look_num DESC
                </when>
                <otherwise>
                    create_time ASC
                </otherwise>
            </choose>
        </if>
        LIMIT #{offset}, #{pageSize}
    </select>

</mapper>