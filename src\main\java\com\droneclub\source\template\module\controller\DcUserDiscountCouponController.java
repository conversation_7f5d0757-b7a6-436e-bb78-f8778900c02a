package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.execption.ZkException;
import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.common.model.TemplateCurrentUser;
import com.droneclub.source.template.common.utils.TemplateSessionUtils;
import com.droneclub.source.template.entity.DcUserDiscountCoupon;
import com.droneclub.source.template.module.pojo.DcUserDiscountCouponVO;
import com.droneclub.source.template.module.pojo.DcUserDiscountCouponListSearch;
import com.droneclub.source.template.module.service.IDcUserDiscountCouponService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dcUserDiscountCoupon")
public class DcUserDiscountCouponController {

    private final IDcUserDiscountCouponService dcUserDiscountCouponService;

    @GetMapping("/getDcUserDiscountCouponList")
    public RestResult<ListData<DcUserDiscountCouponVO>> getDcUserDiscountCouponList(DcUserDiscountCouponListSearch params) {
        TemplateCurrentUser currentUser = TemplateSessionUtils.getCurrentUser();
        if (currentUser != null) {
            params.setUserId(currentUser.getId());
        } else {
            throw new ZkException("用户未登录, 请登录后查看.");
        }
        return RestResult.success(dcUserDiscountCouponService.getDcUserDiscountCouponList(params));
    }

    @GetMapping("/getWebDcUserDiscountCouponList")
    public RestResult<ListData<DcUserDiscountCouponVO>> getWebDcUserDiscountCouponList(DcUserDiscountCouponListSearch params) {
        return RestResult.success(dcUserDiscountCouponService.getDcUserDiscountCouponList(params));
    }



    @GetMapping("/getDcUserDiscountCouponById")
    public RestResult<DcUserDiscountCouponVO> getDcUserDiscountCouponById(Integer id) {
        return RestResult.success(dcUserDiscountCouponService.getDcUserDiscountCouponById(id));
    }

    @PostMapping("/createDcUserDiscountCoupon")
    public RestResult<DcUserDiscountCoupon> createDcUserDiscountCoupon(@RequestBody DcUserDiscountCoupon data) {
        return RestResult.success(dcUserDiscountCouponService.createDcUserDiscountCoupon(data));
    }

    @PutMapping("/updateDcUserDiscountCoupon")
    public RestResult<Boolean> updateDcUserDiscountCoupon(@RequestBody DcUserDiscountCoupon data) {
        return RestResult.success(dcUserDiscountCouponService.updateDcUserDiscountCoupon(data));
    }

    @DeleteMapping("/deleteDcUserDiscountCouponById")
    public RestResult<Boolean> deleteDcUserDiscountCouponById(Integer id) {
        return RestResult.success(dcUserDiscountCouponService.deleteDcUserDiscountCouponById(id));
    }
}
