﻿<!DOCTYPE html>
<html>
  <head>
    <title>未完成作业详情</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/未完成作业详情/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/未完成作业详情/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u217" class="ax_default flow_shape">
        <div id="u217_div" class=""></div>
        <div id="u217_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u218" class="ax_default flow_shape">
        <div id="u218_div" class=""></div>
        <div id="u218_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u219" class="ax_default label">
        <div id="u219_div" class=""></div>
        <div id="u219_text" class="text ">
          <p><span>学员作业详情</span></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u220" class="ax_default _图片_">
        <img id="u220_img" class="img " src="images/作业布置/u38.png"/>
        <div id="u220_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u221" class="ax_default box_1">
        <div id="u221_div" class=""></div>
        <div id="u221_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (垂直线) -->
      <div id="u222" class="ax_default line">
        <img id="u222_img" class="img " src="images/创建作业/u147.svg"/>
        <div id="u222_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u223" class="ax_default label">
        <div id="u223_div" class=""></div>
        <div id="u223_text" class="text ">
          <p><span>基本信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u224" class="ax_default label">
        <div id="u224_div" class=""></div>
        <div id="u224_text" class="text ">
          <p><span>学员名称</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u225" class="ax_default line">
        <img id="u225_img" class="img " src="images/创建作业/u150.svg"/>
        <div id="u225_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u226" class="ax_default label">
        <div id="u226_div" class=""></div>
        <div id="u226_text" class="text ">
          <p><span>测试学员</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u227" class="ax_default label">
        <div id="u227_div" class=""></div>
        <div id="u227_text" class="text ">
          <p><span>作业完成截止日</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u228" class="ax_default label">
        <div id="u228_div" class=""></div>
        <div id="u228_text" class="text ">
          <p><span>2025-06-20</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u229" class="ax_default box_1">
        <div id="u229_div" class=""></div>
        <div id="u229_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (垂直线) -->
      <div id="u230" class="ax_default line">
        <img id="u230_img" class="img " src="images/创建作业/u147.svg"/>
        <div id="u230_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u231" class="ax_default label">
        <div id="u231_div" class=""></div>
        <div id="u231_text" class="text ">
          <p><span>作业信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u232" class="ax_default label">
        <div id="u232_div" class=""></div>
        <div id="u232_text" class="text ">
          <p><span>作业类型</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u233" class="ax_default line">
        <img id="u233_img" class="img " src="images/创建作业/u150.svg"/>
        <div id="u233_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u234" class="ax_default label">
        <div id="u234_div" class=""></div>
        <div id="u234_text" class="text ">
          <p><span>理论练习</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u235" class="ax_default label">
        <div id="u235_div" class=""></div>
        <div id="u235_text" class="text ">
          <p><span>作业章节</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u236" class="ax_default label">
        <div id="u236_div" class=""></div>
        <div id="u236_text" class="text ">
          <p><span>第一章 无人机理论</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u237" class="ax_default _文本段落">
        <div id="u237_div" class=""></div>
        <div id="u237_text" class="text ">
          <p><span>第二章 测试章节名称</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u238" class="ax_default sticky_1">
        <div id="u238_div" class=""></div>
        <div id="u238_text" class="text ">
          <p><span>1.若作业进度为未开始，点击提醒TA的交互跟列表一致；点击的时候后端最好做下判断，若状态变更，前端展示toast“当前数据发生变化，将自动为您刷新”并刷新下页面</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u239" class="ax_default label">
        <div id="u239_div" class=""></div>
        <div id="u239_text" class="text ">
          <p><span>作业进度</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u240" class="ax_default line">
        <img id="u240_img" class="img " src="images/创建作业/u150.svg"/>
        <div id="u240_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u241" class="ax_default label">
        <div id="u241_div" class=""></div>
        <div id="u241_text" class="text ">
          <p><span>未开始</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u242" class="ax_default primary_button">
        <div id="u242_div" class=""></div>
        <div id="u242_text" class="text ">
          <p><span>提醒TA</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
