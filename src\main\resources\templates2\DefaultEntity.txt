package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("${tableName}")
public class ${Module} {

    @TableId(type = IdType.AUTO)
    <#list columns as column>
    private ${column.javaType} ${column.field};
    </#list>

}
