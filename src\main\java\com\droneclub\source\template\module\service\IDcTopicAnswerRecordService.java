package com.droneclub.source.template.module.service;

import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcTopicAnswerRecord;
import com.droneclub.source.template.module.pojo.DcTopicAnswerRecordListSearch;
import com.droneclub.source.template.module.pojo.DcTopicAnswerRecordVO;

public interface IDcTopicAnswerRecordService {

    ListData<DcTopicAnswerRecordVO> getDcTopicAnswerRecordList(DcTopicAnswerRecordListSearch params);

    DcTopicAnswerRecordVO getDcTopicAnswerRecordById(Integer id);

    DcTopicAnswerRecord getDcTopicAnswerLatestRecord(String chapterType, Integer pageSize);

    DcTopicAnswerRecord createDcTopicAnswerRecord(DcTopicAnswerRecord data);

    boolean updateDcTopicAnswerRecord(DcTopicAnswerRecord data);

    boolean deleteDcTopicAnswerRecordById(Integer id);
}
