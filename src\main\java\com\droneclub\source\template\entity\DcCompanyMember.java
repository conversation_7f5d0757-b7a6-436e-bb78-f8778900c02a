package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("dc_company_member")
public class DcCompanyMember {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    private Integer companyId;
    
    private Integer userId;
    
    private String memberName;

    @TableField(exist = false)
    private String roleCodes;

    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;

    private String createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer updateUser;

    private String updateTime;
    
    private Integer isDelete;
} 