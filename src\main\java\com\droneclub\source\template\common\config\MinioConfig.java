package com.droneclub.source.template.common.config;

import com.droneclub.source.template.common.utils.MinioUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;


/**
 * redis 配置类
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "minio")
public class MinioConfig {
    /**
     * minio服务地址
     */
    private String serverUrl;

    /**
     * bucket
     */
    private String bucket;

    /**
     * minio文件预览地址
     */
    private String previewUrl;

    /**
     * accessKey
     */
    private String accessKey;

    /**
     * secretKey
     */
    private String secretKey;


    @PostConstruct
    private void initMinioUtils() {
        MinioUtils.getMinioUtils().initMinioUtils(this.serverUrl, this.bucket, this.previewUrl, this.accessKey, this.secretKey);
        log.info("init minio utils success.");
    }
}