package com.droneclub.source.template.common.utils;

/**
 * 首字母转换
 */
public class CapitalizeConverterUtils {

    public static String capitalize(String input) {
        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = true;

        for (int i = 0; i < input.length(); i++) {
            char currentChar = input.charAt(i);

            if (Character.isWhitespace(currentChar)) {
                capitalizeNext = true;
            } else if (capitalizeNext) {
                result.append(Character.toUpperCase(currentChar));
                capitalizeNext = false;
            } else {
                result.append(currentChar);
            }
        }

        return result.toString();
    }

}
