package com.droneclub.source.template.common.cache;

import com.droneclub.source.template.entity.DcCompany;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class CompanyCache {
    // 静态Map存储数据，Key: company_code, Value: DcCompany对象
    public static final Map<String, DcCompany> COMPANY_MAP = new ConcurrentHashMap<>();

    // 线程变量
    private static final ThreadLocal<DcCompany> companyThreadLocal = new ThreadLocal<>();

    public static DcCompany getDcCompany() {
        return companyThreadLocal.get();
    }

    public static void setDcCompany(DcCompany company) {
        companyThreadLocal.set(company);
    }

    public static void removeCurrentUser() {
        companyThreadLocal.remove();
    }
}
