package com.droneclub.source.template.system.service.impl;

import cn.soulspark.source.common.execption.ZkException;
import cn.soulspark.source.common.utils.StringUtils;
import com.droneclub.source.template.common.utils.FileUtils;
import com.droneclub.source.template.common.utils.IOUtils;
import com.droneclub.source.template.common.utils.MinioUtils;
import com.droneclub.source.template.common.utils.WebpUtils;
import com.droneclub.source.template.system.pojo.FileInfo;
import com.droneclub.source.template.system.service.IFileService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

import static com.droneclub.source.template.common.utils.WebpUtils.WEBP_FORMAT;

/**
 * 文件服务层
 */
@Service
public class FileServiceImpl implements IFileService {

    /**
     * webp content type
     */
    public static final String WEBP_CONTENT_TYPE = "image/webp";

    @Override
    public FileInfo upload(FileInfo fileInfo, MultipartFile file) {
        ByteArrayInputStream in;
        try (InputStream fileInputStream = file.getInputStream()) {
            in = new ByteArrayInputStream(IOUtils.toByteArray(fileInputStream));
            return upload(false, fileInfo, in);
        } catch (IOException e) {
            throw new ZkException("文件上传失败");
        }
    }

    public FileInfo upload(boolean convertWebp, FileInfo fileInfo, InputStream fileIn) {
        InputStream convertIn = null;
        if (convertWebp) {
            convertIn = WebpUtils.encode(fileInfo.getFilePath(), fileIn);
            fileInfo.setContentType(WEBP_CONTENT_TYPE);
            String fileName = fileInfo.getFileName();
            fileName = fileName.substring(0, fileName.lastIndexOf(".") + 1) + WEBP_FORMAT;
            fileInfo.setFileName(fileName);
            String filePath = fileInfo.getFilePath();
            filePath = filePath.substring(0, filePath.lastIndexOf(".") + 1) + WEBP_FORMAT;
            fileInfo.setFilePath(filePath);
        }
        String newPath = MinioUtils
                .getMinioUtils()
                .upload(MinioUtils.BUCKET, fileInfo.getFilePath(), fileInfo.getContentType(), convertWebp ? convertIn : fileIn);
        if (StringUtils.isValid(newPath)) {
            fileInfo.setFilePath(newPath);
            fileInfo.setPreviewPath(FileUtils.getFilePreviewUrl(fileInfo));
        } else {
            throw new ZkException("文件上传失败");
        }
        return fileInfo;
    }

    @Override
    public boolean deleteFile(String fileUrl) {
        String objectName = fileUrl.substring(fileUrl.indexOf(MinioUtils.BUCKET) + MinioUtils.BUCKET.length());
        MinioUtils.delete(MinioUtils.BUCKET, objectName);
        return true;
    }

}
