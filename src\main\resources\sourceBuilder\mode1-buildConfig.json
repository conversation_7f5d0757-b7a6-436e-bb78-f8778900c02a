[{"type": "Controller", "template": "DefaultController.txt", "targetPath": "/module/controller/%sController.java"}, {"type": "IService", "template": "DefaultService.txt", "targetPath": "/module/service/I%sService.java"}, {"type": "ServiceImpl", "template": "DefaultServiceImpl.txt", "targetPath": "/module/service/impl/%sServiceImpl.java"}, {"type": "<PERSON><PERSON>", "template": "DefaultMapper.txt", "targetPath": "/mapper/%sMapper.java"}, {"type": "Entity", "template": "DefaultEntity.txt", "targetPath": "/entity/%s.java"}, {"type": "EntityVO", "template": "DefaultEntityVO.txt", "targetPath": "/module/pojo/%sVO.java"}, {"type": "ListSearch", "template": "DefaultListSearch.txt", "targetPath": "/module/pojo/%sListSearch.java"}]