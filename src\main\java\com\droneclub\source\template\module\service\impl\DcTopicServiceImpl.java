package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import cn.soulspark.source.common.utils.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.common.utils.ResourceUtils;
import com.droneclub.source.template.common.utils.TemplateSessionUtils;
import com.droneclub.source.template.entity.*;
import com.droneclub.source.template.mapper.*;
import com.droneclub.source.template.module.pojo.*;
import com.droneclub.source.template.module.service.IDcExamTopicRecordService;
import com.droneclub.source.template.module.service.IDcSysEnumsService;
import com.droneclub.source.template.module.service.IDcTopicService;
import com.droneclub.source.template.timedtask.TopicDeepSeekToolkit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.droneclub.source.template.constants.TopicConstant.ChapterNameClass.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcTopicServiceImpl implements IDcTopicService {

    private final DcTopicMapper dcTopicMapper;
    private final IDcSysEnumsService dcSysEnumsService;
    private final TmUserMapper userMapper;
    private final DcChapterDoTopicRecordMapper dcChapterDoTopicRecordMapper;
    private final DcAllTopicRecordMapper dcAllTopicRecordMapper;
    private final DcExamRecordMapper examRecordMapper;
    private final IDcExamTopicRecordService dcExamTopicRecordService;
    private final DcTopicDetailAnalysisMapper dcTopicDetailAnalysisMapper;


    @Override
    public ListData<DcTopicVO> getDcTopicList(DcTopicListSearch params) {
        LambdaQueryWrapper<DcTopic> queryWrapper = new LambdaQueryWrapper<>();
        Set<String> chapterNameSet = new HashSet<>();
        chapterNameSet.addAll(Arrays.asList(BASE_CLASS));
        chapterNameSet.addAll(Arrays.asList(ZH_CLASS));
        chapterNameSet.addAll(Arrays.asList(JY_CLASS));
        queryWrapper.in(DcTopic::getChapterName, chapterNameSet);
        if (StringUtils.isValid(params.getChapterType())) {
            queryWrapper.eq(DcTopic::getChapterType, params.getChapterType());
        }
        if (StringUtils.isValid(params.getChapterName())) {
            queryWrapper.eq(DcTopic::getChapterName, params.getChapterName());
        }
        if (StringUtils.isValid(params.getTopicName())) {
            queryWrapper.like(DcTopic::getTopicName, params.getTopicName());
        }
        if (params.getTopicStatus() != null) {
            queryWrapper.eq(DcTopic::getTopicStatus, params.getTopicStatus());
        }
        Map<Integer, String> topicIdAnswerMap = new HashMap<>(16);
        if (params.isErrorTopic()) {
            // 查询当前用户的错题id, 作为筛选条件筛选列表
            LambdaQueryWrapper<DcAllTopicRecord> errorTopicRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
            errorTopicRecordLambdaQueryWrapper.eq(DcAllTopicRecord::getUserId, TemplateSessionUtils.getCurrentUser().getId());
            errorTopicRecordLambdaQueryWrapper.eq(DcAllTopicRecord::getAnswerRight, false);
            List<DcAllTopicRecord> errorTopicRecords = dcAllTopicRecordMapper.selectList(errorTopicRecordLambdaQueryWrapper);
            topicIdAnswerMap = errorTopicRecords.stream()
                    .collect(Collectors.toMap(DcAllTopicRecord::getTopicId, DcAllTopicRecord::getUserAnswer));
            if (!topicIdAnswerMap.isEmpty()) {
                queryWrapper.in(DcTopic::getId, topicIdAnswerMap.keySet());
            } else {
                return new ListData<>(new ArrayList<>(), 0L, params.getPageNo(), params.getPageSize());
            }
        }
        // 考试Id不为空, 则获取考试数据
        if (params.getExamId() != null) {
            // 查询抽选题数Id
            List<DcExamTopicRecord> examTopicRecords = dcExamTopicRecordService.getExamTopicRecord(params.getExamId());
            Set<Integer> topicIds = examTopicRecords.stream().map(DcExamTopicRecord::getTopicId).collect(Collectors.toSet());
            if (!topicIds.isEmpty()) {
                queryWrapper.in(DcTopic::getId, topicIds);
            } else {
                return new ListData<>(new ArrayList<>(), 0L, params.getPageNo(), params.getPageSize());
            }
        }
        // 获取考试错题记录
        if (params.isExamErrorTopic()) {
            // 查询抽选题数Id
            List<DcExamTopicRecord> examTopicRecords = dcExamTopicRecordService.getExamErrorTopicRecord();
            Set<Integer> topicIds = examTopicRecords.stream().map(DcExamTopicRecord::getTopicId).collect(Collectors.toSet());
            if (!topicIds.isEmpty()) {
                queryWrapper.in(DcTopic::getId, topicIds);
            } else {
                return new ListData<>(new ArrayList<>(), 0L, params.getPageNo(), params.getPageSize());
            }
        }

        if (params.getExamIdForErrorTopic() != null) {
            // 查询抽选题数Id
            List<DcExamTopicRecord> examTopicRecords = dcExamTopicRecordService.getExamErrorTopicRecord(params.getExamIdForErrorTopic());
            Set<Integer> topicIds = examTopicRecords.stream().map(DcExamTopicRecord::getTopicId).collect(Collectors.toSet());
            if (!topicIds.isEmpty()) {
                queryWrapper.in(DcTopic::getId, topicIds);
            } else {
                return new ListData<>(new ArrayList<>(), 0L, params.getPageNo(), params.getPageSize());
            }
        }
        // 按创建时间搜索
        if (StringUtils.isValid(params.getCreateStartTime()) && StringUtils.isValid(params.getCreateEndTime())) {
            queryWrapper.ge(DcTopic::getCreateTime, params.getCreateStartTime())
                    .le(DcTopic::getCreateTime, params.getCreateEndTime());
        }

        // 按创建时间倒序排序
        queryWrapper.orderByAsc(DcTopic::getTopicRank);
        // 查询总数
        Long total = dcTopicMapper.selectCount(queryWrapper);

        // 分页查询
        Page<DcTopic> page = new Page<>(params.getPageNo(), params.getPageSize());
        // 考试不返回分析
        if (params.getExamId() != null) {
            queryWrapper.select(DcTopic::getId, DcTopic::getChapterType,
                    DcTopic::getChapterName, DcTopic::getTopicName,
                    DcTopic::getTopicRank, DcTopic::getTopicStatus,
                    DcTopic::getTopicOption, DcTopic::getQuestionAnswer,
                    DcTopic::getSfId, DcTopic::getCreateUser,
                    DcTopic::getCreateTime, DcTopic::getUpdateUser,
                    DcTopic::getUpdateTime);
        }

        IPage<DcTopic> dcTopicPage = dcTopicMapper.selectPage(page, queryWrapper);
        List<DcTopic> list = dcTopicPage.getRecords();
        List<DcTopicVO> listVO = list.stream()
                .map(dcTopic -> JSONObject.parseObject(JSONObject.toJSONString(dcTopic), DcTopicVO.class))
                .collect(Collectors.toList());
        Set<Integer> userIds1 = listVO.stream().map(DcTopicVO::getCreateUser).collect(Collectors.toSet());
        Set<Integer> userIds2 = listVO.stream().map(DcTopicVO::getUpdateUser).collect(Collectors.toSet());
        userIds1.addAll(userIds2);
        Map<Integer, User> idUserNameMap = new HashMap<>();
        if (userIds1.size() > 0) {
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            wrapper.in("id", userIds1);
            List<User> existUserList = userMapper.selectList(wrapper);
            idUserNameMap = existUserList.stream()
                    .collect(Collectors.toMap(User::getId, v -> v));
        }
        // 绑定业务数据创建信息
        for (DcTopicVO vo : listVO) {
            if (idUserNameMap.get(vo.getCreateUser()) != null) {
                vo.setCreateUserName(idUserNameMap.get(vo.getCreateUser()).getUserName());
            }
            if (idUserNameMap.get(vo.getUpdateUser()) != null) {
                vo.setUpdateUserName(idUserNameMap.get(vo.getUpdateUser()).getUserName());
            }
            JSONArray jsonArray = JSONArray.parseArray(vo.getTopicOption());
            String sortedJsonArray = jsonArray.stream()
                    .map(Object::toString)
                    .sorted()
                    .collect(Collectors.toCollection(JSONArray::new))
                    .toJSONString();
            vo.setTopicOption(sortedJsonArray);
            // 设置错误选项
            vo.setErrorAnswer(topicIdAnswerMap.get(vo.getId()));
        }
        // 考试Id不为空, 返回随机试题
        List<DcTopicVO> newListVO = new ArrayList<>();
        if (params.getExamId() != null) {
            List<DcTopicVO> nonChapter9Records = listVO.stream()
                    .filter(record -> !record.getChapterName().equals("第九章 综合问答"))
                    .collect(Collectors.toList());

            // 筛选第九章的题目
            List<DcTopicVO> chapter9Records = listVO.stream()
                    .filter(record -> record.getChapterName().equals("第九章 综合问答"))
                    .collect(Collectors.toList());

            Collections.shuffle(nonChapter9Records);
            Collections.shuffle(chapter9Records);

            newListVO.addAll(nonChapter9Records);
            newListVO.addAll(chapter9Records);
        } else {
            newListVO.addAll(listVO);
        }
        return new ListData<>(newListVO, total, params.getPageNo(), params.getPageSize());
    }

    @Override
    public ListData<DcTopicVO> getKsDcTopicList(DcTopicListSearch params) {
        LambdaQueryWrapper<DcTopic> queryWrapper = new LambdaQueryWrapper<>();
        Set<String> chapterNameSet = new HashSet<>(Arrays.asList(KS_CLASS));
        queryWrapper.in(DcTopic::getChapterName, chapterNameSet);
        if (StringUtils.isValid(params.getChapterType())) {
            queryWrapper.eq(DcTopic::getChapterType, params.getChapterType());
        }
        if (StringUtils.isValid(params.getChapterName())) {
            queryWrapper.eq(DcTopic::getChapterName, params.getChapterName());
        }
        if (StringUtils.isValid(params.getTopicName())) {
            queryWrapper.like(DcTopic::getTopicName, params.getTopicName());
        }
        if (params.getTopicStatus() != null) {
            queryWrapper.eq(DcTopic::getTopicStatus, params.getTopicStatus());
        }

        // 按创建时间搜索
        if (StringUtils.isValid(params.getCreateStartTime()) && StringUtils.isValid(params.getCreateEndTime())) {
            queryWrapper.ge(DcTopic::getCreateTime, params.getCreateStartTime())
                    .le(DcTopic::getCreateTime, params.getCreateEndTime());
        }

        // 按创建时间倒序排序
        queryWrapper.orderByAsc(DcTopic::getTopicRank);
        // 查询总数
        Long total = dcTopicMapper.selectCount(queryWrapper);

        // 分页查询
        Page<DcTopic> page = new Page<>(params.getPageNo(), params.getPageSize());
        IPage<DcTopic> dcTopicPage = dcTopicMapper.selectPage(page, queryWrapper);
        List<DcTopic> list = dcTopicPage.getRecords();
        List<DcTopicVO> listVO = list.stream()
                .map(dcTopic -> JSONObject.parseObject(JSONObject.toJSONString(dcTopic), DcTopicVO.class))
                .collect(Collectors.toList());
        Set<Integer> userIds1 = listVO.stream().map(DcTopicVO::getCreateUser).collect(Collectors.toSet());
        Set<Integer> userIds2 = listVO.stream().map(DcTopicVO::getUpdateUser).collect(Collectors.toSet());
        userIds1.addAll(userIds2);
        Map<Integer, User> idUserNameMap = new HashMap<>();
        if (!userIds1.isEmpty()) {
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            wrapper.in("id", userIds1);
            List<User> existUserList = userMapper.selectList(wrapper);
            idUserNameMap = existUserList.stream()
                    .collect(Collectors.toMap(User::getId, v -> v));
        }
        // 绑定业务数据创建信息
        for (DcTopicVO vo : listVO) {
            if (idUserNameMap.get(vo.getCreateUser()) != null) {
                vo.setCreateUserName(idUserNameMap.get(vo.getCreateUser()).getUserName());
            }
            if (idUserNameMap.get(vo.getUpdateUser()) != null) {
                vo.setUpdateUserName(idUserNameMap.get(vo.getUpdateUser()).getUserName());
            }
        }

        return new ListData<>(listVO, total, params.getPageNo(), params.getPageSize());
    }

    @Override
    public DcTopicVO getDcTopicById(Integer id) {
        DcTopic dcTopic = dcTopicMapper.selectById(id);
        if (dcTopic == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        DcTopicVO dcTopicVO = JSONObject.parseObject(JSONObject.toJSONString(dcTopic), DcTopicVO.class);
        JSONArray jsonArray = JSONArray.parseArray(dcTopicVO.getTopicOption());
        String sortedJsonArray = jsonArray.stream()
                .map(Object::toString)
                .sorted()
                .collect(Collectors.toCollection(JSONArray::new))
                .toJSONString();
        dcTopicVO.setTopicOption(sortedJsonArray);
        return dcTopicVO;
    }


    @Override
    public DcTopic createDcTopic(DcTopic data) {
        // 添加根据章节类型获取最大序号
        boolean rs = dcTopicMapper.insert(data) > 0;
        data.setTopicRank(data.getId());
        dcTopicMapper.updateById(data);
        log.info("创建 DcTopic: {}", rs ? "成功" : "失败");
        return data;
    }

    @Override
    public boolean updateDcTopic(DcTopic data) {
        if (data.getTopicRank() != null) {
            LambdaQueryWrapper<DcTopic> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DcTopic::getTopicRank, data.getTopicRank());
            queryWrapper.ne(DcTopic::getId, data.getId());
            List<DcTopic> list = dcTopicMapper.selectList(queryWrapper);
            if (list.size() > 0) {
                throw new ZkException("已存在相同排序值数据, 请修改!");
            }
        }
        boolean rs = dcTopicMapper.updateById(data) > 0;
        log.info("更新 DcTopic: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public boolean deleteDcTopicById(Integer id) {
        boolean rs = dcTopicMapper.deleteById(id) > 0;
        log.info("删除 DcTopic: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public Boolean updateDcStatus(JSONObject data) {
        String[] ids = data.getString("ids").split(",");
        LambdaQueryWrapper<DcTopic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DcTopic::getId, ids);
        DcTopic topic = new DcTopic();
        topic.setTopicStatus(data.getInteger("topicStatus"));
        boolean rs = dcTopicMapper.update(topic, queryWrapper) > 0;
        log.info("更新 DcTopic 状态: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public List<TopicChapterItem> getTopicChapterList(String chapterType) {
        Integer userId = TemplateSessionUtils.getCurrentUser().getId();
        List<DcSysEnums> enums = dcSysEnumsService.getDcSysEnumsList(DcSysEnumsListSearch
                .builder()
                .aliasCode("gdy_topic_chapter")
                .remark(chapterType)
                .build());
        // 获取每个章节的总题数
        List<TopicChapterItem> topicChapterItems = dcTopicMapper.getTopicChapterCountByChapterType(chapterType);
        Map<String, Integer> topicChapterMap = topicChapterItems.stream()
                .collect(Collectors.toMap(TopicChapterItem::getChapterName, TopicChapterItem::getTopicTotal));
        // 获取用户各章节最新的题目数token
        List<DcAllTopicRecord> records = dcTopicMapper.getLatestRecordByChapter(userId, chapterType);
        Map<String, Integer> topicChapterLastIdMap = records.stream()
                .collect(Collectors.toMap(
                        DcAllTopicRecord::getChapterName,
                        DcAllTopicRecord::getTopicId,
                        (oldVal, newVal) -> newVal // 保留新值（或按需调整策略）
                ));
        List<TopicChapterItem> rsList = new ArrayList<>();
        for (DcSysEnums enums1 : enums) {
            TopicChapterItem item = JSONObject.parseObject(JSONObject.toJSONString(enums1), TopicChapterItem.class);
            item.setChapterName(enums1.getEnumLabel());
            item.setTopicTotal(topicChapterMap.getOrDefault(enums1.getEnumLabel(), 0));
            item.setLastTopicId(topicChapterLastIdMap.getOrDefault(enums1.getEnumLabel(), 0));
            // 获取题号在章节中的排序值
            item.setTopicRank(setTopicRank(false, chapterType, enums1, item));
            rsList.add(item);
        }
        return rsList;
    }

    @Override
    public List<TopicChapterItem> getKsTopicChapterList(String chapterType) {
        List<DcSysEnums> enums = dcSysEnumsService.getDcSysEnumsList(DcSysEnumsListSearch
                .builder()
                .aliasCode("ks_topic_chapter")
                .remark(chapterType)
                .build());
        // 获取每个章节的总题数
        List<TopicChapterItem> topicChapterItems = dcTopicMapper.getTopicChapterCountByChapterType(chapterType);
        Map<String, Integer> topicChapterMap = topicChapterItems.stream()
                .collect(Collectors.toMap(TopicChapterItem::getChapterName, TopicChapterItem::getTopicTotal));
        // 获取用户各章节最新的题目数token
        Map<String, Integer> chapterDoTopicRecordMap = buildChapterProgressMap();

        List<TopicChapterItem> rsList = new ArrayList<>();
        for (DcSysEnums enums1 : enums) {
            TopicChapterItem item = JSONObject.parseObject(JSONObject.toJSONString(enums1), TopicChapterItem.class);
            item.setChapterName(enums1.getEnumLabel());
            item.setTopicTotal(topicChapterMap.getOrDefault(enums1.getEnumValue(), 0));
            item.setLastTopicId(chapterDoTopicRecordMap.getOrDefault(enums1.getEnumValue(), 0));
            // 获取题号在章节中的排序值
            item.setTopicRank(setTopicRank(true, chapterType, enums1, item));
            rsList.add(item);
        }
        return rsList;
    }

    private Map<String, Integer> buildChapterProgressMap() {
        Integer userId = TemplateSessionUtils.getCurrentUser().getId();
        Map<String, Integer> chapterProgressMap = null;
        if (userId != null) {
            LambdaQueryWrapper<DcChapterDoTopicRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DcChapterDoTopicRecord::getUserId, userId);
            List<DcChapterDoTopicRecord> chapterDoTopicRecords = dcChapterDoTopicRecordMapper.selectList(queryWrapper);
            chapterProgressMap = chapterDoTopicRecords.stream()
                    .collect(Collectors.toMap(
                            DcChapterDoTopicRecord::getChapterName,
                            DcChapterDoTopicRecord::getTopicId,
                            (oldVal, newVal) -> newVal // 保留新值（或按需调整策略）
            ));
        }
        return chapterProgressMap;
    }

    private int setTopicRank(boolean ksFlag, String chapterType, DcSysEnums enums1, TopicChapterItem item) {
        LambdaQueryWrapper<DcTopic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcTopic::getIsDelete, 0);
        queryWrapper.eq(DcTopic::getTopicStatus, 1);
        queryWrapper.eq(DcTopic::getChapterType, chapterType);
        queryWrapper.eq(DcTopic::getChapterName, ksFlag ? enums1.getEnumValue() : enums1.getEnumLabel());
        List<DcTopic> list = dcTopicMapper.selectList(queryWrapper);
        List<DcTopic> sortedList = list.stream()
                .sorted(Comparator.comparingInt(DcTopic::getId))
                .collect(Collectors.toList());
        int index = -1; // 默认值
        for (int i = 0; i < sortedList.size(); i++) {
            if (sortedList.get(i).getId() == item.getLastTopicId()) {
                index = i;
                break;
            }
        }
        return index + 1;
    }

    @Override
    public TopicIndexVO getTopicIndex(String chapterType) {
        Integer userId = TemplateSessionUtils.getCurrentUser().getId();
        if (userId != null) {
            int doneTopicNum = dcAllTopicRecordMapper.getUserDoneTopicNum(userId, chapterType);
            LambdaQueryWrapper<DcTopic> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DcTopic::getChapterType, chapterType);
            queryWrapper.eq(DcTopic::getTopicStatus, 1);
            long totalTopicNum = dcTopicMapper.selectCount(queryWrapper);
            LambdaQueryWrapper<DcExamRecord> examRecordQueryWrapper = new LambdaQueryWrapper<>();
            examRecordQueryWrapper
                    .eq(DcExamRecord::getUserId, userId)
                    .isNotNull(DcExamRecord::getExamScore)
                    .orderByDesc(DcExamRecord::getCreateTime)
                    .last("LIMIT 1");
            DcExamRecord lastExamRecord = examRecordMapper.selectOne(examRecordQueryWrapper);
            return TopicIndexVO.builder()
                    .doneTopicNum(doneTopicNum)
                    .totalTopicNum(totalTopicNum)
                    .lastExamScore(lastExamRecord == null ? null : lastExamRecord.getExamScore())
                    .build();
        }
        return null;
    }

    @Override
    public Boolean dealAiTopicAnalysis() {
        // 获取文件
        for (int i = 1; i < 6; i++) {
            String data = ResourceUtils.loadResource("aiTopicData/data" + i + ".json");
            JSONArray jsonArray = JSONArray.parseArray(data);
            for (int j = 0; j < jsonArray.size(); j++) {
                JSONObject jsonObject = jsonArray.getJSONObject(j);
                Integer id = jsonObject.getInteger("id");
                String aiQuestionAnalysis = jsonObject.getString("ai_question_analysis");
                boolean rs = dcTopicMapper.updateById(DcTopic.builder().id(id).aiQuestionAnalysis(aiQuestionAnalysis).build()) > 0;
                log.info("更新题目解析: {} {}", id, rs);
            }
        }

        return true;
    }

    public static String formatOptions(JSONArray optionsArray) {
        // 使用StringBuilder拼接字符串
        StringBuilder result = new StringBuilder("（");

        // 遍历数组，逐个添加选项
        for (int i = 0; i < optionsArray.size(); i++) {
            result.append(optionsArray.getString(i));
            // 如果不是最后一个选项，添加顿号
            if (i < optionsArray.size() - 1) {
                result.append("、");
            }
        }

        // 添加右括号
        result.append("）");

        return result.toString();
    }

    @Async
    @Override
    public Boolean dealAiTopicDetailAnalysis() {
        List<DcTopic> dcTopics = dcTopicDetailAnalysisMapper.selectNotExistTopic();
        for (DcTopic dcTopic : dcTopics) {
            String result = TopicDeepSeekToolkit.dealQuestion(
                    dcTopic.getTopicName() +
                            " 选项:\"" + JSONArray.parseArray(dcTopic.getTopicOption())
                            + "\" 正确答案:" + dcTopic.getQuestionAnswer() + ";"
            );
            if (StringUtils.isValid(result)) {
                log.info("更新题目解析: {} {}", dcTopic.getId(), result);
                dcTopicDetailAnalysisMapper.insert(DcTopicDetailAnalysis.builder()
                        .topicId(dcTopic.getId())
                        .topicAiDetailAnalysis(result)
                        .build());
            }
        }
        return true;
    }

    @Override
    public DcTopic getTopicAiAnalysis(int topicId) {
        LambdaQueryWrapper<DcTopic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcTopic::getId, topicId);
        DcTopic topic = dcTopicMapper.selectOne(queryWrapper);
        if (topic != null) {
            LambdaQueryWrapper<DcTopicDetailAnalysis> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.eq(DcTopicDetailAnalysis::getTopicId, topicId);
            DcTopicDetailAnalysis detailAnalysis = dcTopicDetailAnalysisMapper.selectOne(queryWrapper1);
            if (detailAnalysis != null) {
                topic.setAiQuestionDetailAnalysis(JSONObject.parseObject(detailAnalysis.getTopicAiDetailAnalysis()));
            }
        }
        return topic;
    }

    @Override
    public List<QuestionBreakdown> getQuestionBreakdown(String chapterName) {
        return dcTopicMapper.getQuestionBreakdown(chapterName);
    }

    @Override
    public boolean checkAIAnswer() {
        List<DcTopic> topics = dcTopicMapper.selectList(null);
        List<Integer> ids = new ArrayList<>();
        for (DcTopic dcTopic : topics) {
            // 获取详情数据和答案进行比较
            LambdaQueryWrapper<DcTopicDetailAnalysis> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.eq(DcTopicDetailAnalysis::getTopicId, dcTopic.getId());
            DcTopicDetailAnalysis detailAnalysis = dcTopicDetailAnalysisMapper.selectOne(queryWrapper1);
            if (detailAnalysis != null) {
                JSONObject answer = JSONObject.parseObject(detailAnalysis.getTopicAiDetailAnalysis());
                String aiAnswer = answer.getString("TrueAnswer");
                aiAnswer = aiAnswer.contains(".") ? aiAnswer.substring(0, aiAnswer.indexOf(".")) : aiAnswer;
                if (!dcTopic.getQuestionAnswer().equals(aiAnswer)) {
                    ids.add(dcTopic.getId());
                }
            }
        }
        for (Integer id : ids) {
            System.out.print("\"" + id + "\",");
        }
        return true;
    }

    @Override
    public YCTopic getDcYcTopicList() {
        List<DcTopicVO> ycTopicALLRecords = dcTopicMapper.getDcYcTopicForAllRecords();
        List<DcTopicVO> ycTopic7DayRecords = dcTopicMapper.getDcYcTopicFor7DayRecords();
        return YCTopic.builder()
                .ycTopicALLRecords(ycTopicALLRecords)
                .ycTopic7DayRecords(ycTopic7DayRecords)
                .build();
    }
}
