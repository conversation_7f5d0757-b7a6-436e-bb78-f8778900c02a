package com.droneclub.source.template.timedtask;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.droneclub.source.template.common.utils.ResourceUtils;

public class CompanyDataProcess {

    private static final String[] DATA_JSON = new String[]{"temp-data1.json", "temp-data2.json", "temp-data3.json"};

    public static void main(String[] args) {
        for (String orgData : DATA_JSON) {
            JSONObject json = JSONObject.parseObject(ResourceUtils.loadResource("otherdata/" + orgData));
            JSONArray rows = json.getJSONArray("rows");
            for (int i = 0; i < rows.size(); i++) {
                JSONObject item = rows.getJSONObject(i);
                System.out.println(item.getString("bangsdz"));
            }
        }
    }
}
