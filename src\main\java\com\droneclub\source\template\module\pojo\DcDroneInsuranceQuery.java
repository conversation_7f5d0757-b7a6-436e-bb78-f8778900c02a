package com.droneclub.source.template.module.pojo;

import com.droneclub.source.template.common.model.BaseSearchParams;
import lombok.Data;

import java.io.Serializable;

/**
 * 无人机投保查询条件对象
 */
@Data
public class DcDroneInsuranceQuery extends BaseSearchParams implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 排序字段
     */
    private String sortField = "id";

    /**
     * 排序方式
     */
    private String sortOrder = "desc";

    /**
     * 无人机品牌
     */
    private String droneBrand;

    /**
     * 无人机型号
     */
    private String droneModel;

    /**
     * 机器S/N码
     */
    private String machineSn;

    /**
     * 电池S/N码
     */
    private String batterySn;

    /**
     * 投保类型
     */
    private String insuranceType;

    /**
     * 被保人姓名
     */
    private String insuredName;

    /**
     * 身份证件号码
     */
    private String idNumber;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 联系地址
     */
    private String address;

    /**
     * 机型类别
     */
    private String droneCategory;

    /**
     * 用途
     */
    private String purpose;

    /**
     * 无人机检验合格证图片路径
     */
    private String inspectionCertificate;

    /**
     * 无人机照片路径
     */
    private String dronePhoto;

    /**
     * 营业执照照片路径
     */
    private String businessLicense;

    /**
     * 个人身份证照片路径
     */
    private String idPhoto;

    /**
     * 出单方案序号
     */
    private String policySchemeNo;

    /**
     * 保险状态 1: 待审核 2: 已生效 3: 已拒绝 4: 已过期
     */
    private String insuranceStatus;

    /**
     * 创建人
     */
    private Integer createUser;

    /**
     * 是否删除 0: 未删除 1: 已删除
     */
    private Integer isDelete;

} 