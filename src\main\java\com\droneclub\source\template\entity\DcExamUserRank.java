package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("dc_exam_user_rank")
public class DcExamUserRank {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String chapterType;
    private String examType;

    /**
     * 考试试题类型 base: 基础试题 zh: 综合试题
     */
    private String examTopicType;

    private Integer userId;
    private String userName;
    @TableField(exist = false)
    private String userAvatar;
    private String score;
    private Integer rank;
    private String examTime;
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    private String createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer updateUser;
    private String updateTime;
    private String isDelete;

}
