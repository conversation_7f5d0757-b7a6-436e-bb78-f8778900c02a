package com.droneclub.source.template.module.service.impl;


import cn.soulspark.source.common.execption.ZkException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.droneclub.source.template.entity.DcCkKeyEntity;
import com.droneclub.source.template.mapper.DcCkKeyMapper;
import com.droneclub.source.template.module.service.IDcCdKeyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcCdKeyServiceImpl implements IDcCdKeyService {

    private final DcCkKeyMapper dcCkKeyMapper;

    private static final DateTimeFormatter TIME_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public void checkCdKey(String cdKey, String userAgent) {
        DcCkKeyEntity entity = dcCkKeyMapper.selectOne(
                new QueryWrapper<DcCkKeyEntity>()
                        .eq("cdk_code", cdKey)
                        .last("LIMIT 1")
        );
        if (entity == null) {
            throw new ZkException("激活码不存在");
        }

        // 2. 检查状态（只允许0或1）
        if (entity.getStatus() != 0 && entity.getStatus() != 1) {
            throw new ZkException("激活码已使用");
        }

        // 3. 检查激活时间（状态为0或1时需在1小时内）
        if (entity.getStatus() == 1) {
            if (entity.getActivationTime() == null) {
                throw new ZkException("激活码已使用或已失效");
            }

            LocalDateTime activationTime = LocalDateTime.parse(
                    entity.getActivationTime(),
                    TIME_FORMATTER
            );
            LocalDateTime now = LocalDateTime.now();

            // 计算时间差（小时）
            long hoursBetween = ChronoUnit.HOURS.between(activationTime, now);
            if (hoursBetween >= 1) {
                throw new ZkException("激活码已使用");
            }
        }
        if (entity.getStatus() == 0) {
            entity.setActivationTime(
                    LocalDateTime.now().format(TIME_FORMATTER)
            );
            entity.setStatus(1);
            entity.setActivationUserAgent(userAgent);
            int updateCount = dcCkKeyMapper.updateById(entity);
            log.info("Channel: {} 激活码: {} 已使用, 结果: {}", entity.getChannel(), entity.getCdkCode(), updateCount > 0);
        }
    }

}
