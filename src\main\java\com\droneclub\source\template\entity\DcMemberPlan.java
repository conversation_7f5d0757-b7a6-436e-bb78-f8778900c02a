package com.droneclub.source.template.entity;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;


@Data
@TableName(value = "dc_member_plan", autoResultMap = true)
public class DcMemberPlan {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String enumLabel;

    private String enumValue;

    private String remark;

    private String price;

    @TableField(exist = false)
    private JSONArray interestsList;

    private String interests;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}