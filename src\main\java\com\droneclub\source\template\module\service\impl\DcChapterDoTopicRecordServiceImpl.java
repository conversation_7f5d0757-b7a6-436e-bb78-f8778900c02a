package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.common.utils.TemplateSessionUtils;
import com.droneclub.source.template.entity.DcChapterDoTopicRecord;
import com.droneclub.source.template.mapper.DcChapterDoTopicRecordMapper;
import com.droneclub.source.template.module.pojo.DcChapterDoTopicRecordListSearch;
import com.droneclub.source.template.module.pojo.DcChapterDoTopicRecordVO;
import com.droneclub.source.template.module.service.IDcChapterDoTopicRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcChapterDoTopicRecordServiceImpl implements IDcChapterDoTopicRecordService {

    private final DcChapterDoTopicRecordMapper dcChapterDoTopicRecordMapper;

    @Override
    public ListData<DcChapterDoTopicRecordVO> getDcChapterDoTopicRecordList(DcChapterDoTopicRecordListSearch params) {
        QueryWrapper<DcChapterDoTopicRecord> queryWrapper = new QueryWrapper<>();
        // 查询总数
        Long total = dcChapterDoTopicRecordMapper.selectCount(queryWrapper);

        // 分页查询
        Page<DcChapterDoTopicRecord> page = new Page<>(params.getPageNo(), params.getPageSize());
        IPage<DcChapterDoTopicRecord> dcChapterDoTopicRecordPage = dcChapterDoTopicRecordMapper.selectPage(page, queryWrapper);
        List<DcChapterDoTopicRecord> list = dcChapterDoTopicRecordPage.getRecords();
        List<DcChapterDoTopicRecordVO> listVO = list.stream()
                .map(dcChapterDoTopicRecord -> JSONObject.parseObject(JSONObject.toJSONString(dcChapterDoTopicRecord), DcChapterDoTopicRecordVO.class))
                .collect(Collectors.toList());
        return new ListData<>(listVO, total, params.getPageNo(), params.getPageSize());
    }

    @Override
    public DcChapterDoTopicRecordVO getDcChapterDoTopicRecordById(Integer id) {
        DcChapterDoTopicRecord dcChapterDoTopicRecord = dcChapterDoTopicRecordMapper.selectById(id);
        if (dcChapterDoTopicRecord == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        return JSONObject.parseObject(JSONObject.toJSONString(dcChapterDoTopicRecord), DcChapterDoTopicRecordVO.class);
    }


    @Override
    public DcChapterDoTopicRecord createDcChapterDoTopicRecord(DcChapterDoTopicRecord data) {
        int currentUserId = TemplateSessionUtils.getCurrentUser().getId();
        data.setUserId(currentUserId);
        LambdaQueryWrapper<DcChapterDoTopicRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcChapterDoTopicRecord::getUserId, currentUserId);
        DcChapterDoTopicRecord chapterDoTopicRecord = dcChapterDoTopicRecordMapper.selectOne(queryWrapper);
        if (chapterDoTopicRecord != null) {
            data.setId(chapterDoTopicRecord.getId());
            updateDcChapterDoTopicRecord(data);
        } else {
            boolean rs = dcChapterDoTopicRecordMapper.insert(data) > 0;
            log.info("创建 DcChapterDoTopicRecord: {}", rs ? "成功" : "失败");
        }
        return data;
    }

    @Override
    public boolean updateDcChapterDoTopicRecord(DcChapterDoTopicRecord data) {
        boolean rs = dcChapterDoTopicRecordMapper.updateById(data) > 0;
        log.info("更新 DcChapterDoTopicRecord: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public boolean deleteDcChapterDoTopicRecordById(Integer id) {
        boolean rs = dcChapterDoTopicRecordMapper.deleteById(id) > 0;
        log.info("删除 DcChapterDoTopicRecord: {}", rs ? "成功" : "失败");
        return rs;
    }
}
