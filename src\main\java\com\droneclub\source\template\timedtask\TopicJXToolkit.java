package com.droneclub.source.template.timedtask;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TopicJXToolkit {
    private static final String TOKEN = "pat_UrlRfT8strEaTGPpvSU3GclHfsivzADXNHPdcDvrKbKo7Aff7DtNCwQJyixshUc8";
    private static final String TOKEN_1 = "pat_Cwxl496HX1JyWMFoNd8q6XEB8UzIur8oohlr3BEgcRB3QzzaMNpLgN85QbDoDEPp";

    public static JSONObject sendBotMessage(String content) {
        String url = "https://api.coze.cn/v3/chat";
        String result = OkHttpRequestUtil.postRequest(url, content, TOKEN);
        log.info("sendBotMessage result: {}", result);
        JSONObject json = JSONObject.parseObject(result);
        return json.getJSONObject("data");
    }

    public static String getMessageStatus(String chatId, String conversationId) {
        String baseUrl = "https://api.coze.cn/v3/chat/retrieve";
        String fullUrl = baseUrl + "?chat_id=" + chatId + "&conversation_id=" + conversationId;
        String result = OkHttpRequestUtil.getRequest(fullUrl, TOKEN);
        JSONObject json = JSONObject.parseObject(result);
        return json.getJSONObject("data").getString("status");
    }

    public static String getMessageDetail(String chatId, String conversationId) {
        String baseUrl = "https://api.coze.cn/v3/chat/message/list";
        String fullUrl = baseUrl + "?chat_id=" + chatId + "&conversation_id=" + conversationId;
        return OkHttpRequestUtil.getRequest(fullUrl, TOKEN);
    }

    public static String dealQuestion(String topicQuestion) {
        JSONObject item = new JSONObject();
        item.put("role", "user");
        item.put("content_type", "text");
        item.put("content", topicQuestion);
        JSONArray array = new JSONArray();
        array.add(item);
        JSONObject additionalMessages = JSONObject.parseObject("{\n" +
                "    \"bot_id\": \"7482291121830723584\",\n" +
                "    \"user_id\": \"1\",\n" +
                "    \"stream\": false,\n" +
                "    \"auto_save_history\": true\n" +
                "}");
        additionalMessages.put("additional_messages", array);

        JSONObject messageData = sendBotMessage(additionalMessages.toJSONString());
        if (messageData == null) {
            throw new RuntimeException("发生异常");
        }
        String status = "fail";
        while (!"completed".equals(status)) {
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            status = getMessageStatus(messageData.getString("id"), messageData.getString("conversation_id"));
        }

        String messageContent = getMessageDetail(messageData.getString("id"), messageData.getString("conversation_id"));
        messageContent = messageContent.replaceAll("```json", "");
        messageContent = messageContent.replaceAll("```", "");
        JSONObject messageJson = JSONObject.parseObject(messageContent).getJSONArray("data").getJSONObject(0);
        return messageJson.getJSONObject("content").toJSONString();
    }


}
