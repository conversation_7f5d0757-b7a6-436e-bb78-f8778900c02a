package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("dc_exam_record")
public class DcExamRecord {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String chapterType;
    /**
     * 视距、超视距、教员
     */
    private String examType;
    /**
     * 考试试题类型 base: 基础试题 zh: 综合试题
     */
    private String examTopicType;
    /**
     * 总正确题目数
     */
    private Long correctAnswerRecordNum;
    /**
     * 总错误题目数
     */
    private Long errorAnswerRecordNum;
    /**
     * 考试用时
     */
    private String examDuration;
    /**
     * 考试结果
     */
    private Integer examResult;
    /**
     * 考试总分
     */
    private String examScore;
    private Integer userId;
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    private String createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer updateUser;
    private String updateTime;
    private String isDelete;

}
