package com.droneclub.source.template.module.service;

import com.droneclub.source.template.entity.DcCompanyDiscounts;
import com.droneclub.source.template.module.pojo.DcCompanyDiscountsListSearch;
import com.droneclub.source.template.module.pojo.DcCompanyDiscountsVO;

import java.util.List;

public interface IDcCompanyDiscountsService {

    List<DcCompanyDiscountsVO> getDcCompanyDiscountsList(DcCompanyDiscountsListSearch params);

    DcCompanyDiscountsVO getDcCompanyDiscountsById(Integer id);

    DcCompanyDiscounts createDcCompanyDiscounts(DcCompanyDiscounts data);

    boolean updateDcCompanyDiscounts(DcCompanyDiscounts data);

    boolean deleteDcCompanyDiscountsById(Integer id);
}
