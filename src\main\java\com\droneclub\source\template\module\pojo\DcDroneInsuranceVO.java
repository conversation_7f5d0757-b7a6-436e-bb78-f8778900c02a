package com.droneclub.source.template.module.pojo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;

/**
 * 无人机投保视图对象
 */
@Data
public class DcDroneInsuranceVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物理主键
     */
    private Integer id;

    /**
     * 无人机品牌
     */
    private String droneBrand;

    /**
     * 无人机型号
     */
    private String droneModel;

    /**
     * 机器S/N码
     */
    private String machineSn;

    /**
     * 电池S/N码
     */
    private String batterySn;

    /**
     * 投保类型
     */
    private String insuranceType;

    /**
     * 被保人姓名
     */
    private String insuredName;

    /**
     * 身份证件号码
     */
    private String idNumber;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 联系地址
     */
    private String address;

    /**
     * 机型类别
     */
    private String droneCategory;

    /**
     * 用途
     */
    private String purpose;

    /**
     * 出单方案序号
     */
    private String policySchemeNo;

    /**
     * 文件信息
     */
    @TableField(exist = false)
    private JSONObject files;

    /**
     * 保险状态 1: 待审核 2: 已生效 3: 已拒绝 4: 已过期
     */
    private String insuranceStatus;

    /**
     * 创建人
     */
    private Integer createUser;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新人
     */
    private Integer updateUser;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 是否删除 0: 未删除 1: 已删除
     */
    private Integer isDelete;
} 