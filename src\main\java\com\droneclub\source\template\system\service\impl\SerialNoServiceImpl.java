package com.droneclub.source.template.system.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import com.droneclub.source.template.common.utils.DateUtils;
import com.droneclub.source.template.constants.SerialNoConstant;
import com.droneclub.source.template.mapper.SequenceMapper;
import com.droneclub.source.template.system.service.ISerialNoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;

/**
 * 流水号构造
 */
@Service
@RequiredArgsConstructor
public class SerialNoServiceImpl implements ISerialNoService {

    private final SequenceMapper sequenceMapper;
    private final TransactionTemplate transaction;

    /**
     * 生成流水号
     *
     * @param bindNowDate 是否绑定当前日期
     * @param moduleCode  流水号模块code
     * @param noFormat    流水号格式
     * @param number：号码
     * @return 流水号
     */
    public static String buildSerialNo(boolean bindNowDate, String moduleCode, String noFormat, Integer number) {
        String num = String.valueOf(number);
        StringBuilder sb = new StringBuilder(moduleCode);
        if (bindNowDate) {
            DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
            sb.append(dateFormat.format(new Date()));
        }
        sb.append(noFormat);
        sb.replace(sb.length() - num.length(), sb.length(), num);
        return sb.toString();
    }

    @Override
    public String buildSerialNumber(String moduleCode, String noFormat) {
        return buildSerialNo(true, moduleCode, noFormat, nextValue(moduleCode, LocalDate.now()));
    }

    @Override
    public int buildIntSerialNumber(String moduleCode) {
        return nextValue(moduleCode, LocalDate.now());
    }

    public synchronized int nextValue(String moduleCode, LocalDate date) {
        Integer currentValue = sequenceMapper.getCurrentValue(moduleCode, DateUtils.dateToString(date), false);
        if (currentValue == null) {
            Integer initValue = init(moduleCode, date);
            return initValue == null ? nextValue(moduleCode, date) : initValue;
        } else {
            int oldValue = currentValue;
            int newValue = oldValue + SerialNoConstant.STEP_SIZE;
            int retryCount = 0;
            while (retryCount < 3 && sequenceMapper.progress(moduleCode, DateUtils.dateToString(date), oldValue, newValue) != 1) {
                oldValue = sequenceMapper.getCurrentValue(moduleCode, DateUtils.dateToString(date), false);
                newValue = oldValue + SerialNoConstant.STEP_SIZE;
                retryCount++;
            }
            if (retryCount >= 3) {
                throw new ZkException(ResultCode.FAIL.getCode(), "流水号构建失败");
            }
            return newValue;
        }
    }

    private Integer init(String moduleCode, LocalDate date) {
        return transaction.execute(status -> {
            Integer currentValue = sequenceMapper.getCurrentValue(moduleCode, DateUtils.dateToString(date), true);
            if (currentValue == null) {
                sequenceMapper.init(moduleCode, DateUtils.dateToString(date), SerialNoConstant.INIT_SIZE);
                return SerialNoConstant.INIT_SIZE;
            } else {
                return null;
            }
        });
    }
}
