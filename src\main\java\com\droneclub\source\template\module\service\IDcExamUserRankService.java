package com.droneclub.source.template.module.service;

import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcExamUserRank;
import com.droneclub.source.template.module.pojo.DcExamUserRankVO;
import com.droneclub.source.template.module.pojo.DcExamUserRankListSearch;

public interface IDcExamUserRankService {
    
    ListData<DcExamUserRankVO> getDcExamUserRankList(DcExamUserRankListSearch params);

    DcExamUserRankVO getDcExamUserRankById(Integer id);

    DcExamUserRank createDcExamUserRank(DcExamUserRank data);

    boolean updateDcExamUserRank(DcExamUserRank data);

    boolean deleteDcExamUserRankById(Integer id);
}
