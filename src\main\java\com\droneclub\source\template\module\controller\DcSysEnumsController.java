package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.droneclub.source.template.common.utils.ResourceUtils;
import com.droneclub.source.template.entity.DcSysEnums;
import com.droneclub.source.template.module.pojo.DcSysEnumsListSearch;
import com.droneclub.source.template.module.pojo.DcSysEnumsVO;
import com.droneclub.source.template.module.service.IDcSysEnumsService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dcSysEnums")
public class DcSysEnumsController {

    private final IDcSysEnumsService dcSysEnumsService;

    static String DATA = "道通EVOmax4T\t42800\t7704\n" +
            "道通Nano+\t4999\t900\n" +
            "道通EVO II\t12000\t2160\n" +
            "哈博森ACE SE 精英版标准版 单电\t1999\t360\n" +
            "哈博森ACE SE 精英版便携版 双电\t2799\t504\n" +
            "哈博森ACE SE 精英版便携版 三电\t3199\t576\n" +
            "极飞M500\t22498\t4050\n" +
            "XCam20M多光谱云台相机\t16800\t3024\n" +
            "ZT-16V\t380000\t68400\n" +
            "CW15\t570000\t102600\n" +
            "MG-120E EO/IR光电吊舱\t130000\t23400\n" +
            "C30N\t135000\t24300\n";

    @GetMapping("/getDcSysEnumsList")
    public RestResult<List<DcSysEnums>> getDcSysEnumsList(DcSysEnumsListSearch params) {
        return RestResult.success(dcSysEnumsService.getDcSysEnumsList(params));
    }

    @GetMapping("/getDcSysEnumsById")
    public RestResult<DcSysEnumsVO> getDcSysEnumsById(Integer id) {
        return RestResult.success(dcSysEnumsService.getDcSysEnumsById(id));
    }

    @PostMapping("/createDcSysEnums")
    public RestResult<DcSysEnums> createDcSysEnums(@RequestBody DcSysEnums data) {
        return RestResult.success(dcSysEnumsService.createDcSysEnums(data));
    }

    @PutMapping("/updateDcSysEnums")
    public RestResult<Boolean> updateDcSysEnums(@RequestBody DcSysEnums data) {
        return RestResult.success(dcSysEnumsService.updateDcSysEnums(data));
    }

    @DeleteMapping("/deleteDcSysEnumsById")
    public RestResult<Boolean> deleteDcSysEnumsById(Integer id) {
        return RestResult.success(dcSysEnumsService.deleteDcSysEnumsById(id));
    }

    public static void main(String[] args) {
        String[] array = DATA.split("\n");
        JSONArray array1 = new JSONArray();
        for (String key : array) {
            String[] temp = key.split("\t");
            array1.add(JSONObject.parse("{\n" +
                    "        \"model\": \"" + temp[0] + "\",\n" +
                    "        \"保障额度\": " + temp[1] + ",\n" +
                    "                \"零售保费\": " + temp[2] + "\n" +
                    "    }"));
        }
        System.out.println(array1.toJSONString());
    }

    @GetMapping("/getDictJsonData")
    public RestResult<Object> getDictJsonData(String type) {
        Object json = JSON.parse(ResourceUtils.loadResource("dictdata/" + type + ".json"));
        return RestResult.success(json);
    }

    /**
     * 获取会员套餐
     * @return 会员套餐
     */
    @GetMapping("/getMemberPlan")
    public RestResult<Object> getMemberPlan() {
        return RestResult.success(dcSysEnumsService.getMemberPlan());
    }
}
