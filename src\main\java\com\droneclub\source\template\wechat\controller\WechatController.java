package com.droneclub.source.template.wechat.controller;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.model.RestResult;
import com.alibaba.fastjson.JSONObject;
import com.droneclub.source.template.wechat.service.IWechatSecurityAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/wechat")
public class WechatController {

    private final IWechatSecurityAuditService wechatSecurityAuditService;

    @PostMapping("/contentAudit")
    public RestResult<Object> contentAudit(@RequestBody JSONObject params) {
        if (params.containsKey("openId") && params.containsKey("content")) {
            String openId = params.getString("openId");
            String content = params.getString("content");
            String userName = params.getString("userName");
            return RestResult.success(wechatSecurityAuditService.contentAudit(openId, content, userName));
        }
        return RestResult.failure(ResultCode.LACK_PARAMS);
    }
}
