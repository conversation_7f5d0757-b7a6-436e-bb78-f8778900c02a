package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import cn.soulspark.source.common.utils.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.droneclub.source.template.entity.DcMemberPlan;
import com.droneclub.source.template.entity.DcSysEnums;
import com.droneclub.source.template.mapper.DcMemberPlanMapper;
import com.droneclub.source.template.mapper.DcSysEnumsMapper;
import com.droneclub.source.template.module.pojo.DcSysEnumsListSearch;
import com.droneclub.source.template.module.pojo.DcSysEnumsVO;
import com.droneclub.source.template.module.service.IDcSysEnumsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcSysEnumsServiceImpl implements IDcSysEnumsService {

    private final DcSysEnumsMapper dcSysEnumsMapper;
    private final DcMemberPlanMapper dcMemberPlanMapper;

    @Override
    public List<DcSysEnums> getDcSysEnumsList(DcSysEnumsListSearch params) {
        LambdaQueryWrapper<DcSysEnums> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isValid(params.getAliasCode())) {
            queryWrapper.eq(DcSysEnums::getAliasCode, params.getAliasCode());
        }
        if (StringUtils.isValid(params.getRemark())) {
            queryWrapper.eq(DcSysEnums::getRemark, params.getRemark());
        }
        // 按rank正序排序
        queryWrapper.orderByDesc(DcSysEnums::getRank);
        return dcSysEnumsMapper.selectList(queryWrapper);
    }

    @Override
    public DcSysEnumsVO getDcSysEnumsById(Integer id) {
        DcSysEnums dcSysEnums = dcSysEnumsMapper.selectById(id);
        if (dcSysEnums == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        return JSONObject.parseObject(JSONObject.toJSONString(dcSysEnums), DcSysEnumsVO.class);
    }


    @Override
    public DcSysEnums createDcSysEnums(DcSysEnums data) {
        boolean rs = dcSysEnumsMapper.insert(data) > 0;
        log.info("创建 DcSysEnums: {}", rs ? "成功" : "失败");
        return data;
    }

    @Override
    public boolean updateDcSysEnums(DcSysEnums data) {
        boolean rs = dcSysEnumsMapper.updateById(data) > 0;
        log.info("更新 DcSysEnums: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public boolean deleteDcSysEnumsById(Integer id) {
        boolean rs = dcSysEnumsMapper.deleteById(id) > 0;
        log.info("删除 DcSysEnums: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public Object getMemberPlan() {
        List<DcMemberPlan> list = dcMemberPlanMapper.selectList(null);
        for (DcMemberPlan dcMemberPlan : list) {
            dcMemberPlan.setInterestsList(JSONArray.parseArray(dcMemberPlan.getInterests()));
            dcMemberPlan.setInterests(null);
        }
        return list;
    }
}
