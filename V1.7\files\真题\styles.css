﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-42px;
  width:945px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u359_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:738px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u359 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:33px;
  width:375px;
  height:738px;
  display:flex;
}
#u359 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u359_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u360_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:59px;
}
#u360 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:33px;
  width:375px;
  height:59px;
  display:flex;
}
#u360 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u360_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u361_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:82px;
}
#u361 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:92px;
  width:375px;
  height:82px;
  display:flex;
}
#u361 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u361_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u362_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:355px;
  height:183px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u362 {
  border-width:0px;
  position:absolute;
  left:52px;
  top:254px;
  width:355px;
  height:183px;
  display:flex;
}
#u362 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u362_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u363_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u363 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:267px;
  width:50px;
  height:50px;
  display:flex;
}
#u363 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u363_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u364_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u364 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:267px;
  width:50px;
  height:50px;
  display:flex;
}
#u364 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u364_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u365_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u365 {
  border-width:0px;
  position:absolute;
  left:250px;
  top:267px;
  width:50px;
  height:50px;
  display:flex;
}
#u365 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u365_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u366_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u366 {
  border-width:0px;
  position:absolute;
  left:340px;
  top:267px;
  width:50px;
  height:50px;
  display:flex;
}
#u366 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u366_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u367_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u367 {
  border-width:0px;
  position:absolute;
  left:67px;
  top:326px;
  width:56px;
  height:16px;
  display:flex;
}
#u367 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u367_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u368_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u368 {
  border-width:0px;
  position:absolute;
  left:157px;
  top:326px;
  width:56px;
  height:16px;
  display:flex;
}
#u368 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u368_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u369_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u369 {
  border-width:0px;
  position:absolute;
  left:248px;
  top:326px;
  width:56px;
  height:16px;
  display:flex;
}
#u369 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u369_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u370_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u370 {
  border-width:0px;
  position:absolute;
  left:344px;
  top:326px;
  width:42px;
  height:16px;
  display:flex;
}
#u370 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u370_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u371_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u371 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:350px;
  width:50px;
  height:50px;
  display:flex;
}
#u371 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u371_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u372_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u372 {
  border-width:0px;
  position:absolute;
  left:164px;
  top:409px;
  width:42px;
  height:16px;
  display:flex;
}
#u372 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u372_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u373_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u373 {
  border-width:0px;
  position:absolute;
  left:250px;
  top:350px;
  width:50px;
  height:50px;
  display:flex;
}
#u373 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u373_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u374_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u374 {
  border-width:0px;
  position:absolute;
  left:247px;
  top:409px;
  width:56px;
  height:16px;
  display:flex;
}
#u374 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u374_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u375_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u375 {
  border-width:0px;
  position:absolute;
  left:340px;
  top:350px;
  width:50px;
  height:50px;
  display:flex;
}
#u375 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u375_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u376_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u376 {
  border-width:0px;
  position:absolute;
  left:344px;
  top:409px;
  width:42px;
  height:16px;
  display:flex;
}
#u376 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u376_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u377_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:33px;
}
#u377 {
  border-width:0px;
  position:absolute;
  left:287px;
  top:334px;
  width:33px;
  height:33px;
  display:flex;
}
#u377 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u377_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u378_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:355px;
  height:63px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u378 {
  border-width:0px;
  position:absolute;
  left:52px;
  top:184px;
  width:355px;
  height:63px;
  display:flex;
}
#u378 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u378_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u379_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
}
#u379 {
  border-width:0px;
  position:absolute;
  left:67px;
  top:194px;
  width:133px;
  height:15px;
  display:flex;
  font-size:13px;
}
#u379 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u379_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u380_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:217px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u380 {
  border-width:0px;
  position:absolute;
  left:67px;
  top:221px;
  width:217px;
  height:16px;
  display:flex;
}
#u380 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u380_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u381_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:23px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:38px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u381 {
  border-width:0px;
  position:absolute;
  left:314px;
  top:204px;
  width:75px;
  height:23px;
  display:flex;
}
#u381 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u381_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u382_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u382 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:350px;
  width:50px;
  height:50px;
  display:flex;
}
#u382 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u382_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u383_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u383 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:409px;
  width:70px;
  height:16px;
  display:flex;
}
#u383 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u383_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u384_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:533px;
  height:231px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-size:16px;
}
#u384 {
  border-width:0px;
  position:absolute;
  left:454px;
  top:267px;
  width:533px;
  height:231px;
  display:flex;
  font-size:16px;
}
#u384 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u384_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
