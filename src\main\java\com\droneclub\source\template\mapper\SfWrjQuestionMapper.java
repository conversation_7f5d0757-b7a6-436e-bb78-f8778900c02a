package com.droneclub.source.template.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.droneclub.source.template.entity.SfWrjQuestion;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SfWrjQuestionMapper extends BaseMapper<SfWrjQuestion> {


    @Insert({
            "REPLACE INTO sf_wrj_question_1(ti_id, question, chap_id, analysis, chap_name, update_time, answer, creator_id, status, opt_list)",
            "VALUES (#{question.tiId}, #{question.question}, #{question.chapId}, #{question.analysis}, #{question.chapName}, #{question.updateTime}, #{question.answer}, #{question.creatorId}, #{question.status}, #{question.optList})"
    })
    void replaceInto(@Param("question") SfWrjQuestion question);

}
