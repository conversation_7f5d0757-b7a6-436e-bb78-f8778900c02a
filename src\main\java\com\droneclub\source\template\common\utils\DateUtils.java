package com.droneclub.source.template.common.utils;

import lombok.SneakyThrows;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAccessor;
import java.util.*;

/**
 * 日期工具类
 */
public class DateUtils {

    /**
     * 日期格式yyyy-MM-dd
     */
    public static final String DATE_FORMAT = "yyyy-MM-dd";

    /**
     * 日期格式yyyy-MM-dd HH:mm:ss
     */
    public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 获取当前日期时间
     *
     * @return 当前日期时间
     */
    public static String getNowDateTime() {
        return formatDateTime(new Date());
    }

    /**
     * 获取当前日期
     *
     * @return 当前日期
     */
    public static String getNowDate() {
        return formatDateTime(new Date(), DATE_FORMAT);
    }

    /**
     * 格式化日期时间
     *
     * @param val 时间
     * @return 时间字符串
     */
    public static String formatDateTime(Date val) {
        if (val == null) {
            return "";
        }
        DateFormat ft = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return ft.format(val);
    }

    /**
     * 按照日期格式格式化当前日期时间
     *
     * @param date   日期
     * @param format 格式
     * @return 格式化后的日期
     */
    public static String formatDateTime(Date date, String format) {
        if (date == null) {
            return "";
        }
        if (format == null || "".equals(format)) {
            format = DATETIME_FORMAT;
        }
        DateFormat ft = new SimpleDateFormat(format);
        return ft.format(date);
    }

    /**
     * localData转string
     *
     * @param date 日期
     * @return 日期字符串
     */
    public static String dateToString(LocalDate date) {
        return DateTimeFormatter.ISO_DATE.format(date);
    }

    /**
     * 获取指定下一月指定日期
     *
     * @param additions 月份增加值
     * @param day       天
     * @return 日期字符串
     */
    public static String getNextMonthDay(int additions, int day) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, additions);
        Date theDate = calendar.getTime();

        GregorianCalendar gcLast = (GregorianCalendar) Calendar.getInstance();
        gcLast.setTime(theDate);
        gcLast.set(Calendar.DAY_OF_MONTH, day);
        return df.format(gcLast.getTime());
    }

    /**
     * 获取指定添加多少天数的日期
     *
     * @param addDay 添加天数
     * @return 日期字符串
     */
    public static String getNextDay(int addDay) {
        return getNextDay(LocalDateTime.now(), addDay);
    }

    public static String getNextDay(String time, int addDay) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startDate = LocalDateTime.parse(time, formatter);
        return getNextDay(startDate, addDay);
    }

    public static String getNextDay(LocalDateTime startDate, int addDay) {
        LocalDateTime futureDate = startDate.plus(addDay, ChronoUnit.DAYS);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return futureDate.format(formatter);
    }

    /**
     * 根据时间间隔获取每日的日期
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return
     */
    public static List<String> getDaysByInterval(String startDate, String endDate) {
        List<String> dateList = new ArrayList<>();
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            long startTime = sdf.parse(startDate).getTime();
            long endTime = sdf.parse(endDate).getTime();
            long oneDay = 1000 * 60 * 60 * 24L;
            long time = startTime;
            while (time <= endTime) {
                Date d = new Date(time);
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
                String date = df.format(d);
                dateList.add(date);
                time += oneDay;
            }
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return dateList;
    }

    public static void main(String[] args) {
        System.out.println(compareWithCurrentDate("2022-01-21 14:31:22"));
    }

    /**
     * 校验时间字符串格式是否正确
     *
     * @param timeStr 时间字符串
     * @return 校验结果
     */
    public static boolean checkDateString(String timeStr) {
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        TemporalAccessor date = null;
        try {
            date = timeFormatter.parse(timeStr);
            return timeStr.equals(timeFormatter.format(date));
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    /**
     * 判断时间是否数据指定时间间隔内
     *
     * @param time         时间
     * @param timeInterval 时间间隔
     * @return 判断结果
     */
    @SneakyThrows
    public static boolean judgeTimeInterval(String time, int timeInterval) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date specifiedDate = format.parse(time);
        long specifiedTimestamp = specifiedDate.getTime();
        long currentTimestamp = System.currentTimeMillis();
        long timeDifference = currentTimestamp - specifiedTimestamp;
        double hoursDifference = (double) timeDifference / (1000 * 60 * 60);
        return hoursDifference < timeInterval;
    }

    public static LocalTime stringToLocalTime(String time) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATETIME_FORMAT);
        return LocalTime.parse(time, formatter);
    }

    public static int compareWithCurrentDate(String time) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse(time, formatter);
        LocalDateTime currentDateTime = LocalDateTime.now();
        return dateTime.compareTo(currentDateTime);
    }


    public static String convertTime(String input) {
        try {
            long seconds = Long.parseLong(input);
            return formatDuration(seconds);
        } catch (NumberFormatException e) {
            // 如果输入不是数字类型，直接返回原字符串
            return input;
        }
    }

    private static String formatDuration(long seconds) {
        Duration duration = Duration.ofSeconds(seconds);
        long hours = duration.toHours();
        long minutes = duration.toMinutes() % 60;
        long remainingSeconds = duration.getSeconds() % 60;

        if (hours > 0) {
            return hours + "小时" + minutes + "分钟" + remainingSeconds + "秒";
        } else if (minutes > 0) {
            return minutes + "分钟" + remainingSeconds + "秒";
        } else {
            return remainingSeconds + "秒";
        }
    }
}
