package com.droneclub.source.template.module.service;

import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcCompanyJoinClue;
import com.droneclub.source.template.module.pojo.DcCompanyJoinClueVO;
import com.droneclub.source.template.module.pojo.DcCompanyJoinClueListSearch;

public interface IDcCompanyJoinClueService {
    
    ListData<DcCompanyJoinClueVO> getDcCompanyJoinClueList(DcCompanyJoinClueListSearch params);

    DcCompanyJoinClueVO getDcCompanyJoinClueById(Integer id);

    DcCompanyJoinClue createDcCompanyJoinClue(DcCompanyJoinClue data);

    boolean updateDcCompanyJoinClue(DcCompanyJoinClue data);

    boolean deleteDcCompanyJoinClueById(Integer id);
}
