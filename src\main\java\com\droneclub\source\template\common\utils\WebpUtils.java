package com.droneclub.source.template.common.utils;

import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;

@Slf4j
public class WebpUtils {

    /**
     * webp格式
     */
    public static final String WEBP_FORMAT = "webp";
    /**
     * webp content type
     */
    public static final String WEBP_CONTENT_TYPE = "image/webp";
    /**
     * webp最大尺寸
     */
    private static final int WEBP_MAX_DIMENSION = 16383;

    /**
     * 对图片文件进行webp编码
     *
     * @param filePath          文件路径
     * @param originInputStream 文件字节流
     * @return 转换后的字节流
     */
    public static InputStream encode(String filePath, InputStream originInputStream) {
        BufferedImage image;
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            image = ImageIO.read(originInputStream);
            if (image == null) {
                return null;
            }
            if (image.getHeight() > WEBP_MAX_DIMENSION || image.getWidth() > WEBP_MAX_DIMENSION) {
                return null;
            }
            ImageIO.write(image, WEBP_FORMAT, outputStream);
        } catch (Exception e) {
            log.error("convert webp error: {}", filePath, e);
            return null;
        }
        return new ByteArrayInputStream(outputStream.toByteArray());
    }

    public static void main(String[] args) throws IOException {
        String filePath = "";
        String webpPath = "";
        // Obtain an image to encode from somewhere
        BufferedImage image = ImageIO.read(new File(filePath));

        // Encode it as webp using default settings
        ImageIO.write(image, WEBP_FORMAT, new File(webpPath));
    }

}
