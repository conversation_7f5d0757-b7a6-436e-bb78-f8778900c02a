package com.droneclub.source.template.common.utils;

import com.droneclub.source.template.common.cache.CompanyCache;
import com.droneclub.source.template.entity.DcCompany;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

public class CompanyUtils {

    /**
     * 根据companyCode获取公司信息
     * @param companyCode 公司编码
     * @return Optional包装的DcCompany对象（避免NPE）
     */
    public static DcCompany getCompany(String companyCode) {
        return CompanyCache.COMPANY_MAP.get(companyCode);
    }

    private static Optional<DcCompany> getCompanyOptional(String companyCode) {
        return Optional.ofNullable(CompanyCache.COMPANY_MAP.get(companyCode));
    }

    /**
     * 检查公司系统有效期是否有效
     * @param companyCode 公司编码
     * @return true=有效，false=无效或不存在
     */
    public static Boolean isSystemValid(String companyCode) {
        return getCompanyOptional(companyCode)
                .map(company -> {
                    String expiryDateStr = company.getSystemValidityPeriod();
                    if (expiryDateStr == null) return false;

                    LocalDate expiryDate = LocalDate.parse(
                            expiryDateStr,
                            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                    );
                    return !LocalDate.now().isAfter(expiryDate);
                })
                .orElse(null);
    }
}
