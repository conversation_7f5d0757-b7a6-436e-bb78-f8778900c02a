package com.droneclub.source.template.module.service;

import com.droneclub.source.template.entity.DcCompanyProduct;
import com.droneclub.source.template.module.pojo.DcCompanyProductListSearch;
import com.droneclub.source.template.module.pojo.DcCompanyProductVO;

import java.util.List;

public interface IDcCompanyProductService {

    List<DcCompanyProductVO> getDcCompanyProductList(DcCompanyProductListSearch params);

    DcCompanyProductVO getDcCompanyProductById(Integer id);

    DcCompanyProduct createDcCompanyProduct(DcCompanyProduct data);

    boolean updateDcCompanyProduct(DcCompanyProduct data);

    boolean deleteDcCompanyProductById(Integer id);
}
