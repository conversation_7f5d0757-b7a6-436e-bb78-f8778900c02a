package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("dc_company_discounts")
public class DcCompanyDiscounts {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer companyId;
    private String discountsName;
    private String discountsProfiles;
    /**
     * 购买金额
     */
    private String buyingPrice;
    /**
     * 抵扣金额
     */
    private String deductiblePrice;
    private Integer discountsStatus;
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    private String createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer updateUser;
    private String updateTime;
    private String isDelete;

}
