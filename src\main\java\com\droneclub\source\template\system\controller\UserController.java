package com.droneclub.source.template.system.controller;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.User;
import com.droneclub.source.template.system.pojo.OpenMember;
import com.droneclub.source.template.system.pojo.UserListSearch;
import com.droneclub.source.template.system.pojo.UserVO;
import com.droneclub.source.template.system.service.IUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/user")
public class UserController {

    private final IUserService userService;

    @GetMapping("/getUserOption")
    public RestResult<List<User>> getUserOption(UserListSearch params) {
        return RestResult.success(userService.getUserOption(params));
    }

    @GetMapping("/getUserList")
    public RestResult<ListData<UserVO>> getUserList(UserListSearch params) {
        return RestResult.success(userService.getUserList(params));
    }

    @GetMapping("/getUserById")
    public RestResult<UserVO> getUserById(String id) {
        return RestResult.success(userService.getUserById(id));
    }

    @PostMapping("/createUser")
    public RestResult<User> createUser(@RequestBody User data) {
        return RestResult.success(userService.createUser(data));
    }

    @PutMapping("/updateUser")
    public RestResult<Boolean> updateUser(@RequestBody User data) {
        return RestResult.success(userService.updateUser(data));
    }

    @DeleteMapping("/deleteUserById")
    public RestResult<Boolean> deleteUserById(String id) {
        return RestResult.success(userService.deleteUserById(id));
    }


    @PostMapping("/openMember")
    public RestResult<Boolean> openMember(@RequestBody OpenMember openMember) {
        if (openMember == null) {
            return RestResult.failure(ResultCode.LACK_PARAMS);
        }
        if (!"dab5a1fce774416da4dbf4959d5537a8".equals(openMember.getKey())) {
            return RestResult.failure(ResultCode.LACK_PARAMS.getCode(), "非法请求");
        }
        return RestResult.success(userService.openMember(openMember));
    }
}
