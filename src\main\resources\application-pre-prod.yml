server:
  port: 8777
  servlet:
    # 项目通用接口前缀
    context-path: /drone-club

spring:
  application:
    name: drone-club
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  mvc:
    # 出现异常时直接抛出
    throw-exception-if-no-handler-found: true
  web:
    resources:
      # 项目内资源不建立映射
      add-mappings: false
  servlet:
    multipart:
      max-file-size: 30MB
      max-request-size: 30MB
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************************************************************************************
    username: elephant
    password: elephant#1216
    hikari:
      minimum-idle: 3
      maximum-pool-size: 10
      max-lifetime: 30000
  redis:
    host: 127.0.0.1
    port: 6377
    timeout: 20000
    password: kqgkxeoq
    database: 1
  freemarker:
    cache: false
    template-loader-path: classpath:templates

mybatis:
  mapper-locations: classpath:mapper/*.xml

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: is_delete
      logic-delete-value: 1
      logic-not-delete-value: 0

logging:
  config: classpath:log4j2.xml

source-builder:
  srcDir: D:\git-project\drone-club\src\main\java\com\droneclub\source\template
  ignoreTable:
    - sys_user

# http://************:7901/login
minio:
  # 文件上次使用内网路径
  serverUrl: http://127.0.0.1:7900/
  previewUrl: https://www.dxauto.online/resource/
  bucket: drone-club
  accessKey: eleprod
  secretKey: 71c3f433de1a

email:
  username: <EMAIL>
  password: APYDOSTDPDUOEEHQ
  receiver: <EMAIL>,<EMAIL>,<EMAIL>

wechat:
  appId: wxbdd858eda708eecc
  secret: eb0434d8e4b16b52554f927df6e2707f
  programState: formal
  indexPage: pages/home/<USER>
  accessTokenUrl: https://api.weixin.qq.com/cgi-bin/token
  getUserPhoneUrl: https://api.weixin.qq.com/wxa/business/getuserphonenumber
  getUserLoginSessionUrl: https://api.weixin.qq.com/sns/jscode2session
  sendMessageUrl: https://api.weixin.qq.com/cgi-bin/message/subscribe/send
  contentAuditUrl: https://api.weixin.qq.com/wxa/msg_sec_check
  mediaAuditUrl: https://api.weixin.qq.com/wxa/media_check_async

sm2:
  request-public-key-hax: 04d4c47a78d0b43f8397cb2034c56b848e2b64163100c754ba28c0206418d5a3b6b7e6517f810a20fa2b21da0746a02446b8d70f8e460fa87ff497b75c30b0dca5
  request-private-key-hax: 009acba22f3eaca6028bb104f77a628c8902e9206808ab25cacfbeafac71060c22
  response-public-key-hax: 045bb061ebcc3ccaebd78785e14d6d8565b31f3ac9384bd75e71395631114c63aa9fe1d47361e74e24587df2dafb305ef1e97ebffe1debc40703d483e2f92727f2
  response-private-key-hax: 5c2444d94f146ba33cf5e1bd5a0e02d4f8bdbdec9ec96f7b1d19c26e765df40c

downloadResource:
  tkPdf: /home/<USER>/无人机试题与答案解析.pdf