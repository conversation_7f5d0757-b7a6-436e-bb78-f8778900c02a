﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-75px;
  width:787px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u310_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:758px;
  background:inherit;
  background-color:rgba(245, 245, 245, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u310 {
  border-width:0px;
  position:absolute;
  left:75px;
  top:31px;
  width:375px;
  height:758px;
  display:flex;
}
#u310 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u310_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u311_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:45px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u311 {
  border-width:0px;
  position:absolute;
  left:75px;
  top:31px;
  width:375px;
  height:45px;
  display:flex;
}
#u311 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u311_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u312_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u312 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:46px;
  width:84px;
  height:16px;
  display:flex;
}
#u312 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u312_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u313_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u313 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:44px;
  width:20px;
  height:20px;
  display:flex;
}
#u313 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u313_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u314_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:178px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-top:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u314 {
  border-width:0px;
  position:absolute;
  left:75px;
  top:77px;
  width:375px;
  height:178px;
  display:flex;
}
#u314 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u314_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u315_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:80px;
}
#u315 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:94px;
  width:80px;
  height:80px;
  display:flex;
}
#u315 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u315_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u316_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u316 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:186px;
  width:70px;
  height:16px;
  display:flex;
}
#u316 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u316_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u317_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:16px;
}
#u317 {
  border-width:0px;
  position:absolute;
  left:211px;
  top:209px;
  width:99px;
  height:16px;
  display:flex;
  font-size:10px;
}
#u317 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u317_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u318_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:361px;
  height:157px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u318 {
  border-width:0px;
  position:absolute;
  left:82px;
  top:260px;
  width:361px;
  height:157px;
  display:flex;
}
#u318 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u318_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u319_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:361px;
  height:356px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u319 {
  border-width:0px;
  position:absolute;
  left:82px;
  top:426px;
  width:361px;
  height:356px;
  display:flex;
}
#u319 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u319_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u320_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:9px;
  height:21px;
}
#u320 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:272px;
  width:4px;
  height:16px;
  display:flex;
}
#u320 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u320_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u321_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u321 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:272px;
  width:56px;
  height:16px;
  display:flex;
}
#u321 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u321_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u322_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u322 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:333px;
  width:70px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u322 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u322_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u323_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u323 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:333px;
  width:70px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u323 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u323_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u324_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u324 {
  border-width:0px;
  position:absolute;
  left:344px;
  top:333px;
  width:70px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u324 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u324_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u325_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u325 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:305px;
  width:41px;
  height:21px;
  display:flex;
  font-size:18px;
}
#u325 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u325_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u326_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u326 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:305px;
  width:41px;
  height:21px;
  display:flex;
  font-size:18px;
}
#u326 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u326_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u327_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u327 {
  border-width:0px;
  position:absolute;
  left:356px;
  top:305px;
  width:47px;
  height:21px;
  display:flex;
  font-size:18px;
}
#u327 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u327_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u328_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:9px;
  height:21px;
}
#u328 {
  border-width:0px;
  position:absolute;
  left:99px;
  top:442px;
  width:4px;
  height:16px;
  display:flex;
}
#u328 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u328_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u329_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u329 {
  border-width:0px;
  position:absolute;
  left:110px;
  top:442px;
  width:56px;
  height:16px;
  display:flex;
}
#u329 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u329_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u330_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:170px;
}
#u330 {
  border-width:0px;
  position:absolute;
  left:110px;
  top:503px;
  width:300px;
  height:170px;
  display:flex;
}
#u330 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u330_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u331_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u331 {
  border-width:0px;
  position:absolute;
  left:233px;
  top:578px;
  width:54px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u331 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u331_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u332_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:44px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u332 {
  border-width:0px;
  position:absolute;
  left:242px;
  top:626px;
  width:44px;
  height:16px;
  display:flex;
}
#u332 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u332_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u333_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:135px;
  height:32px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:65px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u333 {
  border-width:0px;
  position:absolute;
  left:193px;
  top:370px;
  width:135px;
  height:32px;
  display:flex;
}
#u333 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u333_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u334_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:758px;
  background:inherit;
  background-color:rgba(245, 245, 245, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u334 {
  border-width:0px;
  position:absolute;
  left:487px;
  top:31px;
  width:375px;
  height:758px;
  display:flex;
}
#u334 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u334_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u335_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:45px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u335 {
  border-width:0px;
  position:absolute;
  left:487px;
  top:31px;
  width:375px;
  height:45px;
  display:flex;
}
#u335 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u335_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u336_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u336 {
  border-width:0px;
  position:absolute;
  left:610px;
  top:46px;
  width:112px;
  height:16px;
  display:flex;
}
#u336 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u336_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u337_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u337 {
  border-width:0px;
  position:absolute;
  left:499px;
  top:44px;
  width:20px;
  height:20px;
  display:flex;
}
#u337 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u337_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u338_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:178px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-top:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u338 {
  border-width:0px;
  position:absolute;
  left:487px;
  top:77px;
  width:375px;
  height:178px;
  display:flex;
}
#u338 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u338_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u339_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:80px;
}
#u339 {
  border-width:0px;
  position:absolute;
  left:632px;
  top:94px;
  width:80px;
  height:80px;
  display:flex;
}
#u339 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u339_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u340_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u340 {
  border-width:0px;
  position:absolute;
  left:637px;
  top:186px;
  width:70px;
  height:16px;
  display:flex;
}
#u340 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u340_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u341_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:16px;
}
#u341 {
  border-width:0px;
  position:absolute;
  left:623px;
  top:209px;
  width:99px;
  height:16px;
  display:flex;
  font-size:10px;
}
#u341 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u341_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u342_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:361px;
  height:522px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u342 {
  border-width:0px;
  position:absolute;
  left:494px;
  top:260px;
  width:361px;
  height:522px;
  display:flex;
}
#u342 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u342_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u343_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:9px;
  height:21px;
}
#u343 {
  border-width:0px;
  position:absolute;
  left:509px;
  top:272px;
  width:4px;
  height:16px;
  display:flex;
}
#u343 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u343_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u344_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u344 {
  border-width:0px;
  position:absolute;
  left:520px;
  top:272px;
  width:56px;
  height:16px;
  display:flex;
}
#u344 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u344_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u345_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u345 {
  border-width:0px;
  position:absolute;
  left:520px;
  top:333px;
  width:70px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u345 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u345_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u346_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u346 {
  border-width:0px;
  position:absolute;
  left:637px;
  top:333px;
  width:70px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u346 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u346_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u347_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u347 {
  border-width:0px;
  position:absolute;
  left:756px;
  top:333px;
  width:70px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u347 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u347_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u348_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u348 {
  border-width:0px;
  position:absolute;
  left:535px;
  top:305px;
  width:41px;
  height:21px;
  display:flex;
  font-size:18px;
}
#u348 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u348_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u349_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u349 {
  border-width:0px;
  position:absolute;
  left:652px;
  top:305px;
  width:41px;
  height:21px;
  display:flex;
  font-size:18px;
}
#u349 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u349_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u350_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u350 {
  border-width:0px;
  position:absolute;
  left:768px;
  top:305px;
  width:47px;
  height:21px;
  display:flex;
  font-size:18px;
}
#u350 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u350_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u351_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:327px;
  height:2px;
}
#u351 {
  border-width:0px;
  position:absolute;
  left:509px;
  top:370px;
  width:326px;
  height:1px;
  display:flex;
}
#u351 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u351_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u352_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u352 {
  border-width:0px;
  position:absolute;
  left:505px;
  top:400px;
  width:116px;
  height:16px;
  display:flex;
}
#u352 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u352_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u353_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u353 {
  border-width:0px;
  position:absolute;
  left:505px;
  top:434px;
  width:92px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u353 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u353_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u354_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u354 {
  border-width:0px;
  position:absolute;
  left:672px;
  top:434px;
  width:94px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u354 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u354_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u355_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u355 {
  border-width:0px;
  position:absolute;
  left:505px;
  top:471px;
  width:116px;
  height:16px;
  display:flex;
}
#u355 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u355_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u356_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u356 {
  border-width:0px;
  position:absolute;
  left:505px;
  top:505px;
  width:92px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u356 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u356_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u357_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u357 {
  border-width:0px;
  position:absolute;
  left:672px;
  top:505px;
  width:94px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u357 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u357_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u358_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:533px;
  height:231px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-size:16px;
}
#u358 {
  border-width:0px;
  position:absolute;
  left:82px;
  top:801px;
  width:533px;
  height:231px;
  display:flex;
  font-size:16px;
}
#u358 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u358_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
