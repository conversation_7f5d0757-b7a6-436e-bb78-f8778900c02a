package com.droneclub.source.template.common.utils;


import com.droneclub.source.template.common.model.TemplateCurrentUser;

public class TemplateSessionUtils {
    private static final ThreadLocal<TemplateCurrentUser> SESSION_THREAD_LOCAL = new ThreadLocal<>();

    public static TemplateCurrentUser getCurrentUser() {
        return SESSION_THREAD_LOCAL.get();
    }

    public static void setCurrentUser(TemplateCurrentUser user) {
        SESSION_THREAD_LOCAL.set(user);
    }

    public static void removeCurrentUser() {
        SESSION_THREAD_LOCAL.remove();
    }
}
