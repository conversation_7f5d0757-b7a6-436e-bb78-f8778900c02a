package com.droneclub.source.template.common.utils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.lang.reflect.Field;

public class MybatisPlusUtils {

    public static <T> QueryWrapper<T> generateQuery(T object) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        Field[] fields = object.getClass().getDeclaredFields();
        try {
            for (Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(object);
                if (value != null) {
                    queryWrapper.eq(field.getName(), value);
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return queryWrapper;
    }

}
