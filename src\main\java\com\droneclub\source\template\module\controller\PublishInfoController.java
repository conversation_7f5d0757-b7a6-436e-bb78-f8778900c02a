package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.decrypt.ResponseEncryption;
import com.droneclub.source.template.entity.PublishInfo;
import com.droneclub.source.template.module.pojo.PublishInfoListSearch;
import com.droneclub.source.template.module.pojo.PublishInfoVO;
import com.droneclub.source.template.module.service.IPublishInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/publishInfo")
public class PublishInfoController {

    private final IPublishInfoService publishInfoService;

    @ResponseEncryption
    @PostMapping("/getPublishInfoList")
    public RestResult<ListData<PublishInfoVO>> getPublishInfoList(@RequestBody PublishInfoListSearch params) {
        return RestResult.success(publishInfoService.getPublishInfoList(params));
    }

    @ResponseEncryption
    @GetMapping("/getPublishInfoById")
    public RestResult<PublishInfoVO> getPublishInfoById(Integer id) {
        return RestResult.success(publishInfoService.getPublishInfoById(id));
    }

    @ResponseEncryption
    @PostMapping("/createPublishInfo")
    public RestResult<PublishInfo> createPublishInfo(@RequestBody PublishInfo data) {
        return RestResult.success(publishInfoService.createPublishInfo(data));
    }

    @PutMapping("/updatePublishInfo")
    public RestResult<Boolean> updatePublishInfo(@RequestBody PublishInfo data) {
        return RestResult.success(publishInfoService.updatePublishInfo(data));
    }

    @DeleteMapping("/deletePublishInfoById")
    public RestResult<Boolean> deletePublishInfoById(Integer id) {
        return RestResult.success(publishInfoService.deletePublishInfoById(id));
    }
}
