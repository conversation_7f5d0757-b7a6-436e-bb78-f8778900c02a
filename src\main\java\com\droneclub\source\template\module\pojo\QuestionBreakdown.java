package com.droneclub.source.template.module.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 题目解析详情类，包含题目相关信息和AI分析结果
 * <p>
 * 该类用于表示一个完整题目的解析内容，包括基础信息和各个分析部分
 * </p>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QuestionBreakdown {
    /**
     * 章节名称，标识题目所属的章节
     */
    private String chapterName;

    /**
     * 主题名称，标识题目所属的具体主题
     */
    private String topicName;

    /**
     * 题目选项，表示题目的选项标识（如A、B、C等）
     */
    private String topicOption;

    /**
     * 题目答案，存储题目的正确答案
     */
    private String questionAnswer;

    /**
     * AI问题分析，包含AI对题目的整体分析内容
     */
    private String aiQuestionAnalysis;

    /**
     * 背景分析，描述题目相关的背景知识和上下文信息
     */
    private String background;

    /**
     * 关键概念，列出题目涉及的核心概念和知识点
     */
    private String keyConcepts;

    /**
     * 选项分析，详细解析每个选项的含义和正确性
     */
    private String optionAnalysis;

    /**
     * 常见错误，总结学生解答该题目时常见的错误类型
     */
    private String frequentErrors;

    /**
     * 结论总结，对题目分析的最终总结和归纳
     */
    private String conclusion;
}