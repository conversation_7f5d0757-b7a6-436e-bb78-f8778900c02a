package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.execption.ZkException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.common.model.TemplateCurrentUser;
import com.droneclub.source.template.common.utils.TemplateSessionUtils;
import com.droneclub.source.template.constants.TopicConstant;
import com.droneclub.source.template.entity.*;
import com.droneclub.source.template.mapper.*;
import com.droneclub.source.template.module.pojo.AnswerStats;
import com.droneclub.source.template.module.pojo.DcOrganizationStudentSearch;
import com.droneclub.source.template.module.pojo.DcOrganizationStudentVO;
import com.droneclub.source.template.module.service.IDcCompanyStudentService;
import com.keelcloud.sdk.javabase.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class DcCompanyStudentServiceImpl implements IDcCompanyStudentService {

    private final DcCompanyStudentMapper studentMapper;
    private final DcExamRecordMapper examRecordMapper;
    private final DcAllTopicRecordMapper dcAllTopicRecordMapper;

    private final TmUserMapper userMapper;
    private final DcCompanyProductMapper dcCompanyProductMapper;

    @Override
    public ListData<DcOrganizationStudentVO> getStudentList(DcOrganizationStudentSearch search) {
        LambdaQueryWrapper<DcCompanyStudent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(search.getCompanyId() != null, DcCompanyStudent::getCompanyId, search.getCompanyId());
        wrapper.eq(search.getCoachId() != null, DcCompanyStudent::getCoachId, search.getCoachId());
        wrapper.like(StringUtils.isValid(search.getStudentName()), DcCompanyStudent::getStudentName, search.getStudentName());
        wrapper.eq(StringUtils.isValid(search.getStudentCourse()), DcCompanyStudent::getStudentCourse, search.getStudentCourse());
        wrapper.eq(StringUtils.isValid(search.getStudentStage()), DcCompanyStudent::getStudentStage, search.getStudentStage());

        Page<DcCompanyStudent> page = studentMapper.selectPage(
                new Page<>(search.getPageNo(), search.getPageSize()),
                wrapper
        );

        List<DcOrganizationStudentVO> voList = page.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        Set<Integer> userIds = voList.stream().map(DcOrganizationStudentVO::getStudentId).collect(Collectors.toSet());
        if (!userIds.isEmpty()) {
            // 获取用户信息
            LambdaQueryWrapper<User> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
            userLambdaQueryWrapper.in(User::getId, userIds);
            List<User> userList = userMapper.selectList(userLambdaQueryWrapper);
            Map<Integer, User> userIdUserMap = userList.stream()
                    .collect(Collectors.toMap(
                            User::getId,  // 使用用户ID作为Map的key
                            user -> user, // 用户对象本身作为value
                            (existing, replacement) -> existing  // 如果key冲突，保留已存在的值
                    ));

            for (DcOrganizationStudentVO studentVO : voList) {
                studentVO.setUser(userIdUserMap.get(studentVO.getStudentId()));
                // 获取最近一次基础考试记录
                List<DcExamRecord> examRecords = getRecentExamRecords(studentVO.getStudentId(), search.getExamTopicType(), 1);
                if (!examRecords.isEmpty()) {
                    studentVO.setExamRecord(examRecords.get(0));
                }
            }
        }
        Set<String> productIds = voList.stream().map(DcOrganizationStudentVO::getStudentCourse).collect(Collectors.toSet());
        if (!productIds.isEmpty()) {
            LambdaQueryWrapper<DcCompanyProduct> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
            userLambdaQueryWrapper.in(DcCompanyProduct::getId, productIds);
            List<DcCompanyProduct> products = dcCompanyProductMapper.selectList(userLambdaQueryWrapper);
            Map<Integer, DcCompanyProduct> productMap = products.stream()
                    .collect(Collectors.toMap(
                            DcCompanyProduct::getId,  // 使用用户ID作为Map的key
                            product -> product, // 用户对象本身作为value
                            (existing, replacement) -> existing  // 如果key冲突，保留已存在的值
                    ));
            for (DcOrganizationStudentVO studentVO : voList) {
                if (StringUtils.hasText(studentVO.getStudentCourse()) && isInteger(studentVO.getStudentCourse())) {
                    if (productMap.get(Integer.parseInt(studentVO.getStudentCourse())) != null) {
                        studentVO.setStudentCourse(productMap.get(Integer.parseInt(studentVO.getStudentCourse())).getProductName());
                    } else {
                        studentVO.setStudentCourse("--");
                    }
                }
            }
        }
        ListData<DcOrganizationStudentVO> listData = new ListData<>(page.getTotal(), search.getPageNo(), search.getPageSize());
        listData.setList(voList);
        return listData;
    }

    public static boolean isInteger(String str) {
        try {
            Integer.parseInt(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    @Override
    public DcOrganizationStudentVO getStudentById(Integer id) {
        // 基础查询与学生存在性校验
        DcCompanyStudent student = studentMapper.selectById(id);
        if (student == null) return null;

        // 对象转换与基础属性设置
        DcOrganizationStudentVO studentVO = convertToVO(student);

        // 获取答题统计数据 - 基础答题
        AnswerStats stats = dcAllTopicRecordMapper.getAnswerStatsByUserId(student.getStudentId(), Arrays.asList(TopicConstant.ChapterNameClass.BASE_CLASS));
        studentVO.setTotalAnswerCount(stats.getTotalCount() != null ? stats.getTotalCount() : 0L);
        studentVO.setCorrectAnswerCount(stats.getCorrectCount() != null ? stats.getCorrectCount() : 0L);


        // 获取答题统计数据 - 综合答题
        AnswerStats zhStats = dcAllTopicRecordMapper.getAnswerStatsByUserId(student.getStudentId(), Arrays.asList(TopicConstant.ChapterNameClass.ZH_CLASS));
        studentVO.setZhTotalAnswerCount(zhStats.getTotalCount() != null ? zhStats.getTotalCount() : 0L);
        studentVO.setZhCorrectAnswerCount(zhStats.getCorrectCount() != null ? zhStats.getCorrectCount() : 0L);

        // 获取答题统计数据 - 教员答题
        AnswerStats jyStats = dcAllTopicRecordMapper.getAnswerStatsByUserId(student.getStudentId(), Arrays.asList(TopicConstant.ChapterNameClass.JY_CLASS));
        studentVO.setJyTotalAnswerCount(jyStats.getTotalCount() != null ? jyStats.getTotalCount() : 0L);
        studentVO.setJyCorrectAnswerCount(jyStats.getCorrectCount() != null ? jyStats.getCorrectCount() : 0L);

        // 计算正确率（防除零处理+精度控制）
        // calculateAndSetCorrectRate(studentVO, stats);

        // 获取最近考试记录
        studentVO.setExamRecords(getRecentExamRecords(student.getStudentId(), "base", 10));
        studentVO.setZhExamRecords(getRecentExamRecords(student.getStudentId(), "zh", 10));

        User user = userMapper.selectById(studentVO.getStudentId());
        studentVO.setUser(user);

        DcCompanyProduct product = dcCompanyProductMapper.selectById(studentVO.getStudentCourse());
        if (product != null) {
            studentVO.setStudentCourse(product.getProductName());
        } else {
            studentVO.setStudentCourse("--");
        }
        return studentVO;
    }


    private List<DcExamRecord> getRecentExamRecords(Integer studentId, String examTopicType, Integer size) {
        LambdaQueryWrapper<DcExamRecord> examRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        examRecordLambdaQueryWrapper.eq(DcExamRecord::getUserId, studentId)
                .isNotNull(DcExamRecord::getExamScore)
                .orderByDesc(DcExamRecord::getId)
                .last("LIMIT " + size);
        if (StringUtils.isValid(examTopicType) && "zh".equals(examTopicType)) {
            examRecordLambdaQueryWrapper.eq(DcExamRecord::getExamTopicType, "zh");
        } else {
            examRecordLambdaQueryWrapper.and(wrapper ->
                    wrapper.eq(DcExamRecord::getExamTopicType, "base")
                            .or()
                            .isNull(DcExamRecord::getExamTopicType)
            );
        }
        return examRecordMapper.selectList(examRecordLambdaQueryWrapper);
    }

    private void calculateAndSetCorrectRate(DcOrganizationStudentVO vo, AnswerStats stats) {
        if (stats.getTotalCount() == null || stats.getTotalCount() == 0) {
            vo.setCorrectAnswerRate(0.0);
            return;
        }
        BigDecimal rate = BigDecimal.valueOf(stats.getCorrectCount())
                .divide(BigDecimal.valueOf(stats.getTotalCount()), 4, RoundingMode.HALF_UP);
        vo.setCorrectAnswerRate(rate.doubleValue());
    }

    @Override
    public DcCompanyStudent createStudent(DcCompanyStudent student) {
        // 教练id为空时从当前会话中取
        if (student.getCoachId() == null) {
            TemplateCurrentUser currentUser = TemplateSessionUtils.getCurrentUser();
            student.setCoachId(currentUser.getId());
        }
        // 根据userId查询, 进行校验
        LambdaQueryWrapper<DcCompanyStudent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DcCompanyStudent::getStudentId, student.getStudentId());
        DcCompanyStudent exist = studentMapper.selectOne(wrapper);
        if (exist != null && !student.getCompanyId().equals(exist.getCompanyId())) {
            throw new ZkException("已绑定其他机构, 请先联系教练解绑");
        }
        studentMapper.insert(student);
        return student;
    }

    @Override
    public Boolean updateStudent(DcCompanyStudent student) {
        return studentMapper.updateById(student) > 0;
    }

    @Override
    public Boolean deleteStudentById(Integer id) {
        return studentMapper.deleteById(id) > 0;
    }

    private DcOrganizationStudentVO convertToVO(DcCompanyStudent student) {
        DcOrganizationStudentVO vo = new DcOrganizationStudentVO();
        BeanUtils.copyProperties(student, vo);
        
        /*// 获取公司名称
        DcCompany company = companyMapper.selectById(student.getCompanyId());
        if (company != null) {
            vo.setCompanyName(company.getCompanyName());
        }
        
        // 获取教练名称
        DcCompanyMember coach = memberMapper.selectById(student.getCoachId());
        if (coach != null) {
            vo.setCoachName(coach.getMemberName());
        }*/

        return vo;
    }
} 