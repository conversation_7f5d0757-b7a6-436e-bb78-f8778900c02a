package com.droneclub.source.template.single.region.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.droneclub.source.template.single.region.common.RegionStatusEnum;
import com.droneclub.source.template.single.region.entity.SysCityEntity;
import com.droneclub.source.template.single.region.entity.SysDistrictEntity;
import com.droneclub.source.template.single.region.entity.SysProvinceEntity;
import com.droneclub.source.template.single.region.mapper.SysCityMapper;
import com.droneclub.source.template.single.region.mapper.SysDistrictMapper;
import com.droneclub.source.template.single.region.mapper.SysProvinceMapper;
import com.droneclub.source.template.single.region.pojo.RegionTreeVO;
import com.droneclub.source.template.single.region.pojo.SysCityVO;
import com.droneclub.source.template.single.region.pojo.SysDistrictVO;
import com.droneclub.source.template.single.region.pojo.SysProvinceVO;
import com.droneclub.source.template.single.region.service.ISysRegionService;
import com.droneclub.source.template.single.region.utils.PinyinUtils;
import com.keelcloud.sdk.javabase.enums.HttpMethod;
import com.keelcloud.sdk.javabase.http.HttpToolkit;
import com.keelcloud.sdk.javabase.http.Response;
import com.keelcloud.sdk.javabase.utils.ConvertUtils;
import com.keelcloud.sdk.javabase.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 区域服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysRegionServiceImpl implements ISysRegionService {

    private final SysProvinceMapper provinceMapper;
    private final SysCityMapper cityMapper;
    private final SysDistrictMapper districtMapper;
    
    @Value("${amap.key:130ae5e03236585992a986733954bb4c}")
    private String amapKey;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncRegionData() {
        try {
            // 调用高德地图API获取省市区数据
            String url = String.format("https://restapi.amap.com/v3/config/district?key=%s&keywords=中国&subdistrict=3", amapKey);
            JSONObject areaDataJson;
            try (Response response = HttpToolkit.request(url, HttpMethod.GET)
                    .paramsRequest()
                    .sync()) {
                areaDataJson = response.getJSONObject();
            } catch (Exception e) {
                log.error("调用高德地图API失败", e);
                return false;
            }

            // 解析省市区数据
            JSONArray districts = areaDataJson.getJSONArray("districts");
            if (districts == null || districts.isEmpty()) {
                log.error("未获取到省市区数据");
                return false;
            }
            
            // 获取中国下的所有省份
            JSONObject chinaData = districts.getJSONObject(0);
            JSONArray provinces = chinaData.getJSONArray("districts");
            if (provinces == null || provinces.isEmpty()) {
                log.error("未获取到省份数据");
                return false;
            }
            
            // 数据变化统计
            int totalDeleted = 0;
            int totalUpdated = 0;
            int totalAdded = 0;
            
            // 同步省份数据
            int[] provinceChanges = syncProvinceData(provinces);
            totalDeleted += provinceChanges[0];
            totalUpdated += provinceChanges[1];
            totalAdded += provinceChanges[2];
            
            // 同步城市数据
            int[] cityChanges = syncCityData(provinces);
            totalDeleted += cityChanges[0];
            totalUpdated += cityChanges[1];
            totalAdded += cityChanges[2];
            
            // 同步区县数据
            int[] districtChanges = syncDistrictData(provinces);
            totalDeleted += districtChanges[0];
            totalUpdated += districtChanges[1];
            totalAdded += districtChanges[2];
            
            // 记录同步结果
            if (totalDeleted == 0 && totalUpdated == 0 && totalAdded == 0) {
                log.info("同步省市区数据完成：无数据变化");
            } else {
                log.info("同步省市区数据完成：删除 {} 条，更新 {} 条，新增 {} 条", 
                        totalDeleted, totalUpdated, totalAdded);
            }
            
            return true;
        } catch (Exception e) {
            log.error("同步省市区数据失败", e);
            throw e;
        }
    }
    
    /**
     * 同步省份数据
     * 
     * @param provinces 省份JSON数组
     * @return 数据变化统计[删除数, 更新数, 新增数]
     */
    private int[] syncProvinceData(JSONArray provinces) {
        // 获取现有省份数据
        List<SysProvinceEntity> existingProvinces = provinceMapper.selectList(null);
        // 使用provinceCode作为键，判断省份是否存在
        Map<String, SysProvinceEntity> existingProvinceMap = existingProvinces.stream()
                .collect(Collectors.toMap(
                    SysProvinceEntity::getProvinceCode,
                    province -> province,
                    (existing, replacement) -> existing // 如果有重复编码，保留第一个
                ));
        
        // 新省份列表
        List<SysProvinceEntity> newProvinces = new ArrayList<>();
        // 更新省份列表
        List<SysProvinceEntity> updatedProvinces = new ArrayList<>();
        
        // 处理每个省份
        int totalDeleted = 0;
        int totalUpdated = 0;
        int totalAdded = 0;
        
        for (int i = 0; i < provinces.size(); i++) {
            JSONObject provinceJson = provinces.getJSONObject(i);
            String provinceCode = provinceJson.getString("adcode");
            String provinceName = provinceJson.getString("name");
                
            // 创建新的省份实体
            SysProvinceEntity province = new SysProvinceEntity();
            province.setProvinceCode(provinceCode);
            province.setProvinceName(provinceName);
            province.setFirstChar(PinyinUtils.getFirstLetter(provinceName));
            province.setPinyin(PinyinUtils.getFullPinyin(provinceName));

            // 处理区号，为空或[]时设置为null
            String cityCode = provinceJson.getString("citycode");
            if (cityCode == null || cityCode.isEmpty() || "[]".equals(cityCode)) {
                province.setCityCode(null);
            } else {
                province.setCityCode(cityCode);
            }
            
            province.setCenter(provinceJson.getString("center"));
            
            // 仅使用provinceCode查找现有数据
            SysProvinceEntity existingProvince = existingProvinceMap.get(provinceCode);
            if (existingProvince == null) {
                // 新增省份
                province.setStatus(RegionStatusEnum.VALID.getCode());
                province.setCreateTime(LocalDateTime.now());
                province.setUpdateTime(LocalDateTime.now());
                newProvinces.add(province);
                totalAdded++;
            } else {
                // 检查是否有更新（包括名称和其他字段，排除时间戳和ID字段）
                boolean isChanged = !province.getProvinceName().equals(existingProvince.getProvinceName())
                        || !Objects.equals(province.getFirstChar(), existingProvince.getFirstChar())
                        || !Objects.equals(province.getCityCode(), existingProvince.getCityCode())
                        || !Objects.equals(province.getCenter(), existingProvince.getCenter());
                
                if (isChanged) {
                    // 记录更新日志
                    log.debug("省份数据更新 - 更新前: {}", JSON.toJSONString(existingProvince));
                    
                    // 设置更新状态
                    province.setId(existingProvince.getId());
                    province.setStatus(RegionStatusEnum.UPDATED.getCode());
                    province.setCreateTime(existingProvince.getCreateTime());
                    province.setUpdateTime(LocalDateTime.now());
                    updatedProvinces.add(province);
                    totalUpdated++;
                    
                    log.debug("省份数据更新 - 更新后: {}", JSON.toJSONString(province));
                } else {
                    // 数据无变化，保持有效状态
                    existingProvince.setStatus(RegionStatusEnum.VALID.getCode());
                    updatedProvinces.add(existingProvince);
                }
                
                // 从现有数据中移除已处理的省份
                existingProvinceMap.remove(provinceCode);
            }
        }
        
        // 批量插入新省份
        for (SysProvinceEntity province : newProvinces) {
            provinceMapper.insert(province);
        }
        
        // 批量更新省份
        for (SysProvinceEntity province : updatedProvinces) {
            provinceMapper.updateById(province);
        }
        
        // 将剩余未匹配的省份标记为删除
        for (SysProvinceEntity province : existingProvinceMap.values()) {
            province.setStatus(RegionStatusEnum.DELETED.getCode());
            province.setUpdateTime(LocalDateTime.now());
            provinceMapper.updateById(province);
            totalDeleted++;
        }
        
        return new int[]{totalDeleted, totalUpdated, totalAdded};
    }
    
    /**
     * 同步城市数据
     * 
     * @param provinces 省份JSON数组
     * @return 数据变化统计[删除数, 更新数, 新增数]
     */
    private int[] syncCityData(JSONArray provinces) {
        // 获取现有城市数据
        List<SysCityEntity> existingCities = cityMapper.selectList(null);
        // 使用cityCode作为键，判断城市是否存在
        Map<String, SysCityEntity> existingCityMap = existingCities.stream()
                .collect(Collectors.toMap(
                    SysCityEntity::getCityCode,
                    city -> city,
                    (existing, replacement) -> existing // 如果有重复编码，保留第一个
                ));
        
        // 新城市列表
        List<SysCityEntity> newCities = new ArrayList<>();
        // 更新城市列表
        List<SysCityEntity> updatedCities = new ArrayList<>();
        
        // 处理每个省份下的城市
        int totalDeleted = 0;
        int totalUpdated = 0;
        int totalAdded = 0;
        
        for (int i = 0; i < provinces.size(); i++) {
            JSONObject provinceJson = provinces.getJSONObject(i);
            String provinceCode = provinceJson.getString("adcode");
            
            JSONArray cities = provinceJson.getJSONArray("districts");
            if (cities != null && !cities.isEmpty()) {
                for (int j = 0; j < cities.size(); j++) {
                    JSONObject cityJson = cities.getJSONObject(j);
                    String cityCode = cityJson.getString("adcode");
                    String cityName = cityJson.getString("name");
                        
                    // 创建新的城市实体
                    SysCityEntity city = new SysCityEntity();
                    city.setCityCode(cityCode);
                    city.setCityName(cityName);
                    city.setLevel(cityJson.getString("level"));
                    city.setFirstChar(PinyinUtils.getFirstLetter(cityName));
                    city.setPinyin(PinyinUtils.getFullPinyin(cityName));

                    // 处理区号，为空或[]时设置为null
                    String cityAreaCode = cityJson.getString("citycode");
                    if (cityAreaCode == null || cityAreaCode.isEmpty() || "[]".equals(cityAreaCode)) {
                        city.setCityAreaCode(null);
                    } else {
                        city.setCityAreaCode(cityAreaCode);
                    }
                    
                    city.setCenter(cityJson.getString("center"));
                    city.setProvinceCode(provinceCode);
                    
                    // 仅使用cityCode查找现有数据
                    SysCityEntity existingCity = existingCityMap.get(cityCode);
                    if (existingCity == null) {
                        // 新增城市
                        city.setStatus(RegionStatusEnum.VALID.getCode());
                        city.setCreateTime(LocalDateTime.now());
                        city.setUpdateTime(LocalDateTime.now());
                        newCities.add(city);
                        totalAdded++;
                    } else {
                        // 检查是否有更新（包括名称和其他字段，排除时间戳和ID字段）
                        boolean isChanged = !city.getCityName().equals(existingCity.getCityName())
                                || !Objects.equals(city.getLevel(), existingCity.getLevel())
                                || !Objects.equals(city.getFirstChar(), existingCity.getFirstChar())
                                || !Objects.equals(city.getCityAreaCode(), existingCity.getCityAreaCode())
                                || !Objects.equals(city.getCenter(), existingCity.getCenter())
                                || !city.getProvinceCode().equals(existingCity.getProvinceCode());
                        
                        if (isChanged) {
                            // 记录更新日志
                            log.debug("城市数据更新 - 更新前: {}", JSON.toJSONString(existingCity));
                            
                            // 设置更新状态
                            city.setId(existingCity.getId());
                            city.setStatus(RegionStatusEnum.UPDATED.getCode());
                            city.setCreateTime(existingCity.getCreateTime());
                            city.setUpdateTime(LocalDateTime.now());
                            updatedCities.add(city);
                            totalUpdated++;
                            
                            log.debug("城市数据更新 - 更新后: {}", JSON.toJSONString(city));
                        } else {
                            // 数据无变化，保持有效状态
                            existingCity.setStatus(RegionStatusEnum.VALID.getCode());
                            updatedCities.add(existingCity);
                        }
                        
                        // 从现有数据中移除已处理的城市
                        existingCityMap.remove(cityCode);
                    }
                }
            }
        }
        
        // 批量插入新城市
        for (SysCityEntity city : newCities) {
            cityMapper.insert(city);
        }
        
        // 批量更新城市
        for (SysCityEntity city : updatedCities) {
            cityMapper.updateById(city);
        }
        
        // 将剩余未匹配的城市标记为删除
        for (SysCityEntity city : existingCityMap.values()) {
            city.setStatus(RegionStatusEnum.DELETED.getCode());
            city.setUpdateTime(LocalDateTime.now());
            cityMapper.updateById(city);
            totalDeleted++;
        }
        
        return new int[]{totalDeleted, totalUpdated, totalAdded};
    }
    
    /**
     * 同步区县数据
     * 
     * @param provinces 省份JSON数组
     * @return 数据变化统计[删除数, 更新数, 新增数]
     */
    private int[] syncDistrictData(JSONArray provinces) {
        // 获取现有区县数据
        List<SysDistrictEntity> existingDistricts = districtMapper.selectList(null);
        // 使用districtCode+districtName作为复合键，避免重复键异常
        Map<String, SysDistrictEntity> existingDistrictMap = existingDistricts.stream()
                .collect(Collectors.toMap(
                    district -> district.getDistrictCode() + ":" + district.getDistrictName(),
                    district -> district,
                    (existing, replacement) -> existing // 如果仍有冲突，保留第一个
                ));
        
        // 新区县列表
        List<SysDistrictEntity> newDistricts = new ArrayList<>();
        // 更新区县列表
        List<SysDistrictEntity> updatedDistricts = new ArrayList<>();
        
        // 处理每个省份下的城市及区县
        int totalDeleted = 0;
        int totalUpdated = 0;
        int totalAdded = 0;
        
        for (int i = 0; i < provinces.size(); i++) {
            JSONObject provinceJson = provinces.getJSONObject(i);
            String provinceCode = provinceJson.getString("adcode");
            
            JSONArray cities = provinceJson.getJSONArray("districts");
            if (cities != null && !cities.isEmpty()) {
                for (int j = 0; j < cities.size(); j++) {
                    JSONObject cityJson = cities.getJSONObject(j);
                    String cityCode = cityJson.getString("adcode");
                    
                    JSONArray districts = cityJson.getJSONArray("districts");
                    if (districts != null && !districts.isEmpty()) {
                        for (int k = 0; k < districts.size(); k++) {
                            JSONObject districtJson = districts.getJSONObject(k);
                            String districtCode = districtJson.getString("adcode");
                            String districtName = districtJson.getString("name");
                            
                            // 创建新的区县实体
                            SysDistrictEntity district = new SysDistrictEntity();
                            district.setDistrictCode(districtCode);
                            district.setDistrictName(districtName);
                            district.setFirstChar(PinyinUtils.getFirstLetter(districtName));
                            district.setPinyin(PinyinUtils.getFullPinyin(districtName));
                            district.setLevel(districtJson.getString("level"));
                            district.setCenter(districtJson.getString("center"));
                            district.setCityCode(cityCode);
                            district.setProvinceCode(provinceCode);
                            
                            // 使用复合键查找现有数据
                            String compositeKey = districtCode + ":" + districtName;
                            SysDistrictEntity existingDistrict = existingDistrictMap.get(compositeKey);
                            if (existingDistrict == null) {
                                // 新增区县
                                district.setStatus(RegionStatusEnum.VALID.getCode());
                                district.setCreateTime(LocalDateTime.now());
                                district.setUpdateTime(LocalDateTime.now());
                                newDistricts.add(district);
                                totalAdded++;
                                
                                // 每500条数据执行一次批量插入
                                if (newDistricts.size() >= 500) {
                                    batchInsertDistricts(newDistricts);
                                    newDistricts.clear();
                                }
                            } else {
                                // 检查是否有更新（排除时间戳和ID字段）
                                boolean isChanged = !Objects.equals(district.getFirstChar(), existingDistrict.getFirstChar())
                                        || !Objects.equals(district.getLevel(), existingDistrict.getLevel())
                                        || !Objects.equals(district.getCenter(), existingDistrict.getCenter())
                                        || !district.getCityCode().equals(existingDistrict.getCityCode())
                                        || !district.getProvinceCode().equals(existingDistrict.getProvinceCode());
                                
                                if (isChanged) {
                                    // 记录更新日志
                                    log.debug("区县数据更新 - 更新前: {}", JSON.toJSONString(existingDistrict));
                                    
                                    // 设置更新状态
                                    district.setId(existingDistrict.getId());
                                    district.setStatus(RegionStatusEnum.UPDATED.getCode());
                                    district.setCreateTime(existingDistrict.getCreateTime());
                                    district.setUpdateTime(LocalDateTime.now());
                                    updatedDistricts.add(district);
                                    totalUpdated++;
                                    
                                    log.debug("区县数据更新 - 更新后: {}", JSON.toJSONString(district));
                                    
                                    // 每500条数据执行一次批量更新
                                    if (updatedDistricts.size() >= 500) {
                                        batchUpdateDistricts(updatedDistricts);
                                        updatedDistricts.clear();
                                    }
                                } else {
                                    // 数据无变化，保持有效状态
                                    existingDistrict.setStatus(RegionStatusEnum.VALID.getCode());
                                    updatedDistricts.add(existingDistrict);
                                    
                                    // 每500条数据执行一次批量更新
                                    if (updatedDistricts.size() >= 500) {
                                        batchUpdateDistricts(updatedDistricts);
                                        updatedDistricts.clear();
                                    }
                                }
                                
                                // 从现有数据中移除已处理的区县
                                existingDistrictMap.remove(compositeKey);
                            }
                        }
                    }
                }
            }
        }
        
        // 处理剩余的批量数据
        if (!newDistricts.isEmpty()) {
            batchInsertDistricts(newDistricts);
        }
        
        if (!updatedDistricts.isEmpty()) {
            batchUpdateDistricts(updatedDistricts);
        }
        
        // 将剩余未匹配的区县标记为删除，分批处理
        List<SysDistrictEntity> deletedDistricts = new ArrayList<>();
        for (SysDistrictEntity district : existingDistrictMap.values()) {
            district.setStatus(RegionStatusEnum.DELETED.getCode());
            district.setUpdateTime(LocalDateTime.now());
            deletedDistricts.add(district);
            totalDeleted++;
            
            // 每500条执行一次批量更新
            if (deletedDistricts.size() >= 500) {
                batchUpdateDistricts(deletedDistricts);
                deletedDistricts.clear();
            }
        }
        
        // 处理剩余的删除数据
        if (!deletedDistricts.isEmpty()) {
            batchUpdateDistricts(deletedDistricts);
        }
        
        return new int[]{totalDeleted, totalUpdated, totalAdded};
    }
    
    /**
     * 批量插入区县数据
     * 
     * @param districtList 区县数据列表
     */
    private void batchInsertDistricts(List<SysDistrictEntity> districtList) {
        for (SysDistrictEntity district : districtList) {
            // 确保所有必填字段都已设置
            if (district.getDistrictCode() == null || district.getDistrictName() == null || 
                district.getCityCode() == null || district.getProvinceCode() == null || 
                district.getStatus() == null) {
                log.warn("区县数据缺少必填字段: {}", district);
                continue;
            }
            districtMapper.insert(district);
        }
    }
    
    /**
     * 批量更新区县数据
     * 
     * @param districtList 区县数据列表
     */
    private void batchUpdateDistricts(List<SysDistrictEntity> districtList) {
        for (SysDistrictEntity district : districtList) {
            districtMapper.updateById(district);
        }
    }
    
    @Override
    public List<SysProvinceVO> listProvinces() {
        LambdaQueryWrapper<SysProvinceEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.ne(SysProvinceEntity::getStatus, RegionStatusEnum.DELETED.getCode()) // 过滤已删除数据
                .orderByAsc(SysProvinceEntity::getFirstChar) // 先按首字母排序
                .orderByAsc(SysProvinceEntity::getProvinceCode); // 同首字母按省份编码排序
        
        List<SysProvinceEntity> entities = provinceMapper.selectList(wrapper);
        return ConvertUtils.convertList(entities, SysProvinceVO.class);
    }
    
    @Override
    public List<SysCityVO> listCitiesByProvinceCode(String provinceCode) {
        if (!StringUtils.isValid(provinceCode)) {
            return new ArrayList<>();
        }
        
        // 直接使用provinceCode查询城市列表
        LambdaQueryWrapper<SysCityEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysCityEntity::getProvinceCode, provinceCode)
                .ne(SysCityEntity::getStatus, RegionStatusEnum.DELETED.getCode()) // 过滤已删除数据
                .orderByAsc(SysCityEntity::getFirstChar) // 先按首字母排序
                .orderByAsc(SysCityEntity::getCityCode); // 同首字母按城市编码排序
        
        List<SysCityEntity> entities = cityMapper.selectList(wrapper);
        return ConvertUtils.convertList(entities, SysCityVO.class);
    }
    
    @Override
    public List<SysDistrictVO> listDistrictsByCityCode(String cityCode) {
        if (!StringUtils.isValid(cityCode)) {
            return new ArrayList<>();
        }
        
        // 直接使用cityCode查询区县列表
        LambdaQueryWrapper<SysDistrictEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysDistrictEntity::getCityCode, cityCode)
                .ne(SysDistrictEntity::getStatus, RegionStatusEnum.DELETED.getCode()) // 过滤已删除数据
                .orderByAsc(SysDistrictEntity::getFirstChar) // 先按首字母排序
                .orderByAsc(SysDistrictEntity::getDistrictCode); // 同首字母按区县编码排序
        
        List<SysDistrictEntity> entities = districtMapper.selectList(wrapper);
        return ConvertUtils.convertList(entities, SysDistrictVO.class);
    }
    
    @Override
    public List<RegionTreeVO> getCompleteRegionTree() {
        // 获取所有有效省份数据
        LambdaQueryWrapper<SysProvinceEntity> provinceWrapper = new LambdaQueryWrapper<>();
        provinceWrapper.ne(SysProvinceEntity::getStatus, RegionStatusEnum.DELETED.getCode())
                .orderByAsc(SysProvinceEntity::getFirstChar)
                .orderByAsc(SysProvinceEntity::getProvinceCode);
        List<SysProvinceEntity> provinces = provinceMapper.selectList(provinceWrapper);
        
        // 获取所有有效城市数据
        LambdaQueryWrapper<SysCityEntity> cityWrapper = new LambdaQueryWrapper<>();
        cityWrapper.ne(SysCityEntity::getStatus, RegionStatusEnum.DELETED.getCode())
                .orderByAsc(SysCityEntity::getFirstChar)
                .orderByAsc(SysCityEntity::getCityCode);
        List<SysCityEntity> allCities = cityMapper.selectList(cityWrapper);
        
        // 按省份编码分组城市
        Map<String, List<SysCityEntity>> cityMap = allCities.stream()
                .collect(Collectors.groupingBy(SysCityEntity::getProvinceCode));
        
        // 获取所有有效区县数据
        LambdaQueryWrapper<SysDistrictEntity> districtWrapper = new LambdaQueryWrapper<>();
        districtWrapper.ne(SysDistrictEntity::getStatus, RegionStatusEnum.DELETED.getCode())
                .orderByAsc(SysDistrictEntity::getFirstChar)
                .orderByAsc(SysDistrictEntity::getDistrictCode);
        List<SysDistrictEntity> allDistricts = districtMapper.selectList(districtWrapper);
        
        // 按城市编码分组区县
        Map<String, List<SysDistrictEntity>> districtMap = allDistricts.stream()
                .collect(Collectors.groupingBy(SysDistrictEntity::getCityCode));
        
        // 构建树形结构
        List<RegionTreeVO> result = new ArrayList<>();
        
        // 遍历省份
        for (SysProvinceEntity province : provinces) {
            RegionTreeVO provinceNode = new RegionTreeVO();
            provinceNode.setProvinceCode(province.getProvinceCode());
            provinceNode.setProvinceName(province.getProvinceName());
            provinceNode.setFirstChar(province.getFirstChar());
            
            // 获取该省份下的所有城市
            List<SysCityEntity> cities = cityMap.getOrDefault(province.getProvinceCode(), new ArrayList<>());
            List<RegionTreeVO.CityVO> cityVOList = new ArrayList<>();
            
            // 遍历城市
            for (SysCityEntity city : cities) {
                RegionTreeVO.CityVO cityVO = new RegionTreeVO.CityVO();
                cityVO.setCityCode(city.getCityCode());
                cityVO.setCityName(city.getCityName());
                cityVO.setFirstChar(city.getFirstChar());
                
                // 获取该城市下的所有区县
                List<SysDistrictEntity> districts = districtMap.getOrDefault(city.getCityCode(), new ArrayList<>());
                List<RegionTreeVO.DistrictVO> districtVOList = new ArrayList<>();
                
                // 遍历区县
                for (SysDistrictEntity district : districts) {
                    RegionTreeVO.DistrictVO districtVO = new RegionTreeVO.DistrictVO();
                    districtVO.setDistrictCode(district.getDistrictCode());
                    districtVO.setDistrictName(district.getDistrictName());
                    districtVO.setFirstChar(district.getFirstChar());
                    
                    districtVOList.add(districtVO);
                }
                
                cityVO.setDistricts(districtVOList);
                cityVOList.add(cityVO);
            }
            
            provinceNode.setCities(cityVOList);
            result.add(provinceNode);
        }
        
        return result;
    }

    @Override
    public Map<String, List<SysCityVO>> firstCharCities() {
        LambdaQueryWrapper<SysCityEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.ne(SysCityEntity::getStatus, RegionStatusEnum.DELETED.getCode()) // 过滤已删除数据
                .orderByAsc(SysCityEntity::getFirstChar) // 先按首字母排序
                .orderByAsc(SysCityEntity::getCityCode); // 同首字母按城市编码排序

        List<SysCityEntity> entities = cityMapper.selectList(wrapper);
        List<SysCityVO> cityVOS = ConvertUtils.convertList(entities, SysCityVO.class);
        // 按首字母分组城市
        return cityVOS.stream().collect(Collectors.groupingBy(SysCityVO::getFirstChar,
                LinkedHashMap::new, Collectors.toList()));
    }

    @Override
    public List<SysCityVO> hotCities() {
        LambdaQueryWrapper<SysCityEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysCityEntity::isHotFlag, true) // 筛选热门城市
                .ne(SysCityEntity::getStatus, RegionStatusEnum.DELETED.getCode()) // 过滤已删除数据
                .orderByAsc(SysCityEntity::getFirstChar) // 先按首字母排序
                .orderByAsc(SysCityEntity::getCityCode); // 同首字母按城市编码排序

        List<SysCityEntity> entities = cityMapper.selectList(wrapper);
        return ConvertUtils.convertList(entities, SysCityVO.class);
    }
} 
