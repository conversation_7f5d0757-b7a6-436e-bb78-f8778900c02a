package com.droneclub.source.template.common.email;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import java.io.File;
import java.util.Properties;

@Slf4j
@Component
public class EmailSender {
    private final EmailConfig config;
    private final Properties props;
    private Message message;
    private MimeMultipart multipart;

    public EmailSender(EmailConfig config) {
        this.config = config;
        this.props = new Properties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.host", "smtp.163.com");
        props.put("mail.smtp.port", "465");
        props.put("mail.smtp.ssl.enable", true);
    }

    public EmailSender createMessage(String recipients) {
        try {
            Session session = Session.getInstance(props, new javax.mail.Authenticator() {
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(config.getUsername(), config.getPassword());
                }
            });

            message = new MimeMessage(session);
            message.setFrom(new InternetAddress(config.getUsername()));

            // 分割收件人地址
            String[] recipientList = recipients.split(",");
            InternetAddress[] recipientAddresses = new InternetAddress[recipientList.length];
            for (int i = 0; i < recipientList.length; i++) {
                recipientAddresses[i] = new InternetAddress(recipientList[i].trim());
            }

            message.setRecipients(Message.RecipientType.TO, recipientAddresses);
            multipart = new MimeMultipart();
        } catch (MessagingException e) {
            log.error("发送邮件异常, 接收人: {}", recipients, e);
        }

        return this;
    }

    public EmailSender ccRecipients(String... ccRecipients) {
        try {
            for (String ccRecipient : ccRecipients) {
                message.addRecipients(Message.RecipientType.CC, InternetAddress.parse(ccRecipient));
            }
        } catch (MessagingException e) {
            log.error("设置邮件抄送人异常, 抄送人: {}", ccRecipients, e);
        }
        return this;
    }

    public EmailSender subject(String subject) {
        try {
            message.setSubject(subject);
        } catch (MessagingException e) {
            e.printStackTrace();
        }
        return this;
    }

    public EmailSender textContent(String content) {
        try {
            MimeBodyPart textPart = new MimeBodyPart();
            textPart.setText(content);
            multipart.addBodyPart(textPart);
        } catch (MessagingException e) {
            log.error("设置邮件文本异常: {}", content, e);
        }
        return this;
    }

    public EmailSender htmlContent(String content) {
        try {
            MimeBodyPart htmlPart = new MimeBodyPart();
            htmlPart.setContent(content, "text/html; charset=utf-8");
            multipart.addBodyPart(htmlPart);
        } catch (MessagingException e) {
            log.error("设置邮件Html文本异常: {}", content, e);
        }
        return this;
    }

    public EmailSender attachment(File attachment) {
        try {
            MimeBodyPart attachmentPart = new MimeBodyPart();
            attachmentPart.attachFile(attachment);
            multipart.addBodyPart(attachmentPart);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("设置邮件附件异常.", e);
        }
        return this;
    }

    public void send() {
        try {
            message.setContent(multipart);
            Transport.send(message);
            log.info("邮件发送成功: {}", message.getRecipients(Message.RecipientType.TO)[0]);
        } catch (MessagingException e) {
            log.error("邮件发送失败.", e);
        }
    }
}