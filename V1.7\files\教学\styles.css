﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-119px;
  width:1053px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u0_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:417px;
  height:751px;
  background:inherit;
  background-color:rgba(245, 245, 245, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u0 {
  border-width:0px;
  position:absolute;
  left:119px;
  top:41px;
  width:417px;
  height:751px;
  display:flex;
}
#u0 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u0_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:417px;
  height:62px;
}
#u1 {
  border-width:0px;
  position:absolute;
  left:119px;
  top:41px;
  width:417px;
  height:62px;
  display:flex;
}
#u1 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:35px;
}
#u2 {
  border-width:0px;
  position:absolute;
  left:292px;
  top:68px;
  width:71px;
  height:35px;
  display:flex;
}
#u2 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:center;
}
#u3 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:72px;
  width:32px;
  height:18px;
  display:flex;
  font-size:16px;
  text-align:center;
}
#u3 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:417px;
  height:53px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4 {
  border-width:0px;
  position:absolute;
  left:119px;
  top:739px;
  width:417px;
  height:53px;
  display:flex;
}
#u4 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u6_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u6 {
  border-width:0px;
  position:absolute;
  left:149px;
  top:744px;
  width:25px;
  height:25px;
  display:flex;
}
#u6 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u7 {
  border-width:0px;
  position:absolute;
  left:150px;
  top:773px;
  width:24px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u7 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u8_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u8 {
  border-width:0px;
  position:absolute;
  left:230px;
  top:744px;
  width:25px;
  height:25px;
  display:flex;
}
#u8 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u9_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u9 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:773px;
  width:36px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u9 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u9_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u10_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u10 {
  border-width:0px;
  position:absolute;
  left:315px;
  top:744px;
  width:25px;
  height:25px;
  display:flex;
}
#u10 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u10_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u11_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u11 {
  border-width:0px;
  position:absolute;
  left:316px;
  top:773px;
  width:24px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u11 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u11_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u12 {
  border-width:0px;
  position:absolute;
  left:393px;
  top:744px;
  width:25px;
  height:25px;
  display:flex;
}
#u12 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u13_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u13 {
  border-width:0px;
  position:absolute;
  left:394px;
  top:773px;
  width:24px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u13 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u14_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u14 {
  border-width:0px;
  position:absolute;
  left:470px;
  top:744px;
  width:25px;
  height:25px;
  display:flex;
}
#u14 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u14_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u15_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u15 {
  border-width:0px;
  position:absolute;
  left:471px;
  top:773px;
  width:24px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u15 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u15_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u16_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:417px;
  height:140px;
}
#u16 {
  border-width:0px;
  position:absolute;
  left:119px;
  top:103px;
  width:417px;
  height:140px;
  display:flex;
}
#u16 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u16_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u17_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u17 {
  border-width:0px;
  position:absolute;
  left:279px;
  top:163px;
  width:97px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u17 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u17_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u18_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:395px;
  height:122px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u18 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:254px;
  width:395px;
  height:122px;
  display:flex;
}
#u18 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u18_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u19_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u19 {
  border-width:0px;
  position:absolute;
  left:162px;
  top:295px;
  width:40px;
  height:40px;
  display:flex;
}
#u19 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u19_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u20_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u20 {
  border-width:0px;
  position:absolute;
  left:158px;
  top:339px;
  width:48px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u20 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u20_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u21_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u21 {
  border-width:0px;
  position:absolute;
  left:146px;
  top:262px;
  width:56px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u21 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u21_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u22_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u22 {
  border-width:0px;
  position:absolute;
  left:255px;
  top:295px;
  width:40px;
  height:40px;
  display:flex;
}
#u22 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u22_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u23_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u23 {
  border-width:0px;
  position:absolute;
  left:251px;
  top:339px;
  width:48px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u23 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u23_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u24_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:395px;
  height:122px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u24 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:391px;
  width:395px;
  height:122px;
  display:flex;
}
#u24 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u24_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u25_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u25 {
  border-width:0px;
  position:absolute;
  left:162px;
  top:432px;
  width:40px;
  height:40px;
  display:flex;
}
#u25 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u25_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u26_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u26 {
  border-width:0px;
  position:absolute;
  left:158px;
  top:476px;
  width:48px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u26 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u26_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u27_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u27 {
  border-width:0px;
  position:absolute;
  left:146px;
  top:399px;
  width:56px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u27 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u27_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u28_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u28 {
  border-width:0px;
  position:absolute;
  left:349px;
  top:432px;
  width:40px;
  height:40px;
  display:flex;
}
#u28 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u28_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u29_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u29 {
  border-width:0px;
  position:absolute;
  left:333px;
  top:476px;
  width:72px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u29 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u29_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u30_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u30 {
  border-width:0px;
  position:absolute;
  left:349px;
  top:295px;
  width:40px;
  height:40px;
  display:flex;
}
#u30 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u30_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u31 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:339px;
  width:48px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u31 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u31_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u32_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:40px;
}
#u32 {
  border-width:0px;
  position:absolute;
  left:255px;
  top:432px;
  width:40px;
  height:40px;
  display:flex;
}
#u32 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u32_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u33_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u33 {
  border-width:0px;
  position:absolute;
  left:251px;
  top:476px;
  width:48px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u33 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u33_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u34_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:605px;
  height:221px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-size:16px;
}
#u34 {
  border-width:0px;
  position:absolute;
  left:567px;
  top:120px;
  width:605px;
  height:221px;
  display:flex;
  font-size:16px;
}
#u34 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u34_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
