package com.droneclub.source.template.module.service;

import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcExamTopicRecord;
import com.droneclub.source.template.entity.DcTopic;
import com.droneclub.source.template.module.pojo.DcExamTopicRecordVO;
import com.droneclub.source.template.module.pojo.DcExamTopicRecordListSearch;

import java.util.List;

public interface IDcExamTopicRecordService {
    
    ListData<DcExamTopicRecordVO> getDcExamTopicRecordList(DcExamTopicRecordListSearch params);

    DcExamTopicRecordVO getDcExamTopicRecordById(Integer id);

    DcExamTopicRecord createDcExamTopicRecord(DcExamTopicRecord data);

    boolean updateDcExamTopicRecord(DcExamTopicRecord data);

    boolean deleteDcExamTopicRecordById(Integer id);

    void batchInsert(Integer examId, List<DcTopic> examTopic);

    List<DcExamTopicRecord> getExamTopicRecord(Integer examId);

    List<DcExamTopicRecord> getExamErrorTopicRecord();

    List<DcExamTopicRecord> getExamErrorTopicRecord(Integer examIdForErrorTopic);
}
