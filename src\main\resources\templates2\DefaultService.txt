package com.droneclub.source.template.module.service;

import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.${Module};
import com.droneclub.source.template.module.pojo.${Module}VO;
import com.droneclub.source.template.module.pojo.${Module}ListSearch;

public interface I${Module}Service {
    
    ListData<${Module}VO> get${Module}List(${Module}ListSearch params);

    ${Module}VO get${Module}ById(Integer id);

    ${Module} create${Module}(${Module} data);

    boolean update${Module}(${Module} data);

    boolean delete${Module}ById(Integer id);
}
