package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import cn.soulspark.source.common.utils.StringUtils;
import cn.soulspark.source.common.utils.UUIDUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.common.model.TemplateCurrentUser;
import com.droneclub.source.template.common.utils.TemplateSessionUtils;
import com.droneclub.source.template.entity.DcCompany;
import com.droneclub.source.template.entity.DcCompanyDiscounts;
import com.droneclub.source.template.entity.DcUserDiscountCoupon;
import com.droneclub.source.template.entity.User;
import com.droneclub.source.template.mapper.DcUserDiscountCouponMapper;
import com.droneclub.source.template.mapper.TmUserMapper;
import com.droneclub.source.template.module.pojo.DcUserDiscountCouponListSearch;
import com.droneclub.source.template.module.pojo.DcUserDiscountCouponVO;
import com.droneclub.source.template.module.service.IDcCompanyService;
import com.droneclub.source.template.module.service.IDcUserDiscountCouponService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcUserDiscountCouponServiceImpl implements IDcUserDiscountCouponService {

    private final DcUserDiscountCouponMapper dcUserDiscountCouponMapper;
    private final IDcCompanyService dcCompanyService;
    private final TmUserMapper userMapper;

    @Override
    public ListData<DcUserDiscountCouponVO> getDcUserDiscountCouponList(DcUserDiscountCouponListSearch params) {
        LambdaQueryWrapper<DcUserDiscountCoupon> queryWrapper = new LambdaQueryWrapper<>();
        if (params.getUserId() != null) {
            queryWrapper.eq(DcUserDiscountCoupon::getUserId, params.getUserId());
        }
        if (params.getDiscountCouponStatus() != null) {
            queryWrapper.eq(DcUserDiscountCoupon::getDiscountCouponStatus, params.getDiscountCouponStatus());
        }
        if (params.getCompanyId() != null) {
            queryWrapper.eq(DcUserDiscountCoupon::getCompanyId, params.getCompanyId());
        }
        if (StringUtils.isValid(params.getDiscountCouponCode())) {
            queryWrapper.eq(DcUserDiscountCoupon::getDiscountCouponCode, params.getDiscountCouponCode());
        }
        // 查询总数
        Long total = dcUserDiscountCouponMapper.selectCount(queryWrapper);

        // 分页查询
        Page<DcUserDiscountCoupon> page = new Page<>(params.getPageNo(), params.getPageSize());
        IPage<DcUserDiscountCoupon> dcUserDiscountCouponPage = dcUserDiscountCouponMapper.selectPage(page, queryWrapper);
        List<DcUserDiscountCoupon> list = dcUserDiscountCouponPage.getRecords();
        List<DcUserDiscountCouponVO> listVO = list.stream()
                .map(dcUserDiscountCoupon -> JSONObject.parseObject(JSONObject.toJSONString(dcUserDiscountCoupon), DcUserDiscountCouponVO.class))
                .collect(Collectors.toList());
        // 添加优惠信息及公司信息
        Set<Integer> companyIds = listVO.stream().map(DcUserDiscountCouponVO::getCompanyId).collect(Collectors.toSet());
        List<DcCompany> companies = dcCompanyService.getCompanyList(companyIds);
        Map<Integer, DcCompany> companyMap = new HashMap<>();
        if (companies.size() > 0) {
            companyMap = companies.stream().collect(Collectors.toMap(DcCompany::getId, v -> v));
        }

        Set<Integer> discountsIds = listVO.stream().map(DcUserDiscountCouponVO::getDiscountsId).collect(Collectors.toSet());
        List<DcCompanyDiscounts> discounts = dcCompanyService.getDcCompanyDiscounts(null, discountsIds);
        Map<Integer, DcCompanyDiscounts> dcCompanyDiscountsMap = new HashMap<>();
        if (discounts.size() > 0) {
            dcCompanyDiscountsMap = discounts.stream().collect(Collectors.toMap(DcCompanyDiscounts::getId, v -> v));
        }
        Set<Integer> userIds = list.stream().map(DcUserDiscountCoupon::getCreateUser).collect(Collectors.toSet());
        Map<Integer, User> idUserNameMap = new HashMap<>();
        if (userIds.size() > 0) {
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            wrapper.in("id", userIds);
            List<User> existUserList = userMapper.selectList(wrapper);
            idUserNameMap = existUserList.stream()
                    .collect(Collectors.toMap(User::getId, v -> v));
        }

        Iterator<DcUserDiscountCouponVO> iterator = listVO.iterator();
        while (iterator.hasNext()) {
            DcUserDiscountCouponVO couponVO = iterator.next();
            DcCompany dcCompany = companyMap.get(couponVO.getCompanyId());
            // 公司信息不存在, 移除优惠券信息
            if (dcCompany == null) {
                iterator.remove();
                continue;
            }
            couponVO.setCompanyName(dcCompany.getCompanyName());
            DcCompanyDiscounts dcCompanyDiscounts = dcCompanyDiscountsMap.get(couponVO.getDiscountsId());
            // 优惠券信息不存在移除优惠券
            if (dcCompanyDiscounts == null) {
                iterator.remove();
                continue;
            }
            // 优惠政策为下架时优惠券过期
            if (dcCompanyDiscounts.getDiscountsStatus() == 2) {
                couponVO.setDiscountCouponStatus(3);
            }
            couponVO.setDiscountsName(dcCompanyDiscounts.getDiscountsName());
            couponVO.setDiscountsProfiles(dcCompanyDiscounts.getDiscountsProfiles());
            couponVO.setBuyingPrice(dcCompanyDiscounts.getBuyingPrice());
            couponVO.setDeductiblePrice(dcCompanyDiscounts.getDeductiblePrice());
            couponVO.setCreateUserName(idUserNameMap.get(couponVO.getCreateUser()).getUserName());
            couponVO.setCreateUserAccount(idUserNameMap.get(couponVO.getCreateUser()).getAccount());
        }

        return new ListData<>(listVO, total, params.getPageNo(), params.getPageSize());
    }

    @Override
    public DcUserDiscountCouponVO getDcUserDiscountCouponById(Integer id) {
        DcUserDiscountCoupon dcUserDiscountCoupon = dcUserDiscountCouponMapper.selectById(id);
        if (dcUserDiscountCoupon == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        return JSONObject.parseObject(JSONObject.toJSONString(dcUserDiscountCoupon), DcUserDiscountCouponVO.class);
    }


    @Override
    public DcUserDiscountCoupon createDcUserDiscountCoupon(DcUserDiscountCoupon data) {
        TemplateCurrentUser currentUser = TemplateSessionUtils.getCurrentUser();
        if (currentUser == null) {
            throw new ZkException("用户未登录, 请登录后领取.");
        }
        data.setUserId(currentUser.getId());
        data.setDiscountCouponCode(UUIDUtils.getUUID());
        data.setDiscountCouponStatus(1);
        // 检查当前用户是否存在未使用的优惠券
        LambdaQueryWrapper<DcUserDiscountCoupon> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcUserDiscountCoupon::getUserId, currentUser.getId());
        queryWrapper.eq(DcUserDiscountCoupon::getDiscountsId, data.getDiscountsId());
        queryWrapper.eq(DcUserDiscountCoupon::getDiscountCouponStatus, 1);
        if (dcUserDiscountCouponMapper.selectList(queryWrapper).size() > 0) {
            throw new ZkException("您已领取过此优惠券, 核销后可再次领取.");
        }
        // 保存优惠券
        boolean rs = dcUserDiscountCouponMapper.insert(data) > 0;
        log.info("创建 DcUserDiscountCoupon: {}", rs ? "成功" : "失败");
        return data;
    }

    @Override
    public boolean updateDcUserDiscountCoupon(DcUserDiscountCoupon data) {
        boolean rs = dcUserDiscountCouponMapper.updateById(data) > 0;
        log.info("更新 DcUserDiscountCoupon: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public boolean deleteDcUserDiscountCouponById(Integer id) {
        boolean rs = dcUserDiscountCouponMapper.deleteById(id) > 0;
        log.info("删除 DcUserDiscountCoupon: {}", rs ? "成功" : "失败");
        return rs;
    }
}
