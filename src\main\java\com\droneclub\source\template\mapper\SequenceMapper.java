package com.droneclub.source.template.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SequenceMapper {

    void init(@Param("moduleCode") String moduleCode,
              @Param("date") String date, @Param("currentValue") int currentValue);

    Integer getCurrentValue(@Param("moduleCode") String moduleCode,
                            @Param("date") String date, @Param("lock") boolean lock);

    int progress(@Param("moduleCode") String moduleCode,
                 @Param("date") String date, @Param("oldValue") int oldValue, @Param("newValue") int newValue);

}