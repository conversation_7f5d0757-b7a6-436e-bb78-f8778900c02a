package com.droneclub.source.template.module.service;

import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcAllTopicRecord;
import com.droneclub.source.template.module.pojo.DcAllTopicRecordVO;
import com.droneclub.source.template.module.pojo.DcAllTopicRecordListSearch;

public interface IDcAllTopicRecordService {
    
    ListData<DcAllTopicRecordVO> getDcAllTopicRecordList(DcAllTopicRecordListSearch params);

    DcAllTopicRecordVO getDcAllTopicRecordById(Integer id);

    DcAllTopicRecord createDcAllTopicRecord(DcAllTopicRecord data);

    boolean updateDcAllTopicRecord(DcAllTopicRecord data);

    Boolean deleteErrorAnswerRecord(Integer topicId);
}
