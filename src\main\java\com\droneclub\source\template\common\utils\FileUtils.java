package com.droneclub.source.template.common.utils;

import cn.soulspark.source.common.utils.StringUtils;
import com.droneclub.source.template.constants.FileConstant;
import com.droneclub.source.template.system.pojo.FileInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Date;

@Slf4j
public class FileUtils {

    /**
     * 初始化 fileInfo
     *
     * @param moduleType 模块
     * @param fileType   文件类型
     * @param businessId 关联业务id
     * @param file       文件
     * @return fileInfo对象
     */
    public static FileInfo initFileInfo(String moduleType, String fileType, String businessId, MultipartFile file) {
        return initFileInfo(moduleType, fileType, businessId, file.getOriginalFilename(), file.getContentType());
    }

    public static FileInfo initFileInfo(String moduleType, String fileType, String businessId, String originalFilename, String contentType) {
        String fileName = new Date().getTime() / 1000 + "_" + originalFilename;
        StringBuilder filePath = new StringBuilder();
        if (StringUtils.isValid(businessId)) {
            filePath.append(businessId).append("/");
        } else {
            businessId = null;
        }
        filePath.append(moduleType).append("/").append(fileType).append("/").append(fileName);
        return FileInfo.builder()
                .moduleType(moduleType)
                .fileType(fileType)
                .businessId(businessId)
                .fileName(fileName)
                .filePath(filePath.toString())
                .contentType(contentType)
                .fileFormat(getFileFormat(fileName))
                .fileStatus(FileConstant.FileStatus.VALID)
                .build();
    }

    /**
     * 获取文件预览路径
     *
     * @param bucket   bucket
     * @param filePath 文件路径
     * @return 预览路径
     */
    public static String getFilePreviewUrl(String bucket, String filePath) {
        return MinioUtils.PREVIEW_URL + bucket + "/" + filePath;
    }

    /**
     * 获取文件预览路径
     *
     * @param fileInfo 文件信息
     * @return 预览路径
     */
    public static String getFilePreviewUrl(FileInfo fileInfo) {
        return MinioUtils.PREVIEW_URL + MinioUtils.BUCKET + "/" + fileInfo.getFilePath();
    }

    /**
     * 根据文件名称获取文件类型
     *
     * @param fileName 文件名称
     * @return 文件类型
     */
    public static String getFileFormat(String fileName) {
        if (StringUtils.isInvalid(fileName)) {
            return "";
        }
        String[] strArray = fileName.split("\\.");
        int suffixIndex = strArray.length - 1;
        return strArray[suffixIndex];
    }

    /**
     * 根据地址获得数据的输入流
     *
     * @param fileUrl 文件地址
     * @return url的输入流
     */
    public static InputStream getInputStreamByUrl(String fileUrl) {
        HttpURLConnection conn = null;
        try (final ByteArrayOutputStream output = new ByteArrayOutputStream();) {
            URL url = new URL(fileUrl);
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(20 * 1000);
            IOUtils.copy(conn.getInputStream(), output);
            return new ByteArrayInputStream(output.toByteArray());
        } catch (Exception e) {
            log.error("根据地址获得数据的输入流异常 Exception，", e);
        } finally {
            try {
                if (conn != null) {
                    conn.disconnect();
                }
            } catch (Exception e) {
                log.error("断开输入流异常 Exception，", e);
            }
        }
        return null;
    }
}
