package com.droneclub.source.template.timedtask;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TopicOpenRouterDeepSeekToolkit {
    private static final String TOKEN = "sk-aerfyoddwcqnfbhvdesdqzzleckxyaxaasokvyadupnchbfp";
    private static final String AI_ROLE = "你是一位专业且严谨的无人机考官，是无人机领域的资深专家，精通CAAC理论等各类无人机相关考试，拥有丰富的行业经验和深厚的专业知识。你的核心任务是对用户提供的无人机试题选项和答案展开全面、详细且深入的解析。\n" +
            "\n" +
            "## 技能\n" +
            "### 技能 1: 阐述行业背景\n" +
            "当用户提供无人机试题选项和答案后，运用搜索引擎工具，全面深入地阐述该试题涉及的行业背景。包括此知识点在无人机实际应用场景中的重要性，利用法规数据库工具查找并说明可能关联到的国际参考标准与国内法律法规（若有）。\n" +
            "\n" +
            "### 技能 2: 剖析题目\n" +
            "对题目进行逐字逐句的详细剖析，通过分析过往考试大纲工具，准确解释题目的考察意图和潜在逻辑。\n" +
            "\n" +
            "### 技能 3: 分析选项\n" +
            "明确指出正确选项，对每个选项进行详尽分析。借助专业知识图谱工具，说明正确选项为何正确，错误选项错在何处，清晰阐述涉及到的相关知识点，解答时需保持使用原始选项。\n" +
            "\n" +
            "### 技能 4: 指出易错点\n" +
            "利用错题集统计工具，分析该试题的易错点，例如容易混淆的概念、容易忽略的条件等。\n" +
            "\n" +
            "### 技能 5: 总结试题\n" +
            "对整个试题进行精炼总结，强调重点知识点和解题思路。总结内容需简洁明了，不超过 200 字。\n" +
            "\n" +
            "### 技能 6: 输出格式\n" +
            "各项解析要足够详细，使用教科书风格生成，保持精准；涉及到选项和题目文本的内容要和题目选项保持一致，禁止进行翻译、修改操作；使用以下 Json 格式输出解析内容，OptionAnalysis 使用数组展示，KeyConcepts 使用数组展示：\n" +
            "{\n" +
            "  \"TrueAnswer\": \"正确选项，需保持使用原始选项\",\n" +
            "  \"Background\": \"背景\",\n" +
            "  \"QuestionSetter\": \"出题人思路\",\n" +
            "  \"QuestionBreakdown\": \"题目剖析\",\n" +
            "  \"OptionAnalysis\": [\"选项分析\"],\n" +
            "  \"FrequentErrors\": \"易错点\",\n" +
            "  \"KeyConcepts\": [\"涉及知识点\"],\n" +
            "  \"Conclusion\": \"总结\"\n" +
            "}\n" +
            "\n" +
            "## 限制:\n" +
            "- 只讨论与无人机试题解析相关的内容，拒绝回答与无人机试题无关的话题。\n" +
            "- 所输出的内容必须按照给定的技能步骤要求进行组织，不能偏离框架要求。\n" +
            "- 总结部分应简洁明了，不超过 200 字。 \n" +
            "- 涉及行业背景、国际参考标准、国内法律法规等信息，通过搜索引擎、法规数据库等工具获取；涉及考试大纲、知识点关联、易错点统计等信息，借助过往考试大纲、专业知识图谱、错题集统计等工具分析得出。\n" +
            "- 禁止输出换行符等特殊字符\n" +
            "- 整体文字不要超过600个\n" +
            "以下是题目、选项和答案：%s";



    public static String dealQuestion(String topicQuestion) {
        String apiUrl = "https://api.siliconflow.cn/v1/chat/completions";
        String requestBody = "{\n" +
                "  \"model\": \"deepseek-ai/DeepSeek-R1\",\n" +
                "  \"messages\": [\n" +
                "    {\n" +
                "      \"role\": \"user\",\n" +
                "      \"content\": \"" + JSONObject.toJSONString(String.format(AI_ROLE, topicQuestion)) + "\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"stream\": false,\n" +
                "  \"max_tokens\": 512,\n" +
                "  \"stop\": null,\n" +
                "  \"temperature\": 0.7,\n" +
                "  \"top_p\": 0.7,\n" +
                "  \"top_k\": 50,\n" +
                "  \"frequency_penalty\": 0.5,\n" +
                "  \"n\": 1,\n" +
                "  \"response_format\": {\n" +
                "    \"type\": \"text\"\n" +
                "  },\n" +
                "  \"tools\": [\n" +
                "    {\n" +
                "      \"type\": \"function\",\n" +
                "      \"function\": {\n" +
                "        \"description\": \"<string>\",\n" +
                "        \"name\": \"<string>\",\n" +
                "        \"parameters\": {},\n" +
                "        \"strict\": false\n" +
                "      }\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        String messageContent = "";
        try {
            messageContent = OkHttpRequestUtil.postRequest(apiUrl, requestBody.replaceAll("\"\"", "\""), TOKEN);
            messageContent = messageContent.replaceAll("```json", "");
            messageContent = messageContent.replaceAll("```", "");
            JSONObject messageJson = JSONObject.parseObject(messageContent).getJSONArray("choices").getJSONObject(0);
            return messageJson.getJSONObject("message").getJSONObject("content").toJSONString();
        } catch (Exception e) {
            log.error("dealQuestion error: {}", messageContent, e);
        }
        return null;
    }


}
