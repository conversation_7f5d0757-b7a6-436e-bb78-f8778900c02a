package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("dc_file")
public class DcFile {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String moduleType;
    private String fileType;
    private String businessId;
    private String filePath;
    private Integer createUser;
    private String createTime;
    private Integer updateUser;
    private String updateTime;
    private String isDelete;

}
