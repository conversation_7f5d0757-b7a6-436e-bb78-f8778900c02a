package com.droneclub.source.template.decrypt;

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.BCUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPublicKey;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SM2Utils {

    private static SM2 REQUEST_SM2;
    private static SM2 RESPONSE_SM2;

    public SM2Utils(Sm2Config sm2Config) {
        // 请求解密
        REQUEST_SM2 = SmUtil.sm2(sm2Config.getRequestPrivateKeyHax(), sm2Config.getRequestPublicKeyHax());
        REQUEST_SM2.usePlainEncoding();
        REQUEST_SM2.setMode(SM2Engine.Mode.C1C3C2);
        // 响应加密
        RESPONSE_SM2 = SmUtil.sm2(sm2Config.getResponsePrivateKeyHax(), sm2Config.getResponsePublicKeyHax());
        RESPONSE_SM2.usePlainEncoding();
        RESPONSE_SM2.setMode(SM2Engine.Mode.C1C3C2);
    }

    public static void generatorSM2Key() {
        SM2 sm2 = SmUtil.sm2();
        // 生成私钥
        String privateKeyD = HexUtil.encodeHexStr(BCUtil.encodeECPrivateKey(sm2.getPrivateKey()));
        // 生成公钥
        String publicKeyQ = HexUtil.encodeHexStr(((BCECPublicKey) sm2.getPublicKey()).getQ().getEncoded(false));
        System.out.println("publicKeyHax: " + publicKeyQ);
        System.out.println("privateKeyHax: " + privateKeyD);
    }

    public static String encrypt(String plainText) {
        try {
            String cipherText = RESPONSE_SM2.encryptHex(plainText, KeyType.PublicKey);
            // showDetailLog("[加密操作]\n明文:{}\n密文: {}", plainText, cipherText);
            return cipherText;
        } catch (Exception e) {
            log.error("[加密操作] 加密失败", e);
            return null;
        }
    }

    public static String decrypt(String cipherText) {
        try {
            String plainText = REQUEST_SM2.decryptStr(cipherText, KeyType.PrivateKey);
            // showDetailLog("[解密操作]\n密文: {}\n明文: {}", cipherText, plainText);
            return plainText;
        } catch (Exception e) {
            log.error("[解密操作] 解密失败", e);
            return null;
        }
    }

    private static void showDetailLog(String message, Object... params) {
        log.info(message, params);
    }

    public static void main(String[] args) {
        // generatorSM2Key();
        String requestPrivateKeyStr = "009acba22f3eaca6028bb104f77a628c8902e9206808ab25cacfbeafac71060c22";
        String requestPublicKeyStr = "04d4c47a78d0b43f8397cb2034c56b848e2b64163100c754ba28c0206418d5a3b6b7e6517f810a20fa2b21da0746a02446b8d70f8e460fa87ff497b75c30b0dca5";
        REQUEST_SM2 = SmUtil.sm2(requestPrivateKeyStr, requestPublicKeyStr);
        REQUEST_SM2.usePlainEncoding();
        REQUEST_SM2.setMode(SM2Engine.Mode.C1C3C2);

        String responsePrivateKeyStr = "5c2444d94f146ba33cf5e1bd5a0e02d4f8bdbdec9ec96f7b1d19c26e765df40c";
        String responsePublicKeyStr = "045bb061ebcc3ccaebd78785e14d6d8565b31f3ac9384bd75e71395631114c63aa9fe1d47361e74e24587df2dafb305ef1e97ebffe1debc40703d483e2f92727f2";
        RESPONSE_SM2 = SmUtil.sm2(responsePrivateKeyStr, responsePublicKeyStr);
        RESPONSE_SM2.usePlainEncoding();
        RESPONSE_SM2.setMode(SM2Engine.Mode.C1C3C2);

        // 使用request加解密
        String plainText = "{\n" +
                "    \"infoType\":1,\n" +
                "    \"pageNo\":1,\n" +
                "    \"pageSize\":10\n" +
                "}";
        // String cipherText = REQUEST_SM2.encryptHex(plainText, KeyType.PublicKey);
        String cipherText = "4d6bb51c8fc984132e831be6c26fa413cb5dd83c68b682c4a08eb732284bddf02f9ea7054f98503d53df2743a648944e399865482e6dec3fce45107ab66f2a5fe43822bd785f968ce104e0de08d052bf17f5c4c32089e3016856d869e321edd5a5b0e86ee94312f28c5f1b677c88bf6f37ab13a6be115484525ef3b58918b75f4d3ede368c68fd";

        System.out.println("请求-加密密文: " + cipherText);
        plainText = REQUEST_SM2.decryptStr(cipherText, KeyType.PrivateKey);
        System.out.println("请求-解密明文: " + plainText);


        // 使用response加解密
        cipherText = RESPONSE_SM2.encryptHex(plainText, KeyType.PublicKey);
        System.out.println("响应-加密密文: " + cipherText);

        // cipherText = "041b5bdd846ac9ef86c1016a7d386c4380bb1d9a49b7a7733c6ef5599aae8fb4153179bd7b6e65c357df438af39889ccc2041729ca63adec71aa765e9642f18a85cbe6843b097690c40277b491630775a83e1f78f16811024fb5cf0470766e46857bc6";
        cipherText = "4d6bb51c8fc984132e831be6c26fa413cb5dd83c68b682c4a08eb732284bddf02f9ea7054f98503d53df2743a648944e399865482e6dec3fce45107ab66f2a5fe43822bd785f968ce104e0de08d052bf17f5c4c32089e3016856d869e321edd5a5b0e86ee94312f28c5f1b677c88bf6f37ab13a6be115484525ef3b58918b75f4d3ede368c68fd";

        plainText = RESPONSE_SM2.decryptStr(cipherText, KeyType.PrivateKey);
        System.out.println("响应-解密明文: " + plainText);
    }

}
