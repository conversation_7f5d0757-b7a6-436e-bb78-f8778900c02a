package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("dc_operation_record")
public class DcOperationRecord {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String operationType;
    private Integer operationUser;
    private Integer businessId;
    private Integer businessCreateBy;
    private String createTime;
    private String isDelete;

}
