package com.droneclub.source.template.module.service;

import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcPublishIllegalInfoReport;
import com.droneclub.source.template.module.pojo.DcPublishIllegalInfoReportListSearch;
import com.droneclub.source.template.module.pojo.DcPublishIllegalInfoReportVO;

public interface IDcPublishIllegalInfoReportService {

    ListData<DcPublishIllegalInfoReportVO> getDcPublishIllegalInfoReportList(DcPublishIllegalInfoReportListSearch params);

    DcPublishIllegalInfoReportVO getDcPublishIllegalInfoReportById(Integer id);

    DcPublishIllegalInfoReport createDcPublishIllegalInfoReport(DcPublishIllegalInfoReport data);

    boolean updateDcPublishIllegalInfoReport(DcPublishIllegalInfoReport data);

    boolean deleteDcPublishIllegalInfoReportById(Integer id);
}
