package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.alibaba.fastjson.JSONObject;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.decrypt.ResponseEncryption;
import com.droneclub.source.template.entity.DcTopic;
import com.droneclub.source.template.module.pojo.*;
import com.droneclub.source.template.module.service.IDcTopicService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dcTopic")
public class DcTopicController {

    private final IDcTopicService dcTopicService;

    @ResponseEncryption
    @PostMapping("/getDcTopicList")
    public RestResult<ListData<DcTopicVO>> getDcTopicList(@RequestBody DcTopicListSearch params) {
        return RestResult.success(dcTopicService.getDcTopicList(params));
    }

    @ResponseEncryption
    @PostMapping("/getKsDcTopicList")
    public RestResult<ListData<DcTopicVO>> getKsDcTopicList(@RequestBody DcTopicListSearch params) {
        return RestResult.success(dcTopicService.getKsDcTopicList(params));
    }

    /**
     * 获取易错题
     * @return 易错题列表
     */
    @GetMapping("/getDcYcTopicList")
    public RestResult<YCTopic> getDcYcTopicList() {
        return RestResult.success(dcTopicService.getDcYcTopicList());
    }

    /**
     * 获取题目章节列表
     *
     * @return 题目章节列表
     */
    @ResponseEncryption
    @GetMapping("/getTopicChapterList")
    public RestResult<List<TopicChapterItem>> getTopicChapterList(String chapterType) {
        return RestResult.success(dcTopicService.getTopicChapterList(chapterType));
    }
    /**
     * 获取题目章节列表
     *
     * @return 题目章节列表
     */
    @ResponseEncryption
    @GetMapping("/getKsTopicChapterList")
    public RestResult<List<TopicChapterItem>> getKsTopicChapterList(String chapterType) {
        return RestResult.success(dcTopicService.getKsTopicChapterList("多旋翼"));
    }


    /**
     * 题目首页数据
     *
     * @return 首页数据
     */
    @GetMapping("/getTopicIndex")
    public RestResult<TopicIndexVO> getTopicIndex(String chapterType) {
        return RestResult.success(dcTopicService.getTopicIndex(chapterType));
    }


    @ResponseEncryption
    @GetMapping("/getDcTopicById")
    public RestResult<DcTopicVO> getDcTopicById(Integer id) {
        return RestResult.success(dcTopicService.getDcTopicById(id));
    }

    @ResponseEncryption
    @PostMapping("/createDcTopic")
    public RestResult<DcTopic> createDcTopic(@RequestBody DcTopic data) {
        return RestResult.success(dcTopicService.createDcTopic(data));
    }

    @PutMapping("/updateDcTopic")
    public RestResult<Boolean> updateDcTopic(@RequestBody DcTopic data) {
        return RestResult.success(dcTopicService.updateDcTopic(data));
    }

    @ResponseEncryption
    @PostMapping("/updateDcStatus")
    public RestResult<Boolean> updateDcStatus(@RequestBody JSONObject data) {
        return RestResult.success(dcTopicService.updateDcStatus(data));
    }

    @DeleteMapping("/deleteDcTopicById")
    public RestResult<Boolean> deleteDcTopicById(Integer id) {
        return RestResult.success(dcTopicService.deleteDcTopicById(id));
    }

    @GetMapping("/dealAiTopicAnalysis")
    public RestResult<Boolean> dealAiTopicAnalysis() {
        return RestResult.success(dcTopicService.dealAiTopicAnalysis());
    }

    @GetMapping("/dealAiTopicDetailAnalysis")
    public RestResult<Boolean> dealAiTopicDetailAnalysis() {
        return RestResult.success(dcTopicService.dealAiTopicDetailAnalysis());
    }

    @ResponseEncryption
    @GetMapping("/getTopicAiAnalysis")
    public RestResult<DcTopic> getTopicAiAnalysis(int topicId) {
        return RestResult.success(dcTopicService.getTopicAiAnalysis(topicId));
    }

    @GetMapping("/getQuestionBreakdown")
    public RestResult<List<QuestionBreakdown>> getQuestionBreakdown(String chapterName) {
        return RestResult.success(dcTopicService.getQuestionBreakdown(chapterName));
    }

    @GetMapping("/checkAIAnswer")
    public RestResult<Boolean> checkAIAnswer() {
        return RestResult.success(dcTopicService.checkAIAnswer());
    }
}
