package com.droneclub.source.template.common.model;

import lombok.Data;

import java.util.List;

/**
 * 列表数据
 */
@Data
public class ListData<T> {
    private List<T> list;
    private Long total;
    private int pageNo;
    private int pageSize;

    /**
     * 起始下标
     */
    private int offset = 0;


    /**
     * 总页码
     */
    private long totalPage;

    public ListData(Long total, int pageNo, int pageSize) {
        this.total = total;
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        setOffset();
    }

    public ListData(List<T> list, Long total, int pageNo, int pageSize) {
        this.list = list;
        this.total = total;
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        setOffset();
    }

    public void setOffset() {
        this.offset = pageNo <= 0 ? 0 : (pageNo - 1) * pageSize;
    }

    public void setPageNo(int pageNo) {
        if (pageNo < 1) {
            pageNo = 1;
        }
        this.pageNo = pageNo;
    }

    public void setTotal(long total) {
        if (total > 0) {
            setTotalPage((total - 1) / pageSize + 1);
        } else {
            setTotalPage(totalPage);
        }
        this.total = total;
    }
}
