package com.droneclub.source.template.single.region.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 城市信息实体类
 */
@Data
@TableName("sys_city")
public class SysCityEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 城市编码
     */
    private String cityCode;
    
    /**
     * 城市名称
     */
    private String cityName;
    
    /**
     * 等级
     */
    private String level;
    
    /**
     * 首字母
     */
    private String firstChar;
    /**
     * 拼音
     */
    private String pinyin;

    /**
     * 热门城市标记
     */
    private boolean hotFlag;

    /**
     * 区号
     */
    private String cityAreaCode;
    
    /**
     * 中心点坐标
     */
    private String center;
    
    /**
     * 所属省份编码
     */
    private String provinceCode;
    
    /**
     * 状态 0:删除 1:有效 2:更新
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 