package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.common.model.TemplateCurrentUser;
import com.droneclub.source.template.common.utils.TemplateSessionUtils;
import com.droneclub.source.template.entity.DcOperationRecord;
import com.droneclub.source.template.entity.User;
import com.droneclub.source.template.mapper.DcCompanyMapper;
import com.droneclub.source.template.mapper.DcOperationRecordMapper;
import com.droneclub.source.template.mapper.TmUserMapper;
import com.droneclub.source.template.module.pojo.DcOperationRecordListSearch;
import com.droneclub.source.template.module.pojo.DcOperationRecordVO;
import com.droneclub.source.template.module.pojo.PublishInfoVO;
import com.droneclub.source.template.module.service.IDcOperationRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcOperationRecordServiceImpl implements IDcOperationRecordService {

    private final DcOperationRecordMapper dcOperationRecordMapper;
    private final TmUserMapper userMapper;
    private final DcCompanyMapper companyMapper;

    @Override
    public Object getDcOperationRecordList(DcOperationRecordListSearch params) {
        TemplateCurrentUser currentUser = TemplateSessionUtils.getCurrentUser();
        if (currentUser == null) {
            throw new ZkException("当前用户未登录");
        }
        params.setOperationUser(currentUser.getId());
        long total = dcOperationRecordMapper.countShowListData(params);
        ListData<PublishInfoVO> listData = new ListData<>(total, params.getPageNo(), params.getPageSize());
        List<PublishInfoVO> jobResumeVOS = dcOperationRecordMapper.getPublishInfoVOList(params);

        Set<Integer> userIds = jobResumeVOS.stream().map(PublishInfoVO::getBusinessCreateBy).collect(Collectors.toSet());
        Map<Integer, User> idUserNameMap = new HashMap<>();
        if (userIds.size() > 0) {
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            wrapper.in("id", userIds);
            List<User> existUserList = userMapper.selectList(wrapper);
            idUserNameMap = existUserList.stream()
                    .collect(Collectors.toMap(User::getId, v -> v));
        }
        // 绑定业务数据创建信息
        for (PublishInfoVO vo : jobResumeVOS) {
            vo.setCreateUserName(idUserNameMap.get(vo.getBusinessCreateBy()).getUserName());
            vo.setActiveState(idUserNameMap.get(vo.getBusinessCreateBy()).getActiveState());
        }
        listData.setList(jobResumeVOS);
        return listData;
    }

    @Override
    public DcOperationRecordVO getDcOperationRecordById(Integer id) {
        DcOperationRecord dcOperationRecord = dcOperationRecordMapper.selectById(id);
        if (dcOperationRecord == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        return JSONObject.parseObject(JSONObject.toJSONString(dcOperationRecord), DcOperationRecordVO.class);
    }


    @Override
    public DcOperationRecord createDcOperationRecord(DcOperationRecord data) {
        if (TemplateSessionUtils.getCurrentUser() != null) {
            data.setOperationUser(TemplateSessionUtils.getCurrentUser().getId());
            data.setBusinessCreateBy(TemplateSessionUtils.getCurrentUser().getId());
            // 如果是操作是look操作, 相同的数据删除之前的数据重新添加
            if (data.getOperationType().startsWith("look_")) {
                LambdaQueryWrapper<DcOperationRecord> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(DcOperationRecord::getOperationType, data.getOperationType());
                queryWrapper.eq(DcOperationRecord::getOperationUser, data.getOperationUser());
                queryWrapper.eq(DcOperationRecord::getBusinessId, data.getBusinessId());
                boolean deleteOldRs = dcOperationRecordMapper.delete(queryWrapper) > 0;
                if (deleteOldRs) {
                    log.info("历史数据已删除: {}", data);
                }
            }
            boolean rs = dcOperationRecordMapper.insert(data) > 0;
            log.info("创建 DcOperationRecord: {}", rs ? "成功" : "失败");
            // 如果操作类型为浏览公司（look_company）则向公司表浏览数 + 1
            if ("look_company".equals(data.getOperationType())) {
                companyMapper.addLookNum(data.getBusinessId());
            }
            return data;
        } else {
            data.setOperationUser(-1);
            data.setBusinessCreateBy(-1);
            boolean rs = dcOperationRecordMapper.insert(data) > 0;
            log.info("创建匿名用户 DcOperationRecord: {}, {}", data.getOperationType(), rs ? "成功" : "失败");
        }
        return null;
    }

    @Override
    public boolean updateDcOperationRecord(DcOperationRecord data) {
        boolean rs = dcOperationRecordMapper.updateById(data) > 0;
        log.info("更新 DcOperationRecord: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public boolean deleteDcOperationRecordById(Integer id) {
        boolean rs = dcOperationRecordMapper.deleteById(id) > 0;
        log.info("删除 DcOperationRecord: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public boolean checkOperationRecordExist(DcOperationRecord conditions) {
        LambdaQueryWrapper<DcOperationRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcOperationRecord::getOperationType, conditions.getOperationType());
        queryWrapper.eq(DcOperationRecord::getOperationUser, conditions.getOperationUser());
        queryWrapper.eq(DcOperationRecord::getBusinessId, conditions.getBusinessId());
        return dcOperationRecordMapper.selectList(queryWrapper).size() > 0;
    }

    @Override
    public boolean deleteDcOperationRecord(DcOperationRecord data) {
        LambdaQueryWrapper<DcOperationRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcOperationRecord::getOperationType, data.getOperationType());
        queryWrapper.eq(DcOperationRecord::getOperationUser, TemplateSessionUtils.getCurrentUser().getId());
        queryWrapper.eq(DcOperationRecord::getBusinessId, data.getBusinessId());
        return dcOperationRecordMapper.delete(queryWrapper) > 0;
    }
}
