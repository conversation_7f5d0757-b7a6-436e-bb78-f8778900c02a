﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-63px;
  width:813px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u385_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:758px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u385 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:33px;
  width:375px;
  height:758px;
  display:flex;
}
#u385 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u385_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u386_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:45px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u386 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:33px;
  width:375px;
  height:45px;
  display:flex;
}
#u386 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u386_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u387_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u387 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:48px;
  width:56px;
  height:16px;
  display:flex;
}
#u387 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u387_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u388_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u388 {
  border-width:0px;
  position:absolute;
  left:75px;
  top:46px;
  width:20px;
  height:20px;
  display:flex;
}
#u388 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u388_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u389_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u389 {
  border-width:0px;
  position:absolute;
  left:135px;
  top:84px;
  width:42px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u389 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u389_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u390_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:376px;
  height:2px;
}
#u390 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:105px;
  width:375px;
  height:1px;
  display:flex;
}
#u390 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u390_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u391_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u391 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:83px;
  width:42px;
  height:16px;
  display:flex;
}
#u391 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u391_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u392_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:129px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u392 {
  border-width:0px;
  position:absolute;
  left:75px;
  top:119px;
  width:351px;
  height:129px;
  display:flex;
}
#u392 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u392_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u393_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u393 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:134px;
  width:56px;
  height:16px;
  display:flex;
}
#u393 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u393_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u394_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u394 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:134px;
  width:56px;
  height:16px;
  display:flex;
}
#u394 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u394_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u395_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u395 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:217px;
  width:47px;
  height:16px;
  display:flex;
  font-size:12px;
}
#u395 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u395_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u396_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u396 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:189px;
  width:98px;
  height:16px;
  display:flex;
}
#u396 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u396_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u397_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u397 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:189px;
  width:72px;
  height:16px;
  display:flex;
}
#u397 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u397_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u398_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u398 {
  border-width:0px;
  position:absolute;
  left:398px;
  top:177px;
  width:20px;
  height:20px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u398 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u398_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u399_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u399 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:162px;
  width:56px;
  height:16px;
  display:flex;
}
#u399 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u399_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u400_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u400 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:162px;
  width:128px;
  height:16px;
  display:flex;
}
#u400 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u400_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u401_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u401 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:217px;
  width:56px;
  height:16px;
  display:flex;
}
#u401 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u401_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u402_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:7px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:10px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u402 {
  border-width:0px;
  position:absolute;
  left:74px;
  top:119px;
  width:351px;
  height:7px;
  display:flex;
}
#u402 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u402_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u403_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:129px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u403 {
  border-width:0px;
  position:absolute;
  left:75px;
  top:258px;
  width:351px;
  height:129px;
  display:flex;
}
#u403 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u403_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u404_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u404 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:273px;
  width:56px;
  height:16px;
  display:flex;
}
#u404 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u404_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u405_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u405 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:273px;
  width:56px;
  height:16px;
  display:flex;
}
#u405 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u405_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u406_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u406 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:356px;
  width:47px;
  height:16px;
  display:flex;
  font-size:12px;
}
#u406 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u406_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u407_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u407 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:328px;
  width:98px;
  height:16px;
  display:flex;
}
#u407 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u407_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u408_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u408 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:328px;
  width:72px;
  height:16px;
  display:flex;
}
#u408 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u408_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u409_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u409 {
  border-width:0px;
  position:absolute;
  left:398px;
  top:316px;
  width:20px;
  height:20px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u409 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u409_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u410_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u410 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:301px;
  width:56px;
  height:16px;
  display:flex;
}
#u410 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u410_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u411_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u411 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:301px;
  width:128px;
  height:16px;
  display:flex;
}
#u411 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u411_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u412_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u412 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:356px;
  width:56px;
  height:16px;
  display:flex;
}
#u412 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u412_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u413_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:7px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:10px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u413 {
  border-width:0px;
  position:absolute;
  left:74px;
  top:258px;
  width:351px;
  height:7px;
  display:flex;
}
#u413 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u413_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u414_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:127px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u414 {
  border-width:0px;
  position:absolute;
  left:76px;
  top:397px;
  width:351px;
  height:127px;
  display:flex;
}
#u414 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u414_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u415_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u415 {
  border-width:0px;
  position:absolute;
  left:88px;
  top:412px;
  width:56px;
  height:16px;
  display:flex;
}
#u415 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u415_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u416_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u416 {
  border-width:0px;
  position:absolute;
  left:233px;
  top:412px;
  width:56px;
  height:16px;
  display:flex;
}
#u416 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u416_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u417_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u417 {
  border-width:0px;
  position:absolute;
  left:233px;
  top:489px;
  width:47px;
  height:16px;
  display:flex;
  font-size:12px;
}
#u417 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u417_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u418_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u418 {
  border-width:0px;
  position:absolute;
  left:88px;
  top:462px;
  width:98px;
  height:16px;
  display:flex;
}
#u418 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u418_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u419_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u419 {
  border-width:0px;
  position:absolute;
  left:233px;
  top:462px;
  width:72px;
  height:16px;
  display:flex;
}
#u419 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u419_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u420_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u420 {
  border-width:0px;
  position:absolute;
  left:398px;
  top:446px;
  width:20px;
  height:20px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u420 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u420_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u421_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u421 {
  border-width:0px;
  position:absolute;
  left:88px;
  top:489px;
  width:56px;
  height:16px;
  display:flex;
}
#u421 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u421_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u422_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:7px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:10px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u422 {
  border-width:0px;
  position:absolute;
  left:75px;
  top:397px;
  width:351px;
  height:7px;
  display:flex;
}
#u422 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u422_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u423_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:758px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u423 {
  border-width:0px;
  position:absolute;
  left:501px;
  top:33px;
  width:375px;
  height:758px;
  display:flex;
}
#u423 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u423_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u424_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:45px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u424 {
  border-width:0px;
  position:absolute;
  left:501px;
  top:33px;
  width:375px;
  height:45px;
  display:flex;
}
#u424 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u424_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u425_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u425 {
  border-width:0px;
  position:absolute;
  left:658px;
  top:48px;
  width:56px;
  height:16px;
  display:flex;
}
#u425 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u425_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u426_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u426 {
  border-width:0px;
  position:absolute;
  left:513px;
  top:46px;
  width:20px;
  height:20px;
  display:flex;
}
#u426 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u426_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u427_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u427 {
  border-width:0px;
  position:absolute;
  left:573px;
  top:84px;
  width:42px;
  height:16px;
  display:flex;
}
#u427 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u427_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u428_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:376px;
  height:2px;
}
#u428 {
  border-width:0px;
  position:absolute;
  left:501px;
  top:105px;
  width:375px;
  height:1px;
  display:flex;
}
#u428 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u428_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u429_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u429 {
  border-width:0px;
  position:absolute;
  left:770px;
  top:83px;
  width:42px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u429 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u429_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u430_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:117px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u430 {
  border-width:0px;
  position:absolute;
  left:513px;
  top:119px;
  width:351px;
  height:117px;
  display:flex;
}
#u430 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u430_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u431_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u431 {
  border-width:0px;
  position:absolute;
  left:525px;
  top:134px;
  width:56px;
  height:16px;
  display:flex;
}
#u431 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u431_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u432_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u432 {
  border-width:0px;
  position:absolute;
  left:670px;
  top:134px;
  width:56px;
  height:16px;
  display:flex;
}
#u432 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u432_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u433_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u433 {
  border-width:0px;
  position:absolute;
  left:525px;
  top:189px;
  width:98px;
  height:16px;
  display:flex;
}
#u433 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u433_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u434_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u434 {
  border-width:0px;
  position:absolute;
  left:670px;
  top:189px;
  width:72px;
  height:16px;
  display:flex;
}
#u434 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u434_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u435_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u435 {
  border-width:0px;
  position:absolute;
  left:836px;
  top:162px;
  width:20px;
  height:20px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u435 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u435_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u436_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u436 {
  border-width:0px;
  position:absolute;
  left:525px;
  top:162px;
  width:56px;
  height:16px;
  display:flex;
}
#u436 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u436_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u437_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u437 {
  border-width:0px;
  position:absolute;
  left:670px;
  top:162px;
  width:128px;
  height:16px;
  display:flex;
}
#u437 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u437_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u438_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:7px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:10px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u438 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:119px;
  width:351px;
  height:7px;
  display:flex;
}
#u438 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u438_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u439_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:119px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u439 {
  border-width:0px;
  position:absolute;
  left:511px;
  top:489px;
  width:351px;
  height:119px;
  display:flex;
}
#u439 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u439_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u440_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u440 {
  border-width:0px;
  position:absolute;
  left:523px;
  top:504px;
  width:56px;
  height:16px;
  display:flex;
}
#u440 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u440_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u441_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u441 {
  border-width:0px;
  position:absolute;
  left:668px;
  top:504px;
  width:56px;
  height:16px;
  display:flex;
}
#u441 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u441_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u442_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u442 {
  border-width:0px;
  position:absolute;
  left:523px;
  top:559px;
  width:98px;
  height:16px;
  display:flex;
}
#u442 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u442_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u443_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u443 {
  border-width:0px;
  position:absolute;
  left:668px;
  top:559px;
  width:72px;
  height:16px;
  display:flex;
}
#u443 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u443_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u444_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u444 {
  border-width:0px;
  position:absolute;
  left:834px;
  top:533px;
  width:20px;
  height:20px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u444 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u444_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u445_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u445 {
  border-width:0px;
  position:absolute;
  left:523px;
  top:532px;
  width:56px;
  height:16px;
  display:flex;
}
#u445 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u445_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u446_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u446 {
  border-width:0px;
  position:absolute;
  left:668px;
  top:532px;
  width:128px;
  height:16px;
  display:flex;
}
#u446 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u446_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u447_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:7px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:10px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u447 {
  border-width:0px;
  position:absolute;
  left:510px;
  top:489px;
  width:351px;
  height:7px;
  display:flex;
}
#u447 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u447_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u448_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:94px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u448 {
  border-width:0px;
  position:absolute;
  left:511px;
  top:384px;
  width:351px;
  height:94px;
  display:flex;
}
#u448 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u448_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u449_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u449 {
  border-width:0px;
  position:absolute;
  left:523px;
  top:399px;
  width:56px;
  height:16px;
  display:flex;
}
#u449 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u449_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u450_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u450 {
  border-width:0px;
  position:absolute;
  left:668px;
  top:399px;
  width:56px;
  height:16px;
  display:flex;
}
#u450 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u450_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u451_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u451 {
  border-width:0px;
  position:absolute;
  left:523px;
  top:429px;
  width:98px;
  height:16px;
  display:flex;
}
#u451 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u451_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u452_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u452 {
  border-width:0px;
  position:absolute;
  left:668px;
  top:429px;
  width:72px;
  height:16px;
  display:flex;
}
#u452 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u452_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u453_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u453 {
  border-width:0px;
  position:absolute;
  left:833px;
  top:414px;
  width:20px;
  height:20px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u453 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u453_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u454_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:7px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:10px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u454 {
  border-width:0px;
  position:absolute;
  left:510px;
  top:384px;
  width:351px;
  height:7px;
  display:flex;
}
#u454 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u454_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u455_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:771px;
  height:231px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-size:16px;
}
#u455 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:819px;
  width:771px;
  height:231px;
  display:flex;
  font-size:16px;
}
#u455 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u455_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u456_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u456 {
  border-width:0px;
  position:absolute;
  left:88px;
  top:440px;
  width:56px;
  height:16px;
  display:flex;
}
#u456 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u456_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u457_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u457 {
  border-width:0px;
  position:absolute;
  left:233px;
  top:440px;
  width:42px;
  height:16px;
  display:flex;
}
#u457 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u457_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u458_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:106px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u458 {
  border-width:0px;
  position:absolute;
  left:75px;
  top:534px;
  width:351px;
  height:106px;
  display:flex;
}
#u458 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u458_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u459_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u459 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:549px;
  width:56px;
  height:16px;
  display:flex;
}
#u459 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u459_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u460_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u460 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:549px;
  width:56px;
  height:16px;
  display:flex;
}
#u460 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u460_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u461_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u461 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:606px;
  width:47px;
  height:16px;
  display:flex;
  font-size:12px;
}
#u461 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u461_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u462_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u462 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:579px;
  width:98px;
  height:16px;
  display:flex;
}
#u462 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u462_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u463_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u463 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:579px;
  width:72px;
  height:16px;
  display:flex;
}
#u463 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u463_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u464_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u464 {
  border-width:0px;
  position:absolute;
  left:397px;
  top:583px;
  width:20px;
  height:20px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u464 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u464_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u465_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u465 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:606px;
  width:56px;
  height:16px;
  display:flex;
}
#u465 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u465_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u466_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:7px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:10px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u466 {
  border-width:0px;
  position:absolute;
  left:74px;
  top:534px;
  width:351px;
  height:7px;
  display:flex;
}
#u466 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u466_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u467_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:127px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u467 {
  border-width:0px;
  position:absolute;
  left:511px;
  top:246px;
  width:351px;
  height:127px;
  display:flex;
}
#u467 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u467_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u468_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u468 {
  border-width:0px;
  position:absolute;
  left:523px;
  top:261px;
  width:56px;
  height:16px;
  display:flex;
}
#u468 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u468_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u469_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u469 {
  border-width:0px;
  position:absolute;
  left:668px;
  top:261px;
  width:56px;
  height:16px;
  display:flex;
}
#u469 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u469_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u470_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u470 {
  border-width:0px;
  position:absolute;
  left:523px;
  top:316px;
  width:98px;
  height:16px;
  display:flex;
}
#u470 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u470_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u471_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u471 {
  border-width:0px;
  position:absolute;
  left:668px;
  top:316px;
  width:72px;
  height:16px;
  display:flex;
}
#u471 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u471_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u472_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u472 {
  border-width:0px;
  position:absolute;
  left:833px;
  top:295px;
  width:20px;
  height:20px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u472 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u472_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u473_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:7px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:10px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u473 {
  border-width:0px;
  position:absolute;
  left:510px;
  top:246px;
  width:351px;
  height:7px;
  display:flex;
}
#u473 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u473_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u474_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u474 {
  border-width:0px;
  position:absolute;
  left:523px;
  top:289px;
  width:56px;
  height:16px;
  display:flex;
}
#u474 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u474_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u475_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u475 {
  border-width:0px;
  position:absolute;
  left:668px;
  top:289px;
  width:42px;
  height:16px;
  display:flex;
}
#u475 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u475_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u476_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u476 {
  border-width:0px;
  position:absolute;
  left:525px;
  top:212px;
  width:84px;
  height:16px;
  display:flex;
}
#u476 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u476_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u477_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u477 {
  border-width:0px;
  position:absolute;
  left:670px;
  top:212px;
  width:72px;
  height:16px;
  display:flex;
}
#u477 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u477_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u478_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u478 {
  border-width:0px;
  position:absolute;
  left:523px;
  top:343px;
  width:84px;
  height:16px;
  display:flex;
}
#u478 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u478_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u479_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u479 {
  border-width:0px;
  position:absolute;
  left:668px;
  top:343px;
  width:72px;
  height:16px;
  display:flex;
}
#u479 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u479_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u480_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u480 {
  border-width:0px;
  position:absolute;
  left:523px;
  top:455px;
  width:84px;
  height:16px;
  display:flex;
}
#u480 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u480_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u481_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u481 {
  border-width:0px;
  position:absolute;
  left:668px;
  top:455px;
  width:72px;
  height:16px;
  display:flex;
}
#u481 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u481_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u482_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u482 {
  border-width:0px;
  position:absolute;
  left:523px;
  top:585px;
  width:84px;
  height:16px;
  display:flex;
}
#u482 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u482_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u483_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u483 {
  border-width:0px;
  position:absolute;
  left:668px;
  top:585px;
  width:72px;
  height:16px;
  display:flex;
}
#u483 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u483_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
