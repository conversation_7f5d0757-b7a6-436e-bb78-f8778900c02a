package com.droneclub.source.template.common.utils;

import io.minio.MinioClient;
import io.minio.ObjectWriteResponse;
import io.minio.PutObjectArgs;
import io.minio.RemoveObjectArgs;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;

@Slf4j
public class MinioUtils {

    public static String PREVIEW_URL = "";
    public static String BUCKET = "public";
    private volatile static MinioUtils minioUtils;
    private volatile static MinioClient minioClient;

    public MinioUtils() {
    }

    public static MinioUtils getMinioUtils() {
        if (minioUtils == null) {
            synchronized (MinioUtils.class) {
                if (minioUtils == null) {
                    minioUtils = new MinioUtils();
                }
            }
        }
        return minioUtils;
    }

    /**
     * 删除文件
     *
     * @param bucket   存储桶
     * @param filePath 文件路径
     */
    public static void delete(String bucket, String filePath) {
        try {
            minioClient.removeObject(RemoveObjectArgs.builder()
                    .bucket(bucket)
                    .object(filePath)
                    .build());
        } catch (Exception e) {
            log.info("delete minio file  fail: {} {}", bucket, filePath, e);
        }
    }

    /**
     * 初始化minio utils
     *
     * @param serverUrl  minio服务地址
     * @param serverUrl  serverUrl
     * @param previewUrl previewUrl
     * @param accessKey  accessKey
     * @param secretKey  secretKey
     */
    public void initMinioUtils(String serverUrl, String bucket, String previewUrl, String accessKey, String secretKey) {
        log.info("init minio client: {}, accessKey: {}", secretKey, accessKey);
        PREVIEW_URL = previewUrl;
        BUCKET = bucket;
        minioClient =
                MinioClient.builder()
                        .endpoint(serverUrl)
                        .credentials(accessKey, secretKey)
                        .build();
    }

    /**
     * 上传文件
     *
     * @param bucket      存储桶
     * @param filePath    文件路径
     * @param contentType 内容类型
     * @param in          文件字节流
     * @return 上传结果
     */
    public String upload(String bucket, String filePath, String contentType, InputStream in) {
        String uploadPath = "";
        try {
            ObjectWriteResponse response = minioClient.putObject(
                    PutObjectArgs.builder().bucket(bucket).object(filePath).stream(
                                    in, -1, 10485760).contentType(contentType)
                            .build());
            uploadPath = response.object();
        } catch (Exception e) {
            log.info("upload file to minio fail: {} {}", bucket, filePath, e);
        }
        return uploadPath;
    }
}
