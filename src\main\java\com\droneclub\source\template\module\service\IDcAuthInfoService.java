package com.droneclub.source.template.module.service;

import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcAuthInfo;
import com.droneclub.source.template.module.pojo.DcAuthInfoListSearch;
import com.droneclub.source.template.module.pojo.DcAuthInfoVO;

public interface IDcAuthInfoService {

    ListData<DcAuthInfoVO> getDcAuthInfoList(DcAuthInfoListSearch params);

    DcAuthInfoVO getDcAuthInfoById(Integer id);

    DcAuthInfo createDcAuthInfo(DcAuthInfo data);

    boolean updateDcAuthInfo(DcAuthInfo data);

    boolean deleteDcAuthInfoById(Integer id);
}
