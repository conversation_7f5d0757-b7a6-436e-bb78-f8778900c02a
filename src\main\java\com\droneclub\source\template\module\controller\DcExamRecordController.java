package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcExamRecord;
import com.droneclub.source.template.module.pojo.DcExamRecordVO;
import com.droneclub.source.template.module.pojo.DcExamRecordListSearch;
import com.droneclub.source.template.module.service.IDcExamRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dcExamRecord")
public class DcExamRecordController {

    private final IDcExamRecordService dcExamRecordService;

    @GetMapping("/getDcExamRecordList")
    public RestResult<ListData<DcExamRecordVO>> getDcExamRecordList(DcExamRecordListSearch params) {
        return RestResult.success(dcExamRecordService.getDcExamRecordList(params));
    }

    @GetMapping("/getDcExamRecordById")
    public RestResult<DcExamRecordVO> getDcExamRecordById(Integer id) {
        return RestResult.success(dcExamRecordService.getDcExamRecordById(id));
    }

    @PostMapping("/createDcExamRecord")
    public RestResult<DcExamRecord> createDcExamRecord(@RequestBody DcExamRecord data) {
        return RestResult.success(dcExamRecordService.createDcExamRecord(data));
    }

    @PutMapping("/updateDcExamRecord")
    public RestResult<DcExamRecord> updateDcExamRecord(@RequestBody DcExamRecord data) {
        return RestResult.success(dcExamRecordService.updateDcExamRecord(data));
    }

    @DeleteMapping("/deleteDcExamRecordById")
    public RestResult<Boolean> deleteDcExamRecordById(Integer id) {
        return RestResult.success(dcExamRecordService.deleteDcExamRecordById(id));
    }
}
