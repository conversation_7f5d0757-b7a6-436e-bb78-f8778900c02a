package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import cn.soulspark.source.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.droneclub.source.template.common.email.EmailConfig;
import com.droneclub.source.template.common.email.EmailSender;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.common.utils.ResourceUtils;
import com.droneclub.source.template.entity.DcAuthInfo;
import com.droneclub.source.template.entity.DcBxApply;
import com.droneclub.source.template.mapper.DcBxApplyMapper;
import com.droneclub.source.template.module.pojo.DcBxApplyListSearch;
import com.droneclub.source.template.module.pojo.DcBxApplyVO;
import com.droneclub.source.template.module.service.IDcBxApplyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcBxApplyServiceImpl implements IDcBxApplyService {

    private final DcBxApplyMapper dcBxApplyMapper;
    private final EmailSender emailSender;
    private final EmailConfig emailConfig;

    @Override
    public ListData<DcBxApplyVO> getDcBxApplyList(DcBxApplyListSearch params) {
        QueryWrapper<DcBxApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("create_time");
        // 查询总数
        Long total = dcBxApplyMapper.selectCount(queryWrapper);

        // 分页查询
        Page<DcBxApply> page = new Page<>(params.getPageNo(), params.getPageSize());
        IPage<DcBxApply> dcBxApplyPage = dcBxApplyMapper.selectPage(page, queryWrapper);
        List<DcBxApply> list = dcBxApplyPage.getRecords();
        List<DcBxApplyVO> listVO = list.stream()
                .map(dcBxApply -> JSONObject.parseObject(JSONObject.toJSONString(dcBxApply), DcBxApplyVO.class))
                .collect(Collectors.toList());
        return new ListData<>(listVO, total, params.getPageNo(), params.getPageSize());
    }

    private void sendEmail(DcBxApply data) {
        if (StringUtils.isValid(data.getPhone()) && StringUtils.isValid(data.getPhone())) {
            String emailTemplate = ResourceUtils.loadResource("emailTemplate/bx-apply-notice.html");
            if (emailTemplate != null) {
                emailTemplate = emailTemplate.replaceAll("#\\{phone}", data.getPhone());
                String subject = String.format("新的保险购买申请-%s", data.getPhone());
                emailSender.createMessage(emailConfig.getReceiver())
                        .subject(subject)
                        .htmlContent(emailTemplate)
                        .send();
                log.info("邮件通知成功: {}", subject);
            }
        }
    }

    @Override
    public DcBxApplyVO getDcBxApplyById(Integer id) {
        DcBxApply dcBxApply = dcBxApplyMapper.selectById(id);
        if (dcBxApply == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        return JSONObject.parseObject(JSONObject.toJSONString(dcBxApply), DcBxApplyVO.class);
    }


    @Override
    public DcBxApply createDcBxApply(DcBxApply data) {
        boolean rs = dcBxApplyMapper.insert(data) > 0;
        log.info("创建 DcBxApply: {}", rs ? "成功" : "失败");
        if (rs) {
            sendEmail(data);
        }
        return data;
    }

    @Override
    public boolean updateDcBxApply(DcBxApply data) {
        boolean rs = dcBxApplyMapper.updateById(data) > 0;
        log.info("更新 DcBxApply: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public boolean deleteDcBxApplyById(Integer id) {
        boolean rs = dcBxApplyMapper.deleteById(id) > 0;
        log.info("删除 DcBxApply: {}", rs ? "成功" : "失败");
        return rs;
    }
}
