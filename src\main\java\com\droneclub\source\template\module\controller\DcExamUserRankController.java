package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcExamUserRank;
import com.droneclub.source.template.module.pojo.DcExamUserRankVO;
import com.droneclub.source.template.module.pojo.DcExamUserRankListSearch;
import com.droneclub.source.template.module.service.IDcExamUserRankService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dcExamUserRank")
public class DcExamUserRankController {

    private final IDcExamUserRankService dcExamUserRankService;

    @GetMapping("/getDcExamUserRankList")
    public RestResult<ListData<DcExamUserRankVO>> getDcExamUserRankList(DcExamUserRankListSearch params) {
        return RestResult.success(dcExamUserRankService.getDcExamUserRankList(params));
    }

    @GetMapping("/getDcExamUserRankById")
    public RestResult<DcExamUserRankVO> getDcExamUserRankById(Integer id) {
        return RestResult.success(dcExamUserRankService.getDcExamUserRankById(id));
    }

    @PostMapping("/createDcExamUserRank")
    public RestResult<DcExamUserRank> createDcExamUserRank(@RequestBody DcExamUserRank data) {
        return RestResult.success(dcExamUserRankService.createDcExamUserRank(data));
    }

    @PutMapping("/updateDcExamUserRank")
    public RestResult<Boolean> updateDcExamUserRank(@RequestBody DcExamUserRank data) {
        return RestResult.success(dcExamUserRankService.updateDcExamUserRank(data));
    }

    @DeleteMapping("/deleteDcExamUserRankById")
    public RestResult<Boolean> deleteDcExamUserRankById(Integer id) {
        return RestResult.success(dcExamUserRankService.deleteDcExamUserRankById(id));
    }
}
