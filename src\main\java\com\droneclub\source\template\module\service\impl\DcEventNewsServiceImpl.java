package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import cn.soulspark.source.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcEventNews;
import com.droneclub.source.template.mapper.DcEventNewsMapper;
import com.droneclub.source.template.module.pojo.DcEventNewsListSearch;
import com.droneclub.source.template.module.pojo.DcEventNewsVO;
import com.droneclub.source.template.module.service.IDcEventNewsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcEventNewsServiceImpl implements IDcEventNewsService {

    private final DcEventNewsMapper dcEventNewsMapper;

    @Override
    public ListData<DcEventNewsVO> getDcEventNewsList(DcEventNewsListSearch params) {
        LambdaQueryWrapper<DcEventNews> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isValid(params.getSearchKey())) {
            queryWrapper.and(w -> w.like(DcEventNews::getTitle, params.getSearchKey()));
        }
        if (StringUtils.isValid(params.getEventType())) {
            queryWrapper.and(w -> w.eq(DcEventNews::getEventType, params.getEventType()));
        }
        // 查询总数
        Long total = dcEventNewsMapper.selectCount(queryWrapper);
        queryWrapper.orderByDesc(DcEventNews::getCreateTime);
        // 分页查询
        Page<DcEventNews> page = new Page<>(params.getPageNo(), params.getPageSize());
        IPage<DcEventNews> dcEventNewsPage = dcEventNewsMapper.selectPage(page, queryWrapper);
        List<DcEventNews> list = dcEventNewsPage.getRecords();
        List<DcEventNewsVO> listVO = list.stream()
                .map(dcEventNews -> JSONObject.parseObject(JSONObject.toJSONString(dcEventNews), DcEventNewsVO.class))
                .collect(Collectors.toList());
        return new ListData<>(listVO, total, params.getPageNo(), params.getPageSize());
    }

    @Override
    public DcEventNewsVO getDcEventNewsById(Integer id) {
        DcEventNews dcEventNews = dcEventNewsMapper.selectById(id);
        if (dcEventNews == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        return JSONObject.parseObject(JSONObject.toJSONString(dcEventNews), DcEventNewsVO.class);
    }


    @Override
    public DcEventNews createDcEventNews(DcEventNews data) {
        boolean rs = dcEventNewsMapper.insert(data) > 0;
        log.info("创建 DcEventNews: {}", rs ? "成功" : "失败");
        return data;
    }

    @Override
    public boolean updateDcEventNews(DcEventNews data) {
        boolean rs = dcEventNewsMapper.updateById(data) > 0;
        log.info("更新 DcEventNews: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public boolean deleteDcEventNewsById(Integer id) {
        boolean rs = dcEventNewsMapper.deleteById(id) > 0;
        log.info("删除 DcEventNews: {}", rs ? "成功" : "失败");
        return rs;
    }
}
