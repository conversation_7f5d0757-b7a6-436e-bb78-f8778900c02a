package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import cn.soulspark.source.common.utils.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.droneclub.source.template.common.email.EmailConfig;
import com.droneclub.source.template.common.email.EmailSender;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.common.utils.ResourceUtils;
import com.droneclub.source.template.common.utils.TemplateSessionUtils;
import com.droneclub.source.template.entity.DcAuthInfo;
import com.droneclub.source.template.entity.DcFile;
import com.droneclub.source.template.entity.User;
import com.droneclub.source.template.mapper.DcAuthInfoMapper;
import com.droneclub.source.template.module.pojo.DcAuthInfoListSearch;
import com.droneclub.source.template.module.pojo.DcAuthInfoVO;
import com.droneclub.source.template.module.service.IDcAuthInfoService;
import com.droneclub.source.template.module.service.IDcFileService;
import com.droneclub.source.template.system.service.IUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcAuthInfoServiceImpl implements IDcAuthInfoService {

    private final DcAuthInfoMapper dcAuthInfoMapper;
    private final IDcFileService dcFileService;
    private final IUserService userService;
    private final EmailSender emailSender;
    private final EmailConfig emailConfig;

    @Override
    public ListData<DcAuthInfoVO> getDcAuthInfoList(DcAuthInfoListSearch params) {
        QueryWrapper<DcAuthInfo> queryWrapper = new QueryWrapper<>();
        if (params.getAuthStatus() != null) {
            queryWrapper.eq("auth_status", params.getAuthStatus());
        }
        // 查询总数
        Long total = dcAuthInfoMapper.selectCount(queryWrapper);
        queryWrapper.orderByDesc("create_time");
        // 分页查询
        Page<DcAuthInfo> page = new Page<>(params.getPageNo(), params.getPageSize());
        IPage<DcAuthInfo> dcAuthInfoPage = dcAuthInfoMapper.selectPage(page, queryWrapper);
        List<DcAuthInfo> list = dcAuthInfoPage.getRecords();
        List<DcAuthInfoVO> listVO = list.stream()
                .map(dcAuthInfo -> JSONObject.parseObject(JSONObject.toJSONString(dcAuthInfo), DcAuthInfoVO.class))
                .collect(Collectors.toList());
        for (DcAuthInfoVO vo : listVO) {
            buildExtraInfo(vo);
        }
        return new ListData<>(listVO, total, params.getPageNo(), params.getPageSize());
    }

    private void buildExtraInfo(DcAuthInfoVO vo) {
        List<DcFile> files = dcFileService.getFileList("auth_info", vo.getId());
        Map<String, List<DcFile>> fileTypeMap = files.stream().collect(Collectors.groupingBy(DcFile::getFileType));
        JSONObject filesJson = new JSONObject();
        for (String fileType : fileTypeMap.keySet()) {
            filesJson.put(fileType, fileTypeMap.get(fileType).stream()
                    .map(DcFile::getFilePath)
                    .collect(Collectors.toList()));
        }
        vo.setFiles(filesJson);
    }

    @Override
    public DcAuthInfoVO getDcAuthInfoById(Integer id) {
        DcAuthInfo dcAuthInfo = dcAuthInfoMapper.selectById(id);
        if (dcAuthInfo == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        DcAuthInfoVO vo = JSONObject.parseObject(JSONObject.toJSONString(dcAuthInfo), DcAuthInfoVO.class);
        buildExtraInfo(vo);
        return vo;
    }


    @Override
    public DcAuthInfo createDcAuthInfo(DcAuthInfo data) {
        data.setAuthStatus(1);
        boolean rs = dcAuthInfoMapper.insert(data) > 0;
        log.info("创建 DcAuthInfo: {}", rs ? "成功" : "失败");
        bindFile(data);
        if (rs) {
            // 更新用户信息
            userService.updateUser(User.builder()
                    .id(TemplateSessionUtils.getCurrentUser().getId())
                    .authId(data.getId())
                    .authStatus(1)
                    .build());
        }
        sendEmail(data);
        return data;
    }

    private void sendEmail(DcAuthInfo data) {
        if (StringUtils.isValid(data.getAuthName()) && StringUtils.isValid(data.getAuthType())) {
            String emailTemplate = ResourceUtils.loadResource("emailTemplate/renzheng-notice.html");
            if (emailTemplate != null) {
                emailTemplate = emailTemplate.replaceAll("#\\{name}", data.getAuthName());
                emailTemplate = emailTemplate.replaceAll("#\\{type}", data.getAuthType());
                String subject = String.format("新的认证申请-%s", data.getAuthName());
                emailSender.createMessage(emailConfig.getReceiver())
                        .subject(subject)
                        .htmlContent(emailTemplate)
                        .send();
                log.info("邮件通知成功: {}", subject);
            }
        }
    }

    private void bindFile(DcAuthInfo data) {
        if (data.getFiles() == null || data.getFiles().size() == 0) {
            return;
        }
        for (String key : data.getFiles().keySet()) {
            dcFileService.bindFile("auth_info", key, data.getId(),
                    JSONArray.parseArray(data.getFiles().getJSONArray(key).toJSONString(), String.class)
            );
        }
    }

    @Override
    public boolean updateDcAuthInfo(DcAuthInfo data) {
        boolean rs = dcAuthInfoMapper.updateById(data) > 0;
        log.info("更新 DcAuthInfo: {}", rs ? "成功" : "失败");
        bindFile(data);
        if (rs) {
            User user = userService.getUserByAuthId(data.getId());
            // 更新用户信息
            userService.updateUser(User.builder()
                    .id(user.getId())
                    .authId(data.getId())
                    .build());
            sendEmail(data);
        }
        return rs;
    }

    @Override
    public boolean deleteDcAuthInfoById(Integer id) {
        boolean rs = dcAuthInfoMapper.deleteById(id) > 0;
        log.info("删除 DcAuthInfo: {}", rs ? "成功" : "失败");
        return rs;
    }
}
