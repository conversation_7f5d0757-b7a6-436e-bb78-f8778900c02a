package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tm_user")
public class User {

    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 微信小程序openID
     */
    @TableField(exist = false)
    private String openId;
    private String account;
    private String password;
    private String userName;
    private Integer orgId;
    private Integer authId;
    private Integer activeState;
    private Integer authStatus;
    private Integer userStatus;
    private String avatar;
    private String regularPlace;
    private String expertise;
    private String certificateType;
    private String certificateStatus;
    /**
     * 培训机构ID
     */
    private Integer pxCompanyId;
    /**
     * 培训所在城市
     */
    private String pxCity;
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    private String createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer updateUser;
    private String updateTime;
    private String isDelete;

}
