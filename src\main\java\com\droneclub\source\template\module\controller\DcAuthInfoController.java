package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcAuthInfo;
import com.droneclub.source.template.module.pojo.DcAuthInfoListSearch;
import com.droneclub.source.template.module.pojo.DcAuthInfoVO;
import com.droneclub.source.template.module.service.IDcAuthInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dcAuthInfo")
public class DcAuthInfoController {

    private final IDcAuthInfoService dcAuthInfoService;

    @GetMapping("/getDcAuthInfoList")
    public RestResult<ListData<DcAuthInfoVO>> getDcAuthInfoList(DcAuthInfoListSearch params) {
        return RestResult.success(dcAuthInfoService.getDcAuthInfoList(params));
    }

    @GetMapping("/getDcAuthInfoById")
    public RestResult<DcAuthInfoVO> getDcAuthInfoById(Integer id) {
        return RestResult.success(dcAuthInfoService.getDcAuthInfoById(id));
    }

    @PostMapping("/createDcAuthInfo")
    public RestResult<DcAuthInfo> createDcAuthInfo(@RequestBody DcAuthInfo data) {
        return RestResult.success(dcAuthInfoService.createDcAuthInfo(data));
    }

    @PutMapping("/updateDcAuthInfo")
    public RestResult<Boolean> updateDcAuthInfo(@RequestBody DcAuthInfo data) {
        return RestResult.success(dcAuthInfoService.updateDcAuthInfo(data));
    }

    @DeleteMapping("/deleteDcAuthInfoById")
    public RestResult<Boolean> deleteDcAuthInfoById(Integer id) {
        return RestResult.success(dcAuthInfoService.deleteDcAuthInfoById(id));
    }
}
