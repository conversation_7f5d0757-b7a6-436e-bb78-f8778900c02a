package com.droneclub.source.template.timedtask;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.droneclub.source.template.common.utils.ResourceUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Random;

public class GetJobSp {
    private static final String[] CERTIFICATE_LEVEL = new String[]{"[\"垂直起降固定翼、超视距驾驶员、四类（25kg以上）\",\"\"]",
            "[\"多旋翼、视距内驾驶员、三类（25kg以下）\",\"\"]", "[\"多旋翼、超视距驾驶员、四类（25kg以上）\",\"\"]"};

    public static void main(String[] args) {
        String jobs = ResourceUtils.loadResource("otherdata/jobs.json");
        JSONArray jobArrays = JSONObject.parseObject(jobs).getJSONArray("rows");
        for (int i = 0; i < jobArrays.size(); i++) {
            JSONObject item = jobArrays.getJSONObject(i);
            String name = item.getString("company");
            String phone = item.getString("phone");
            String address = item.getString("address");
            String startTime = item.getString("startTime");
            String content = item.getString("detail");
            String extraSkill = item.getString("无");
            String bindUrl = item.getString("id");
            Integer salary = item.getInteger("salary");
            buildSql(name, phone, address, startTime, content, extraSkill, bindUrl, salary);
        }
    }


    public static String buildSql(String name, String phone, String address,
                                  String startTime, String content, String extraSkill, String bindUrl, Integer salary) {
        String sql = String.format("INSERT INTO `drone-club`.dc_publish_info (\n" +
                        "info_type,info_status,contact_person,contact_phone,certificate_level,\n" +
                        "uav_type,uav_weight,total_flight_time,extra_skill,expect_job_content,\n" +
                        "job_expense,work_area,work_place,work_start_time,work_end_time,\n" +
                        "job_content,pay_money,require_certificate_level,provide_food,provide_room,\n" +
                        "require_extra_skill,device_type,device_description,device_rent,device_place,\n" +
                        "lease_start_time,lease_end_time,\n" +
                        "create_user,create_time,update_user,update_time,is_delete,bind_url)\n" +
                        "VALUES\n" +
                        "\t (2,1,'%s','%s',NULL,\n" +
                        "\tNULL,NULL,NULL,NULL,NULL,\n" +
                        "NULL,NULL,'%s','%s','%s',\n" +
                        "'%s','%s','%s',0,0,\n" +
                        "'%s',NULL,NULL,NULL,NULL,\n" +
                        "NULL,NULL,\n" +
                        "4,NOW(),4,NOW(),0,%s);",
                name, phone,
                address, startTime, getEndTime(startTime),
                content.replaceAll("无人机飞手圈", "瑞鹰无人机"), -1, "[]",
                "无",
                bindUrl
        );
        System.out.println(sql.replaceAll("\n", ""));
        return sql;
    }

    public static String getEndTime(String startTime) {
        if (startTime.contains("T")) {
            startTime = startTime.substring(0, startTime.indexOf("T"));
        }
        LocalDate startDate = LocalDate.parse(startTime);

        // 随机增加5天到一个月的时间
        Random random = new Random();
        int daysToAdd = random.nextInt(26) + 5; // 5到30天之间的随机天数
        LocalDate endDate = startDate.plusDays(daysToAdd);

        // 格式化日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return endDate.format(formatter);
    }

    public static int getPayMoney() {
        Random random = new Random();

        int lowerBound = 200;
        int upperBound = 1200;
        int count = 100;

        for (int i = 0; i < count; i++) {
            int randomNum = random.nextInt(upperBound - lowerBound + 1) + lowerBound;
            return randomNum;
        }
        return lowerBound;
    }

    public static String getLevel() {
        Random random = new Random();
        int randomIndex = random.nextInt(CERTIFICATE_LEVEL.length);

        return CERTIFICATE_LEVEL[randomIndex];
    }
}
