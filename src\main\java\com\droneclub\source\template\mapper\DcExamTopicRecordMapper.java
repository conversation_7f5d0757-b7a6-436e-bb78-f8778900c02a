package com.droneclub.source.template.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.droneclub.source.template.entity.DcExamTopicRecord;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DcExamTopicRecordMapper extends BaseMapper<DcExamTopicRecord> {

    @Insert("<script>" +
            "INSERT INTO dc_exam_topic_record " +
            "(exam_id, topic_id, topic_answer, correct_answer, answer_result, create_user, create_time, update_user, update_time, is_delete) " +
            "VALUES " +
            "<foreach collection='list' item='record' separator=','>" +
            "(#{record.examId}, #{record.topicId}, #{record.topicAnswer}, #{record.correctAnswer}, #{record.answerResult}, " +
            "#{record.createUser}, NOW(), #{record.updateUser}, NOW(), #{record.isDelete})" +
            "</foreach>" +
            "</script>")
    int insertBatch(@Param("list") List<DcExamTopicRecord> records);

    List<DcExamTopicRecord> selectExamTopicRecordWithChapter(@Param("examId") Integer examId);
}
