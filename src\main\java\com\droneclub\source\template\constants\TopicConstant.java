package com.droneclub.source.template.constants;

public interface TopicConstant {

    interface ChapterNameClass {

        String[] BASE_CLASS = {
                "第一章 无人机概述",
                "第二章 系统组成及介绍",
                "第三章 飞行原理与飞行性能",
                "第四章 航空气象",
                "第五章 空中交通管制",
                "第六章 无人机驾驶员起降阶段操纵技术",
                "第七章 无人机驾驶员巡航阶段操纵技术及相关知识",
                "第八章 旋翼机"
        };
        String[] ZH_CLASS = {
                "第九章 综合问答"
        };
        String[] JY_CLASS = {
                "第十章 教员法（教员适用）"
        };

        String[] KS_CLASS = {
                "口试-飞行过程",
                "口试-飞行原理",
                "口试-飞行理论",
                "口试-公式、测算原理",
                "口试-电池",
                "口试-电调",
                "口试-遥控器",
                "口试-飞控",
                "口试-地面站",
                "口试-电机",
                "口试-螺旋桨",
                "口试-动力系统",
                "口试-链路、连接、安装",
                "口试-内部件",
                "口试-教学",
                "口试-气象"
        };
    }

}
