package com.droneclub.source.template.module.pojo;

import com.droneclub.source.template.common.model.BaseSearchParams;
import lombok.Data;

@Data
public class DcCompanyListSearch extends BaseSearchParams {

    private Integer companyId;
    private String companyName;
    private String industry;
    private String contactWay;
    private String[] industries;
    private String city;
    private Integer companyStatus;
    private Integer createUser;
    private String createStartTime;
    private String createEndTime;
    /**
     * default: 默认排序 lookNum: 浏览量倒叙排序 default_desc: 创建时间倒序
     */
    private String sortType;
}
