package com.droneclub.source.template.common.utils;

/**
 * 驼峰转换
 */
public class CamelCaseConverterUtils {

    public static String toCamelCase(String s) {
        StringBuilder result = new StringBuilder();
        boolean nextUpperCase = false;

        for (int i = 0; i < s.length(); i++) {
            char currentChar = s.charAt(i);

            if (currentChar == '_') {
                nextUpperCase = true;
            } else {
                if (nextUpperCase) {
                    result.append(Character.toUpperCase(currentChar));
                    nextUpperCase = false;
                } else {
                    result.append(Character.toLowerCase(currentChar));
                }
            }
        }

        return result.toString();
    }

    public static void main(String[] args) {
        System.out.println(toCamelCase("sys_config"));
    }
}
