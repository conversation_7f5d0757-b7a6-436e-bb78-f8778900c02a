package com.droneclub.source.template.common.cache;

import com.droneclub.source.template.module.service.IDcCompanyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class CompanyDataLoader implements CommandLineRunner {

    private final IDcCompanyService companyService;

    @Override
    public void run(String... args) {
        companyService.refreshCompanySystemConfig();
    }
}
