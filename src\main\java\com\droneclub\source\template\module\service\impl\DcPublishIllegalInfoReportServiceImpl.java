package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.common.utils.TemplateSessionUtils;
import com.droneclub.source.template.entity.DcPublishIllegalInfoReport;
import com.droneclub.source.template.entity.PublishInfo;
import com.droneclub.source.template.entity.User;
import com.droneclub.source.template.mapper.DcPublishIllegalInfoReportMapper;
import com.droneclub.source.template.mapper.PublishInfoMapper;
import com.droneclub.source.template.mapper.TmUserMapper;
import com.droneclub.source.template.module.pojo.DcPublishIllegalInfoReportListSearch;
import com.droneclub.source.template.module.pojo.DcPublishIllegalInfoReportVO;
import com.droneclub.source.template.module.service.IDcPublishIllegalInfoReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcPublishIllegalInfoReportServiceImpl implements IDcPublishIllegalInfoReportService {

    private final DcPublishIllegalInfoReportMapper dcPublishIllegalInfoReportMapper;
    private final TmUserMapper userMapper;
    private final PublishInfoMapper publishInfoMapper;


    @Override
    public ListData<DcPublishIllegalInfoReportVO> getDcPublishIllegalInfoReportList(DcPublishIllegalInfoReportListSearch params) {
        LambdaQueryWrapper<DcPublishIllegalInfoReport> queryWrapper = new LambdaQueryWrapper<>();
        if (params.getInfoStatus() != null) {
            LambdaQueryWrapper<PublishInfo> publishInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            publishInfoLambdaQueryWrapper.in(PublishInfo::getInfoStatus, params.getInfoStatus());
            List<PublishInfo> publishInfos = publishInfoMapper.selectList(publishInfoLambdaQueryWrapper);
            // 根据状态筛选info id作为条件筛选举报信息
            if (publishInfos.size() > 0) {
                Set<Integer> infoIds = publishInfos.stream().map(PublishInfo::getId).collect(Collectors.toSet());
                queryWrapper.in(DcPublishIllegalInfoReport::getPublishInfoId, infoIds);
            }
        }
        // 查询总数
        Long total = dcPublishIllegalInfoReportMapper.selectCount(queryWrapper);
        queryWrapper.orderByDesc(DcPublishIllegalInfoReport::getCreateTime);
        // 分页查询
        Page<DcPublishIllegalInfoReport> page = new Page<>(params.getPageNo(), params.getPageSize());
        IPage<DcPublishIllegalInfoReport> dcPublishIllegalInfoReportPage = dcPublishIllegalInfoReportMapper.selectPage(page, queryWrapper);
        List<DcPublishIllegalInfoReport> list = dcPublishIllegalInfoReportPage.getRecords();
        List<DcPublishIllegalInfoReportVO> listVO = list.stream()
                .map(dcPublishIllegalInfoReport -> JSONObject.parseObject(JSONObject.toJSONString(dcPublishIllegalInfoReport), DcPublishIllegalInfoReportVO.class))
                .collect(Collectors.toList());
        Map<Integer, User> idUserNameMap = getUserMap(listVO);
        Map<Integer, PublishInfo> idPublishInfoMap = getPublishInfoMap(listVO);
        // 填充信息
        for (DcPublishIllegalInfoReportVO vo : listVO) {
            vo.setReportUserName(idUserNameMap.get(vo.getCreateUser()) != null ? idUserNameMap.get(vo.getReportUser()).getUserName() : null);
            vo.setCreateUserName(idUserNameMap.get(vo.getCreateUser()) != null ? idUserNameMap.get(vo.getCreateUser()).getUserName() : null);
            vo.setPublishInfo(idPublishInfoMap.get(vo.getPublishInfoId()));
        }
        return new ListData<>(listVO, total, params.getPageNo(), params.getPageSize());
    }

    private Map<Integer, PublishInfo> getPublishInfoMap(List<DcPublishIllegalInfoReportVO> listVO) {
        Set<Integer> infoIds = listVO.stream().map(DcPublishIllegalInfoReportVO::getPublishInfoId).collect(Collectors.toSet());
        Map<Integer, PublishInfo> idPublishInfoMap = new HashMap<>();
        if (infoIds.size() > 0) {
            QueryWrapper<PublishInfo> wrapper = new QueryWrapper<>();
            wrapper.in("id", infoIds);
            List<PublishInfo> publishInfos = publishInfoMapper.selectList(wrapper);
            idPublishInfoMap = publishInfos.stream()
                    .collect(Collectors.toMap(PublishInfo::getId, v -> v));
        }
        return idPublishInfoMap;
    }

    @NotNull
    private Map<Integer, User> getUserMap(List<DcPublishIllegalInfoReportVO> listVO) {
        Set<Integer> reportUserIds = listVO.stream().map(DcPublishIllegalInfoReportVO::getReportUser).collect(Collectors.toSet());
        Set<Integer> userIds = listVO.stream().map(DcPublishIllegalInfoReportVO::getCreateUser).collect(Collectors.toSet());
        reportUserIds.addAll(userIds);
        Map<Integer, User> idUserNameMap = new HashMap<>();
        if (userIds.size() > 0) {
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            wrapper.in("id", reportUserIds);
            List<User> existUserList = userMapper.selectList(wrapper);
            idUserNameMap = existUserList.stream()
                    .collect(Collectors.toMap(User::getId, v -> v));
        }
        return idUserNameMap;
    }

    @Override
    public DcPublishIllegalInfoReportVO getDcPublishIllegalInfoReportById(Integer id) {
        DcPublishIllegalInfoReport dcPublishIllegalInfoReport = dcPublishIllegalInfoReportMapper.selectById(id);
        if (dcPublishIllegalInfoReport == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        return JSONObject.parseObject(JSONObject.toJSONString(dcPublishIllegalInfoReport), DcPublishIllegalInfoReportVO.class);
    }


    @Override
    public DcPublishIllegalInfoReport createDcPublishIllegalInfoReport(DcPublishIllegalInfoReport data) {
        data.setReportUser(TemplateSessionUtils.getCurrentUser().getId());
        boolean rs = dcPublishIllegalInfoReportMapper.insert(data) > 0;
        log.info("创建 DcPublishIllegalInfoReport: {}", rs ? "成功" : "失败");
        return data;
    }

    @Override
    public boolean updateDcPublishIllegalInfoReport(DcPublishIllegalInfoReport data) {
        boolean rs = dcPublishIllegalInfoReportMapper.updateById(data) > 0;
        log.info("更新 DcPublishIllegalInfoReport: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public boolean deleteDcPublishIllegalInfoReportById(Integer id) {
        boolean rs = dcPublishIllegalInfoReportMapper.deleteById(id) > 0;
        log.info("删除 DcPublishIllegalInfoReport: {}", rs ? "成功" : "失败");
        return rs;
    }
}
