package com.droneclub.source.template.single.region.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 省份信息实体类
 */
@Data
@TableName("sys_province")
public class SysProvinceEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 省份编码
     */
    private String provinceCode;
    
    /**
     * 省份名称
     */
    private String provinceName;
    
    /**
     * 首字母
     */
    private String firstChar;

    /**
     * 拼音
     */
    private String pinyin;
    
    /**
     * 区号
     */
    private String cityCode;
    
    /**
     * 中心点坐标
     */
    private String center;
    
    /**
     * 状态 0:删除 1:有效 2:更新
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 