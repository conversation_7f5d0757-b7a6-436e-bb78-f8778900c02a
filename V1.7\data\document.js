﻿$axure.loadDocument(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,_(c,d,e,f,g,d,h,d,i,d,j,k,l,d,m,f,n,f,o,d,p,f),q,_(r,[_(s,t,u,v,w,x,y,t,z,[_(s,A,u,v,w,B,y,C,z,[_(s,D,u,E,w,B,y,F,z,[_(s,G,u,H,w,B,y,I),_(s,J,u,K,w,B,y,L),_(s,M,u,N,w,B,y,O)]),_(s,P,u,Q,w,B,y,R)])]),_(s,t,u,S,w,x,y,t,z,[_(s,T,u,S,w,B,y,U,z,[_(s,V,u,W,w,B,y,X)])])]),Y,[Z,ba,bb],bc,[bd,be,bf],bg,_(bh,t),bi,_(bj,_(s,bk,bl,bm,bn,bo,bp,bq,br,bs,bt,_(bu,bv,bw,bx,by,bz),bA,bB,bC,f,bD,bE,bF,bq,bG,bq,bH,bI,bJ,f,bK,_(bL,bM,bN,bM),bO,_(bP,bM,bQ,bM),bR,d,bS,f,bT,bk,bU,_(bu,bv,bw,bV),bW,_(bu,bv,bw,bX),bY,bZ,ca,bv,by,bZ,cb,cc,cd,ce,cf,cg,ch,cg,ci,cg,cj,cg,ck,_(),cl,null,cm,null,cn,cc,co,_(cp,f,cq,cr,cs,cr,ct,cr,cu,bM,bw,_(cv,cw,cx,cw,cy,cw,cz,cA)),cB,_(cp,f,cq,bM,cs,cr,ct,cr,cu,bM,bw,_(cv,cw,cx,cw,cy,cw,cz,cA)),cC,_(cp,f,cq,bz,cs,bz,ct,cr,cu,bM,bw,_(cv,cw,cx,cw,cy,cw,cz,cD)),cE,cF),cG,_(cH,_(s,cI,bA,cJ,bn,cK,bY,cc,bU,_(bu,bv,bw,cL),bD,cM,cd,cN,cf,cc,ch,cc,ci,cc,cj,cc),cO,_(s,cP,bA,cQ,bn,cK,bY,cc,bU,_(bu,bv,bw,cL),bD,cM,cd,cN,cf,cc,ch,cc,ci,cc,cj,cc),cR,_(s,cS,bA,cT,bn,cK,bY,cc,bU,_(bu,bv,bw,cL),bD,cM,cd,cN,cf,cc,ch,cc,ci,cc,cj,cc),cU,_(s,cV,bA,cW,bn,cK,bY,cc,bU,_(bu,bv,bw,cL),bD,cM,cd,cN,cf,cc,ch,cc,ci,cc,cj,cc),cX,_(s,cY,bn,cK,bY,cc,bU,_(bu,bv,bw,cL),bD,cM,cd,cN,cf,cc,ch,cc,ci,cc,cj,cc),cZ,_(s,da,bA,db,bn,cK,bY,cc,bU,_(bu,bv,bw,cL),bD,cM,cd,cN,cf,cc,ch,cc,ci,cc,cj,cc),dc,_(s,dd,bY,cc,bU,_(bu,bv,bw,cL),bD,cM,cd,cN,cf,cc,ch,cc,ci,cc,cj,cc),de,_(s,df,bt,_(bu,bv,bw,dg,by,bz)),dh,_(s,di,bU,_(bu,bv,bw,dj)),dk,_(s,dl),dm,_(s,dn,bY,cc),dp,_(s,dq,bA,cW,bD,cM,bU,_(bu,bv,bw,cL),bY,cc,cd,cN,cf,cc,ch,cc,ci,cc,cj,cc),dr,_(s,ds,bU,_(bu,bv,bw,dt)),du,_(s,dv,bU,_(bu,bv,bw,bV)),dw,_(s,dx,bt,_(bu,bv,bw,bV,by,bz),bU,_(bu,bv,bw,dy),bY,cc,cb,bs),dz,_(s,dA,cb,bs),dB,_(s,dC,bU,_(bu,bv,bw,cL)),dD,_(s,dE,bD,cM,cd,cN),dF,_(s,dG,bD,cM,cd,cN),dH,_(s,dI,bY,cc,bU,_(bu,bv,bw,dJ),co,_(cp,d,cq,cr,cs,cr,ct,cr,cu,bM,bw,_(cv,cw,cx,cw,cy,cw,cz,dK)),bD,cM,cd,cN,cf,dL,ch,dL,ci,dL,cj,dL),dM,_(s,dN,bU,_(bu,dO,dP,_(bL,dQ,bN,bM),dR,_(bL,dQ,bN,bz),dS,[_(bw,bV,dT,bM),_(bw,dt,dT,bM),_(bw,dU,dT,bz),_(bw,bV,dT,bz)])),dV,_(s,dW,bU,_(bu,bv,bw,dj)),dX,_(s,dY,bU,_(bu,bv,bw,dt),bY,cc)),dZ,_()));}; 
var b="configuration",c="showPageNotes",d=true,e="showPageNoteNames",f=false,g="showAnnotations",h="showAnnotationsSidebar",i="showConsole",j="linkStyle",k="displayMultipleTargetsOnly",l="linkFlowsToPages",m="linkFlowsToPagesNewWindow",n="useLabels",o="useViews",p="loadFeedbackPlugin",q="sitemap",r="rootNodes",s="id",t="",u="pageName",v="教学",w="type",x="Folder",y="url",z="children",A="zd3lbe",B="Wireframe",C="教学.html",D="14kw3v",E="作业布置",F="作业布置.html",G="mgaoqb",H="创建作业",I="创建作业.html",J="mseroq",K="未完成作业详情",L="未完成作业详情.html",M="jtbvla",N="已完成作业详情",O="已完成作业详情.html",P="c9m5cn",Q="学员练习记录",R="学员练习记录.html",S="真题",T="8k0uj8",U="真题.html",V="ideow9",W="我的作业",X="我的作业.html",Y="additionalJs",Z="plugins/sitemap/sitemap.js",ba="plugins/page_notes/page_notes.js",bb="plugins/debug/debug.js",bc="additionalCss",bd="plugins/sitemap/styles/sitemap.css",be="plugins/page_notes/styles/page_notes.css",bf="plugins/debug/styles/debug.css",bg="globalVariables",bh="onloadvariable",bi="stylesheet",bj="defaultStyle",bk="627587b6038d43cca051c114ac41ad32",bl="fontName",bm="'Arial Normal', 'Arial', sans-serif",bn="fontWeight",bo="400",bp="fontStyle",bq="normal",br="fontStretch",bs="5",bt="foreGroundFill",bu="fillType",bv="solid",bw="color",bx=0xFF333333,by="opacity",bz=1,bA="fontSize",bB="13px",bC="underline",bD="horizontalAlignment",bE="center",bF="lineSpacing",bG="characterSpacing",bH="letterCase",bI="none",bJ="strikethrough",bK="location",bL="x",bM=0,bN="y",bO="size",bP="width",bQ="height",bR="visible",bS="limbo",bT="baseStyle",bU="fill",bV=0xFFFFFFFF,bW="borderFill",bX=0xFF797979,bY="borderWidth",bZ="1",ca="linePattern",cb="cornerRadius",cc="0",cd="verticalAlignment",ce="middle",cf="paddingLeft",cg="2",ch="paddingTop",ci="paddingRight",cj="paddingBottom",ck="stateStyles",cl="image",cm="imageFilter",cn="rotation",co="outerShadow",cp="on",cq="offsetX",cr=5,cs="offsetY",ct="blurRadius",cu="spread",cv="r",cw=0,cx="g",cy="b",cz="a",cA=0.349019607843137,cB="innerShadow",cC="textShadow",cD=0.647058823529412,cE="viewOverride",cF="19e82109f102476f933582835c373474",cG="customStyles",cH="_一级标题",cI="********************************",cJ="32px",cK="bold",cL=0xFFFFFF,cM="left",cN="top",cO="_二级标题",cP="b3a15c9ddde04520be40f94c8168891e",cQ="24px",cR="_三级标题",cS="8c7a4c5ad69a4369a5f7788171ac0b32",cT="18px",cU="_四级标题",cV="e995c891077945c89c0b5fe110d15a0b",cW="14px",cX="_五级标题",cY="386b19ef4be143bd9b6c392ded969f89",cZ="_六级标题",da="fc3b9a13b5574fa098ef0a1db9aac861",db="10px",dc="_文本段落",dd="4988d43d80b44008a4a415096f1632af",de="_表单提示",df="4889d666e8ad4c5e81e59863039a5cc0",dg=0xFF999999,dh="_表单禁用",di="9bd0236217a94d89b0314c8c7fc75f16",dj=0xFFF0F0F0,dk="box_1",dl="********************************",dm="_图片_",dn="adf6e725347a4d01a80614e3b9664247",dp="label",dq="f8c70a63ec8c4ded9e1c2963c0e658a5",dr="placeholder",ds="d47d8120d97741469da4152217605972",dt=0xFFF2F2F2,du="flow_shape",dv="60d87ff5e0934fb5a735f21d2a268c7d",dw="primary_button",dx="c26e509c01924380b00a973a82019677",dy=0xFF169BD5,dz="button",dA="6f1c31a532bc4c9ba00f668fb850196d",dB="line",dC="1df8bc12869c446989a07f36813b37ee",dD="checkbox",dE="********************************",dF="radio_button",dG="4eb5516f311c4bdfa0cb11d7ea75084e",dH="sticky_1",dI="31e8887730cc439f871dc77ac74c53b6",dJ=0xFFFFDF25,dK=0.2,dL="10",dM="_流程形状",dN="df01900e3c4e43f284bafec04b0864c4",dO="linearGradient",dP="startPoint",dQ=0.5,dR="endPoint",dS="stops",dT="offset",dU=0xFFE4E4E4,dV="form_disabled",dW="2829faada5f8449da03773b96e566862",dX="box_2",dY="********************************",dZ="duplicateStyles";
return _creator();
})());