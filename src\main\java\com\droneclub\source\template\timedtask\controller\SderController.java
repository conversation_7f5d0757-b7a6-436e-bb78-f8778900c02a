package com.droneclub.source.template.timedtask.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.timedtask.service.ISderService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/sder")
public class SderController {

    private final ISderService sderService;

    @GetMapping("/initWxfData")
    public RestResult<Object> initWxfData() {
        return RestResult.success(sderService.initWxfData());
    }

    @GetMapping("/transferWxfData")
    public RestResult<Object> transferWxfData(int zjId) {
        return RestResult.success(sderService.transferWxfData(zjId, "多旋翼"));
    }

    @GetMapping("/transferWxfUpdateData")
    public RestResult<Object> transferWxfUpdateData() {
        return RestResult.success(sderService.transferWxfUpdateData());
    }
}
