package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("dc_all_topic_record")
public class DcAllTopicRecord {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String chapterType;
    private String chapterName;
    private Integer topicId;
    private String topicAnswer;
    private String userAnswer;
    private Boolean answerRight;
    /**
     * 记录类型 zj:普通 sx:普通 exam:考试
     */
    private String recordType;
    private Integer userId;
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    private String createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer updateUser;
    private String updateTime;
    private String isDelete;

}
