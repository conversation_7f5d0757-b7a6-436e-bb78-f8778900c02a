package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcFile;
import com.droneclub.source.template.mapper.DcFileMapper;
import com.droneclub.source.template.module.pojo.DcFileListSearch;
import com.droneclub.source.template.module.pojo.DcFileVO;
import com.droneclub.source.template.module.service.IDcFileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcFileServiceImpl implements IDcFileService {

    private final DcFileMapper dcFileMapper;

    @Override
    public ListData<DcFileVO> getDcFileList(DcFileListSearch params) {
        QueryWrapper<DcFile> queryWrapper = new QueryWrapper<>();
        // 查询总数
        Long total = dcFileMapper.selectCount(queryWrapper);

        // 分页查询
        Page<DcFile> page = new Page<>(params.getPageNo(), params.getPageSize());
        IPage<DcFile> dcFilePage = dcFileMapper.selectPage(page, queryWrapper);
        List<DcFile> list = dcFilePage.getRecords();
        List<DcFileVO> listVO = list.stream()
                .map(dcFile -> JSONObject.parseObject(JSONObject.toJSONString(dcFile), DcFileVO.class))
                .collect(Collectors.toList());
        return new ListData<>(listVO, total, params.getPageNo(), params.getPageSize());
    }

    @Override
    public DcFileVO getDcFileById(Integer id) {
        DcFile dcFile = dcFileMapper.selectById(id);
        if (dcFile == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        return JSONObject.parseObject(JSONObject.toJSONString(dcFile), DcFileVO.class);
    }


    @Override
    public DcFile createDcFile(DcFile data) {
        boolean rs = dcFileMapper.insert(data) > 0;
        log.info("创建 DcFile: {}", rs ? "成功" : "失败");
        return data;
    }

    @Override
    public boolean updateDcFile(DcFile data) {
        boolean rs = dcFileMapper.updateById(data) > 0;
        log.info("更新 DcFile: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public boolean deleteDcFileById(Integer id) {
        boolean rs = dcFileMapper.deleteById(id) > 0;
        log.info("删除 DcFile: {}", rs ? "成功" : "失败");
        return rs;
    }


    public void bindFile(String moduleType, String fileType, Integer businessId, List<String> file) {
        if (file != null && file.size() != 0) {
            LambdaQueryWrapper<DcFile> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(DcFile::getFileType, fileType);
            queryWrapper.in(DcFile::getBusinessId, String.valueOf(businessId));
            dcFileMapper.delete(queryWrapper);
            List<DcFile> fileInfos = file.stream()
                    .map(filePath -> DcFile.builder()
                            .moduleType(moduleType)
                            .fileType(fileType)
                            .businessId(String.valueOf(businessId))
                            .filePath(filePath)
                            .build())
                    .collect(Collectors.toList());
            fileInfos.forEach(dcFileMapper::insert);
        } else if (businessId == null) {
            log.info("businessId: {} fileType: {}绑定文件失败.", fileType, businessId);
        }
    }

    @Override
    public List<DcFile> getFileList(String moduleType, String fileType, Integer businessId) {
        LambdaQueryWrapper<DcFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DcFile::getModuleType, moduleType);
        queryWrapper.in(DcFile::getFileType, fileType);
        queryWrapper.in(DcFile::getBusinessId, businessId);
        return dcFileMapper.selectList(queryWrapper);
    }

    @Override
    public List<DcFile> getFileList(String moduleType, Integer businessId) {
        LambdaQueryWrapper<DcFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DcFile::getModuleType, moduleType);
        queryWrapper.in(DcFile::getBusinessId, businessId);
        return dcFileMapper.selectList(queryWrapper);
    }

}
