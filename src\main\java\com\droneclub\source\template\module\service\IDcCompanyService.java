package com.droneclub.source.template.module.service;

import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcCompany;
import com.droneclub.source.template.entity.DcCompanyDiscounts;
import com.droneclub.source.template.module.pojo.DcCompanyListSearch;
import com.droneclub.source.template.module.pojo.DcCompanyVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IDcCompanyService {

    List<DcCompany> getDcCompanyOption(DcCompanyListSearch params);

    ListData<DcCompanyVO> getDcCompanyList(DcCompanyListSearch params);

    DcCompanyVO getDcCompanyById(Integer id);

    DcCompany createDcCompany(DcCompany data);

    boolean updateDcCompany(DcCompany data);

    boolean deleteDcCompanyById(Integer id);

    List<DcCompany> getCompanyList(Set<Integer> companyIds);

    List<DcCompanyDiscounts> getDcCompanyDiscounts(Integer companyId, Set<Integer> discountsIds);


    Map<String, List<DcCompany>> getPxCompanyOption(DcCompanyListSearch params);

    void refreshCompanySystemConfig();
}
