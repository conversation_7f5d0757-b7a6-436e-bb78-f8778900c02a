package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("dc_topic_answer_record")
public class DcTopicAnswerRecord {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer answerId;
    private Integer topicId;
    private String answerResult;
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    private String createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer updateUser;
    private String updateTime;
    private String isDelete;

}
