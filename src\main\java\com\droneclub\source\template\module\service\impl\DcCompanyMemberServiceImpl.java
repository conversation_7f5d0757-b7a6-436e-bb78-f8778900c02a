package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.execption.ZkException;
import cn.soulspark.source.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.common.model.TemplateCurrentUser;
import com.droneclub.source.template.common.utils.TemplateSessionUtils;
import com.droneclub.source.template.entity.DcCompanyMember;
import com.droneclub.source.template.entity.TmRelUserRole;
import com.droneclub.source.template.entity.User;
import com.droneclub.source.template.mapper.DcCompanyMemberMapper;
import com.droneclub.source.template.mapper.TmRelUserRoleMapper;
import com.droneclub.source.template.mapper.TmUserMapper;
import com.droneclub.source.template.module.pojo.DcCompanyMemberSearch;
import com.droneclub.source.template.module.pojo.DcCompanyMemberVO;
import com.droneclub.source.template.module.service.IDcCompanyMemberService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class DcCompanyMemberServiceImpl implements IDcCompanyMemberService {

    private final DcCompanyMemberMapper memberMapper;
    private final TmUserMapper userMapper;
    private final TmRelUserRoleMapper userRoleMapper;

    @Override
    public ListData<DcCompanyMemberVO> getMemberList(DcCompanyMemberSearch search) {
        LambdaQueryWrapper<DcCompanyMember> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(search.getCompanyId() != null, DcCompanyMember::getCompanyId, search.getCompanyId());
        wrapper.like(StringUtils.isValid(search.getMemberName()), DcCompanyMember::getMemberName, search.getMemberName());

        // 添加排序逻辑, 查询管理员ID, 按照先管理员id再其他角色id排序
        Set<Integer> adminUserIds = userRoleMapper.getUserIdByRole(new String[]{"px_org_admin"});
        // 构建排序SQL
        String orderBySql = "";
        if (!adminUserIds.isEmpty()) {
            orderBySql = "CASE WHEN user_id IN (" +
                    adminUserIds.stream()
                            .map(String::valueOf)
                            .collect(Collectors.joining(",")) +
                    ") THEN 0 ELSE 1 END, ";
        }
        orderBySql += "create_time DESC";

        // 使用last方法添加完整的ORDER BY子句
        wrapper.last("ORDER BY " + orderBySql);

        Page<DcCompanyMember> page = memberMapper.selectPage(
                new Page<>(search.getPageNo(), search.getPageSize()),
                wrapper
        );

        List<DcCompanyMemberVO> voList = page.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        ListData<DcCompanyMemberVO> listData = new ListData<>(page.getTotal(), search.getPageNo(), search.getPageSize());
        if (voList.size() == 0) {
            return listData;
        }
        listData.setList(voList);
        // 获取所有用户的权限列表
        Set<Integer> userIds = voList.stream().map(DcCompanyMemberVO::getUserId).collect(Collectors.toSet());
        if (!userIds.isEmpty()) {
            List<TmRelUserRole> roleCodes = userRoleMapper.getUserRoleByUserIds(userIds);
            Map<Integer, List<TmRelUserRole>> userIdRolesMap = roleCodes.stream()
                    .collect(Collectors.groupingBy(TmRelUserRole::getUserId));
            // 获取用户信息
            LambdaQueryWrapper<User> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
            userLambdaQueryWrapper.in(User::getId, userIds);
            List<User> userList = userMapper.selectList(userLambdaQueryWrapper);
            Map<Integer, User> userIdUserMap = userList.stream()
                    .collect(Collectors.toMap(
                            User::getId,  // 使用用户ID作为Map的key
                            user -> user, // 用户对象本身作为value
                            (existing, replacement) -> existing  // 如果key冲突，保留已存在的值
                    ));
            for (DcCompanyMemberVO memberVO : voList) {
                memberVO.setRoles(userIdRolesMap.get(memberVO.getUserId()));
                memberVO.setUser(userIdUserMap.get(memberVO.getUserId()));
            }
        }
        return listData;
    }

    @Override
    public DcCompanyMember createMember(DcCompanyMember member) {
        if (member.getUserId() == null) {
            TemplateCurrentUser currentUser = TemplateSessionUtils.getCurrentUser();
            member.setUserId(currentUser.getId());
        }
        // 根据userId查询, 进行校验
        LambdaQueryWrapper<DcCompanyMember> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DcCompanyMember::getUserId, member.getUserId());
        DcCompanyMember exist = memberMapper.selectOne(wrapper);
        if (exist != null && !member.getCompanyId().equals(exist.getCompanyId())) {
            throw new ZkException("已绑定其他机构, 请先联系管理员解绑");
        }
        if (exist != null && member.getCompanyId().equals(exist.getCompanyId())) {
            throw new ZkException("当前用户已加入机构, 无需重复加入");
        }
        memberMapper.insert(member);
        updateMemberRole(member);
        return member;
    }

    private void updateMemberRole(DcCompanyMember member) {
        if (StringUtils.isInvalid(member.getRoleCodes())) {
            return;
        }
        // 先删除权限, 再批量添加用户角色
        LambdaQueryWrapper<TmRelUserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmRelUserRole::getUserId, member.getUserId());
        wrapper.in(TmRelUserRole::getRoleCode, "px_org_admin", "px_org_yy", "px_org_jy");
        userRoleMapper.delete(wrapper);
        for (String roleCode : member.getRoleCodes().split(",")) {
            userRoleMapper.insert(TmRelUserRole.builder().userId(member.getUserId()).roleCode(roleCode).build());
        }
    }

    @Override
    public Boolean updateMember(DcCompanyMember member) {
        updateMemberRole(member);
        return memberMapper.updateById(member) > 0;
    }

    @Override
    public Boolean deleteMemberById(Integer id) {
        DcCompanyMember dcCompanyMember = memberMapper.selectById(id);
        // 清除之前的角色信息
        if (dcCompanyMember != null) {
            LambdaQueryWrapper<TmRelUserRole> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TmRelUserRole::getUserId, dcCompanyMember.getUserId());
            wrapper.in(TmRelUserRole::getRoleCode, "px_org_admin", "px_org_yy", "px_org_jy");
            userRoleMapper.delete(wrapper);
        }
        return memberMapper.deleteById(id) > 0;
    }

    @Override
    public Boolean checkMemberExists(Integer userId, Integer companyId) {
        LambdaQueryWrapper<DcCompanyMember> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DcCompanyMember::getCompanyId, companyId);
        wrapper.eq(DcCompanyMember::getCreateUser, userId);
        return memberMapper.selectCount(wrapper) > 0;
    }

    private DcCompanyMemberVO convertToVO(DcCompanyMember member) {
        DcCompanyMemberVO vo = new DcCompanyMemberVO();
        BeanUtils.copyProperties(member, vo);

        // 获取公司名称
        /*DcCompany company = companyMapper.selectById(member.getCompanyId());
        if (company != null) {
            vo.setCompanyName(company.getCompanyName());
        }*/

        return vo;
    }
} 