package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcDroneInsuranceEntity;
import com.droneclub.source.template.module.pojo.DcDroneInsuranceDTO;
import com.droneclub.source.template.module.pojo.DcDroneInsuranceQuery;
import com.droneclub.source.template.module.pojo.DcDroneInsuranceVO;
import com.droneclub.source.template.module.service.IDcDroneInsuranceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


/**
 * 无人机投保控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/dcDroneInsurance")
public class DcDroneInsuranceController {

    private final IDcDroneInsuranceService dcDroneInsuranceService;

    /**
     * 分页查询无人机投保列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    @PostMapping("/listDcDroneInsuranceByPage")
    public RestResult<ListData<DcDroneInsuranceVO>> listDcDroneInsuranceByPage(@RequestBody DcDroneInsuranceQuery query) {
        ListData<DcDroneInsuranceVO> pageResult = dcDroneInsuranceService.listDcDroneInsuranceByPage(query);
        return RestResult.success(pageResult);
    }


    /**
     * 根据ID获取无人机投保详情
     *
     * @param id 主键ID
     * @return 无人机投保详情
     */
    @GetMapping("/getDcDroneInsuranceById")
    public RestResult<DcDroneInsuranceVO> getDcDroneInsuranceById(Integer id) {
        DcDroneInsuranceVO vo = dcDroneInsuranceService.getDcDroneInsuranceById(id);
        if (vo == null) {
            return RestResult.failure(RestResult.FAIL.getCode(), "未找到指定的无人机投保");
        }
        return RestResult.success(vo);
    }

    /**
     * 新增无人机投保
     *
     * @param dto 无人机投保信息
     * @return 操作结果
     */
    @PostMapping("/createDcDroneInsurance")
    public RestResult<DcDroneInsuranceEntity> createDcDroneInsurance(@RequestBody @Valid DcDroneInsuranceDTO dto) {
        DcDroneInsuranceEntity entity = dcDroneInsuranceService.createDcDroneInsurance(dto);
        return RestResult.success(entity);
    }

    /**
     * 修改无人机投保
     *
     * @param dto 无人机投保信息
     * @return 操作结果
     */
    @PutMapping("/updateDcDroneInsurance")
    public RestResult<Boolean> updateDcDroneInsurance(@RequestBody DcDroneInsuranceDTO dto) {
        boolean result = dcDroneInsuranceService.updateDcDroneInsurance(dto);
        if (!result) {
            return RestResult.failure(RestResult.FAIL.getCode(), "修改无人机投保失败：未找到指定的无人机投保");
        }
        return RestResult.success(true);
    }

    /**
     * 删除无人机投保
     *
     * @param id 主键ID
     * @return 操作结果
     */
    @DeleteMapping("/deleteDcDroneInsurance")
    public RestResult
            <Boolean> deleteDcDroneInsurance(Integer id) {
        boolean result = dcDroneInsuranceService.deleteDcDroneInsurance(id);
        if (!result) {
            return RestResult.failure(RestResult.FAIL.getCode(), "删除无人机投保失败：未找到指定的无人机投保");
        }
        return RestResult.success(true);
    }

}