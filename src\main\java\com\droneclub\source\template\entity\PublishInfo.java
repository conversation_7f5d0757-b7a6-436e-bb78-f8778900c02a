package com.droneclub.source.template.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("dc_publish_info")
public class PublishInfo {

    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 微信小程序openID
     */
    @TableField(exist = false)
    private String openId;
    /**
     * 信息类型 1:兼职 2:飞手 3: 设备租赁
     */
    private Integer infoType;
    /**
     * 信息状态 1: 待审核 2:审核通过 3:下架
     */
    private Integer infoStatus;
    private String certificateLevel;
    private String uavType;
    private String uavWeight;
    private String totalFlightTime;
    private String extraSkill;
    /**
     * 工作费用
     */
    private String jobExpense;
    /**
     * 期望工作内容
     */
    private String expectJobContent;

    private String workArea;
    private String workPlace;
    private String workStartTime;
    private String workEndTime;
    /**
     * 工作类型: 兼职、全职
     */
    private String jobType;
    private String jobContent;
    private String payMoney;
    private String requireCertificateLevel;
    private String provideFood;
    private String provideRoom;
    private String requireExtraSkill;
    /**
     * 租赁设备信息
     */
    private String deviceType;
    private String deviceDescription;
    private String deviceRent;
    private String devicePlace;
    private String leaseStartTime;
    private String leaseEndTime;

    /**
     * 联系人
     */
    private String contactPerson;
    /**
     * 联系电话
     */
    private String contactPhone;

    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    private String createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer updateUser;
    private String updateTime;
    private String isDelete;
    private String bindUrl;
    /**
     * 简历-照片
     */
    @TableField(exist = false)
    private JSONObject files;

}
