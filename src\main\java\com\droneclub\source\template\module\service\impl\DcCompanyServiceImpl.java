package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import cn.soulspark.source.common.utils.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.droneclub.source.template.common.cache.CompanyCache;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.common.model.TemplateCurrentUser;
import com.droneclub.source.template.common.utils.DateUtils;
import com.droneclub.source.template.common.utils.TemplateSessionUtils;
import com.droneclub.source.template.entity.*;
import com.droneclub.source.template.mapper.*;
import com.droneclub.source.template.module.pojo.*;
import com.droneclub.source.template.module.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcCompanyServiceImpl implements IDcCompanyService {

    private final DcCompanyMapper dcCompanyMapper;
    private final DcCompanyDiscountsMapper dcCompanyDiscountsMapper;
    private final IDcFileService dcFileService;
    private final IDcCompanyProductService companyProductService;
    private final IDcCompanyDiscountsService companyDiscountsService;
    private final IDcCompanyIndustryService dcCompanyIndustryService;
    private final DcUserDiscountCouponMapper dcUserDiscountCouponMapper;
    private final TmUserMapper userMapper;
    private final TmRelUserRoleMapper userRoleMapper;
    private final DcCompanyMemberMapper memberMapper;
    private final DcMemberOpenMapper memberOpenMapper;

    @Override
    public List<DcCompany> getDcCompanyOption(DcCompanyListSearch params) {
        LambdaQueryWrapper<DcCompany> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(DcCompany::getId, DcCompany::getCompanyName);
        queryWrapper.eq(DcCompany::getShowList, 1);
        return dcCompanyMapper.selectList(queryWrapper);
    }

    @Override
    public ListData<DcCompanyVO> getDcCompanyList(DcCompanyListSearch params) {
        long total = dcCompanyMapper.getDcCompanyCount(params);
        params.setOffset((params.getPageNo() - 1) * params.getPageSize());
        List<DcCompany> companies = dcCompanyMapper.getDcCompanyList(params);

        List<DcCompanyVO> listVO = companies.stream()
                .map(dcCompany -> JSONObject.parseObject(JSONObject.toJSONString(dcCompany), DcCompanyVO.class))
                .collect(Collectors.toList());
        Set<Integer> companyIds = listVO.stream().map(DcCompanyVO::getId).collect(Collectors.toSet());
        List<DcCompanyIndustryVO> industryVOS = dcCompanyIndustryService.getDcCompanyIndustryList(DcCompanyIndustryListSearch.builder()
                .companyIds(companyIds).build());
        Map<Integer, List<DcCompanyIndustryVO>> companyIndustryMap = industryVOS.stream().collect(Collectors.groupingBy(DcCompanyIndustryVO::getCompanyId));
        LambdaQueryWrapper<DcMemberOpen> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(DcMemberOpen::getEndTime, DateUtils.getNowDateTime())
                .eq(DcMemberOpen::getStatus, 1);
        List<DcMemberOpen> memberOpens = memberOpenMapper.selectList(queryWrapper);
        Set<Integer> memberCompanyIds = memberOpens.stream().map(DcMemberOpen::getCompanyId).collect(Collectors.toSet());

        for (DcCompanyVO dcCompanyVO : listVO) {
            buildExtraInfo(dcCompanyVO);
            // 绑定
            dcCompanyVO.setIndustries(companyIndustryMap.get(dcCompanyVO.getId()));
            dcCompanyVO.setOpenMemberFlag(memberCompanyIds.contains(dcCompanyVO.getId()));
        }
        return new ListData<>(listVO, total, params.getPageNo(), params.getPageSize());
    }

    private void buildExtraInfo(DcCompanyVO vo) {
        List<DcFile> files = dcFileService.getFileList("sjrz", vo.getId());
        Map<String, List<DcFile>> fileTypeMap = files.stream().collect(Collectors.groupingBy(DcFile::getFileType));
        JSONObject filesJson = new JSONObject();
        for (String fileType : fileTypeMap.keySet()) {
            filesJson.put(fileType, fileTypeMap.get(fileType).stream()
                    .map(DcFile::getFilePath)
                    .collect(Collectors.toList()));
        }
        vo.setFiles(filesJson);
    }

    @Override
    public DcCompanyVO getDcCompanyById(Integer id) {
        DcCompany dcCompany = dcCompanyMapper.selectById(id);
        if (dcCompany == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        List<DcCompanyProductVO> productVOS = companyProductService.getDcCompanyProductList(DcCompanyProductListSearch.builder().companyId(id).build());
        List<DcCompanyDiscountsVO> discountsVOS = companyDiscountsService.getDcCompanyDiscountsList(DcCompanyDiscountsListSearch.builder().companyId(id).build());
        List<DcCompanyIndustryVO> industryList = dcCompanyIndustryService.getDcCompanyIndustryList(DcCompanyIndustryListSearch.builder().companyId(id).build());
        DcCompanyVO companyVO = JSONObject.parseObject(JSONObject.toJSONString(dcCompany), DcCompanyVO.class);
        companyVO.setProducts(productVOS);
        // 优惠信息添加用户是否购买过
        TemplateCurrentUser currentUser = TemplateSessionUtils.getCurrentUser();
        if (currentUser != null && !discountsVOS.isEmpty()) {
            LambdaQueryWrapper<DcUserDiscountCoupon> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DcUserDiscountCoupon::getUserId, currentUser.getId());
            queryWrapper.eq(DcUserDiscountCoupon::getDiscountCouponStatus, 1);
            List<DcUserDiscountCoupon> dcUserDiscountCoupons =dcUserDiscountCouponMapper.selectList(queryWrapper);
            Set<Integer> discountIds = dcUserDiscountCoupons.stream().map(DcUserDiscountCoupon::getDiscountsId).collect(Collectors.toSet());
            discountsVOS.forEach(discountsVO -> discountsVO.setCanBuy(!discountIds.contains(discountsVO.getId())));
        }
        companyVO.setDiscounts(discountsVOS);
        companyVO.setIndustries(industryList);
        buildExtraInfo(companyVO);
        return companyVO;
    }


    @Override
    public DcCompany createDcCompany(DcCompany data) {
        // 如果存在联系人手机号处于待审核或者审核通过的数据则无法再新增
        LambdaQueryWrapper<DcCompany> wrapper = new LambdaQueryWrapper<DcCompany>()
                .eq(DcCompany::getContactWay, data.getContactWay())
                .in(DcCompany::getCompanyStatus, 0, 1, 2);
        List<DcCompany> dcCompany = dcCompanyMapper.selectList(wrapper);
        if (dcCompany != null && !dcCompany.isEmpty()) {
            throw new ZkException(ResultCode.FAIL.getCode(), "当前存在审核中或者审核通过的认证公司, 无法再提交.");
        }

        data.setCompanyStatus(1);
        boolean rs = dcCompanyMapper.insert(data) > 0;
        // 绑定行业
        dcCompanyIndustryService.saveDcCompanyIndustry(data.getId(), data.getIndustry());
        log.info("创建 DcCompany: {}", rs ? "成功" : "失败");
        // 公司信息绑定文件
        bindFile(data);
        // 给会员联系手机号开通会员管理员权限
        // User adminUser = updateUserAdminAuth(data);
        // 给会员联系手机号绑定机构关系 todo: 创建时不绑定
        // bindUserCompany(data, adminUser);
        return data;
    }

    private void bindFile(DcCompany data) {
        if (data.getFiles() == null || data.getFiles().size() == 0) {
            return;
        }
        for (String key : data.getFiles().keySet()) {
            dcFileService.bindFile("sjrz", key, data.getId(),
                    JSONArray.parseArray(data.getFiles().getJSONArray(key).toJSONString(), String.class)
            );
        }
    }

    @Override
    public boolean updateDcCompany(DcCompany data) {
        // 绑定行业
        if (StringUtils.isValid(data.getIndustry())) {
            dcCompanyIndustryService.saveDcCompanyIndustry(data.getId(), data.getIndustry());
        }
        // 更新其他信息
        boolean rs = dcCompanyMapper.updateById(data) > 0;
        log.info("更新 DcCompany: {}", rs ? "成功" : "失败");
        // 公司信息绑定文件
        bindFile(data);
        // 给会员联系手机号开通会员管理员权限
        User adminUser = updateUserAdminAuth(data);
        // 审核通过了, 再进行绑定
        if (data.getCompanyStatus() != null && data.getCompanyStatus() == 2) {
            // 给会员联系手机号绑定机构关系
            bindUserCompany(data, adminUser);
        }
        return rs;
    }

    @Override
    public boolean deleteDcCompanyById(Integer id) {
        boolean rs = dcCompanyMapper.deleteById(id) > 0;
        log.info("删除 DcCompany: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public List<DcCompany> getCompanyList(Set<Integer> companyIds) {
        LambdaQueryWrapper<DcCompany> queryWrapper = new LambdaQueryWrapper<>();
        if (companyIds.size() > 0) {
            queryWrapper.in(DcCompany::getId, companyIds);
            queryWrapper.eq(DcCompany::getShowList, 1);
            return dcCompanyMapper.selectList(queryWrapper);
        }
        return new ArrayList<>();
    }

    @Override
    public List<DcCompanyDiscounts> getDcCompanyDiscounts(Integer companyId, Set<Integer> discountsIds) {
        LambdaQueryWrapper<DcCompanyDiscounts> queryWrapper = new LambdaQueryWrapper<>();
        if (companyId != null) {
            queryWrapper.eq(DcCompanyDiscounts::getCompanyId, companyId);
        }
        if (discountsIds != null && discountsIds.size() > 0) {
            queryWrapper.in(DcCompanyDiscounts::getId, discountsIds);
        }
        return dcCompanyDiscountsMapper.selectList(queryWrapper);
    }

    @Override
    public Map<String, List<DcCompany>> getPxCompanyOption(DcCompanyListSearch params) {
        LambdaQueryWrapper<DcCompany> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isValid(params.getCity())) {
            queryWrapper.eq(DcCompany::getCity, params.getCity());
        }
        List<DcCompany> companies = dcCompanyMapper.selectList(queryWrapper);
        companies.forEach(company -> {
            if (params.getCity() != null && company.getCompanyName() != null) {
                // 1. 同时替换 "北京市" 和 "北京" 两种格式
                String cityWithSuffix = params.getCity();          // 例如 "北京市"
                String cityWithoutSuffix = cityWithSuffix.replace("市", ""); // 例如 "北京"

                // 2. 一次性替换所有可能的城市名格式
                String newCompanyName = company.getCompanyName()
                        .replace(cityWithSuffix, "")
                        .replace(cityWithoutSuffix, "")
                        .trim(); // 移除多余空格

                company.setCompanyName(newCompanyName);
            }
        });
        // 按首字母分组
        return companies.stream()
                .filter(company -> company.getFirstChar() != null) // 过滤掉 firstChar 为 null 的数据
                .collect(Collectors.groupingBy(
                        DcCompany::getFirstChar,
                        TreeMap::new,  // 使用 TreeMap 自动按键的字典顺序排序
                        Collectors.toList()
                ));
    }

    @Override
    public void refreshCompanySystemConfig() {
        LambdaQueryWrapper<DcCompany> companyLambdaQueryWrapper = new LambdaQueryWrapper<>();
        companyLambdaQueryWrapper.isNotNull(DcCompany::getCompanyCode);
        List<DcCompany> companies = dcCompanyMapper.selectList(companyLambdaQueryWrapper);
        companies.forEach(company -> {
                    CompanyCache.COMPANY_MAP.put(company.getCompanyCode(), company);
                    log.info("加载公司配置: {} {}", company.getCompanyCode(), company.getCompanyName());
                }
        );
    }

    private void bindUserCompany(DcCompany data, User adminUser) {
        if (adminUser != null) {
            LambdaQueryWrapper<DcCompanyMember> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DcCompanyMember::getUserId, adminUser.getId());
            DcCompanyMember exist = memberMapper.selectOne(wrapper);
            if (exist != null && !data.getId().equals(exist.getCompanyId())) {
                throw new ZkException("已绑定其他机构, 请先联系管理员解绑");
            }
            if (exist == null) {
                memberMapper.insert(DcCompanyMember.builder()
                        .companyId(data.getId())
                        .userId(adminUser.getId())
                        .memberName(data.getLinkman())
                        .build());
            }
        }
    }

    private User updateUserAdminAuth(DcCompany data) {
        if (data.getOpenAdminFlag() != null && data.getOpenAdminFlag() == 1) {
            // 根据管理员手机号搜索用户信息, 之后添加用户角色
            LambdaQueryWrapper<User> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
            userLambdaQueryWrapper.eq(User::getAccount, data.getContactWay());
            User adminUser = userMapper.selectOne(userLambdaQueryWrapper);
            if (adminUser == null) {
                throw new ZkException("管理员账号未注册, 请先联系管理员账号注册登录.");
            }
            // 检查用户是否已经拥有该角色
            LambdaQueryWrapper<TmRelUserRole> roleCheckWrapper = new LambdaQueryWrapper<>();
            roleCheckWrapper.eq(TmRelUserRole::getUserId, adminUser.getId())
                    .eq(TmRelUserRole::getRoleCode, "px_org_admin");
            long count = userRoleMapper.selectCount(roleCheckWrapper);

            // 如果用户没有该角色，则添加
            if (count == 0) {
                userRoleMapper.insert(TmRelUserRole.builder()
                        .userId(adminUser.getId())
                        .roleCode("px_org_admin")
                        .build());
            }
            return adminUser;
        }
        return null;
    }


}
