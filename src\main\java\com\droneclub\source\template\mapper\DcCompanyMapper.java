package com.droneclub.source.template.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.droneclub.source.template.entity.DcCompany;
import com.droneclub.source.template.module.pojo.DcCompanyListSearch;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DcCompanyMapper extends BaseMapper<DcCompany> {

    boolean addLookNum(@Param("companyId") Integer companyId);

    long getDcCompanyCount(DcCompanyListSearch params);

    List<DcCompany> getDcCompanyList(DcCompanyListSearch params);
}
