package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.execption.ZkException;
import com.droneclub.source.template.module.service.IDcCdKeyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.Base64;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/dkKey")
public class DcCdKeyController {

    @Value("${downloadResource.tkPdf}")
    private String tkPdfFilePath;

    private final IDcCdKeyService dcCdKeyService;

    @GetMapping("/downloadTkFile")
    public ResponseEntity<Resource> downloadTkFile(
            String cdKey,
            @RequestHeader("User-Agent") String userAgent,
            HttpServletRequest request) {
        try {
            log.info("激活码下载请求，激活码：{}, 访问设备信息: {}", cdKey, userAgent);
            // 1. 激活码校验
            dcCdKeyService.checkCdKey(cdKey, userAgent);

            // 2. 获取文件资源
            Path filePath = Paths.get(tkPdfFilePath).normalize();
            Resource resource = new UrlResource(filePath.toUri());

            // 3. 校验文件是否存在
            if (!resource.exists() || !resource.isReadable()) {
                return ResponseEntity.notFound().build();
            }

            // 4. 动态生成文件名（示例：瑞鹰无人机题库-2023版.pdf）
            String displayFileName = "瑞鹰无人机题库-" + LocalDate.now().getYear() + "版.pdf";

            // 5. 智能文件名编码（兼容各浏览器）
            String encodedFileName;
            if (request.getHeader("User-Agent").contains("Firefox")) {
                // Firefox特殊处理
                encodedFileName = "=?UTF-8?B?" +
                        new String(Base64.getEncoder().encode(displayFileName.getBytes(StandardCharsets.UTF_8))) +
                        "?=";
            } else {
                // 其他浏览器采用URL编码
                encodedFileName = URLEncoder.encode(displayFileName, String.valueOf(StandardCharsets.UTF_8))
                        .replaceAll("\\+", "%20");
            }

            // 6. 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION,
                    "attachment; filename=\"" + encodedFileName + "\"");
            headers.add(HttpHeaders.CONTENT_TYPE, "application/pdf");
            headers.add(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
            headers.add(HttpHeaders.PRAGMA, "no-cache");
            headers.add(HttpHeaders.EXPIRES, "0");

            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(resource.contentLength())
                    .body(resource);

        } catch (ZkException ze) {
            throw new ZkException(ze.getMsg());
        } catch (Exception e) {
            throw new ZkException("题库下载失败，请联系瑞鹰无人机客服");
        }
    }

}
