package com.droneclub.source.template.module.service;

import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcDroneInsuranceEntity;
import com.droneclub.source.template.module.pojo.DcDroneInsuranceDTO;
import com.droneclub.source.template.module.pojo.DcDroneInsuranceQuery;
import com.droneclub.source.template.module.pojo.DcDroneInsuranceVO;

/**
 * 无人机投保服务接口
 */
public interface IDcDroneInsuranceService {

    /**
     * 分页查询无人机投保列表
     */
    ListData<DcDroneInsuranceVO> listDcDroneInsuranceByPage(DcDroneInsuranceQuery query);


    /**
     * 根据ID获取无人机投保详情
     */
    DcDroneInsuranceVO getDcDroneInsuranceById(Integer id);

    /**
     * 保存无人机投保
     */
    DcDroneInsuranceEntity createDcDroneInsurance(DcDroneInsuranceDTO dto);

    /**
     * 更新无人机投保
     */
    boolean updateDcDroneInsurance(DcDroneInsuranceDTO dto);

    /**
     * 删除无人机投保
     */
    boolean deleteDcDroneInsurance(Integer id);

}