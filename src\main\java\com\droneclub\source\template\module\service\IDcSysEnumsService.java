package com.droneclub.source.template.module.service;

import com.droneclub.source.template.entity.DcSysEnums;
import com.droneclub.source.template.module.pojo.DcSysEnumsListSearch;
import com.droneclub.source.template.module.pojo.DcSysEnumsVO;

import java.util.List;

public interface IDcSysEnumsService {

    List<DcSysEnums> getDcSysEnumsList(DcSysEnumsListSearch params);

    DcSysEnumsVO getDcSysEnumsById(Integer id);

    DcSysEnums createDcSysEnums(DcSysEnums data);

    boolean updateDcSysEnums(DcSysEnums data);

    boolean deleteDcSysEnumsById(Integer id);

    Object getMemberPlan();
}
