package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import cn.soulspark.source.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.droneclub.source.template.common.email.EmailConfig;
import com.droneclub.source.template.common.email.EmailSender;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.common.utils.ResourceUtils;
import com.droneclub.source.template.entity.DcTrainingApply;
import com.droneclub.source.template.mapper.DcTrainingApplyMapper;
import com.droneclub.source.template.module.pojo.DcTrainingApplyListSearch;
import com.droneclub.source.template.module.pojo.DcTrainingApplyVO;
import com.droneclub.source.template.module.service.IDcTrainingApplyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcTrainingApplyServiceImpl implements IDcTrainingApplyService {

    private final DcTrainingApplyMapper dcTrainingApplyMapper;
    private final EmailSender emailSender;
    private final EmailConfig emailConfig;

    @Override
    public ListData<DcTrainingApplyVO> getDcTrainingApplyList(DcTrainingApplyListSearch params) {
        QueryWrapper<DcTrainingApply> queryWrapper = new QueryWrapper<>();
        // 查询总数
        Long total = dcTrainingApplyMapper.selectCount(queryWrapper);

        // 分页查询
        Page<DcTrainingApply> page = new Page<>(params.getPageNo(), params.getPageSize());
        IPage<DcTrainingApply> dcTrainingApplyPage = dcTrainingApplyMapper.selectPage(page, queryWrapper);
        List<DcTrainingApply> list = dcTrainingApplyPage.getRecords();
        List<DcTrainingApplyVO> listVO = list.stream()
                .map(dcTrainingApply -> JSONObject.parseObject(JSONObject.toJSONString(dcTrainingApply), DcTrainingApplyVO.class))
                .collect(Collectors.toList());
        return new ListData<>(listVO, total, params.getPageNo(), params.getPageSize());
    }

    @Override
    public DcTrainingApplyVO getDcTrainingApplyById(Integer id) {
        DcTrainingApply dcTrainingApply = dcTrainingApplyMapper.selectById(id);
        if (dcTrainingApply == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        return JSONObject.parseObject(JSONObject.toJSONString(dcTrainingApply), DcTrainingApplyVO.class);
    }


    @Override
    public DcTrainingApply createDcTrainingApply(DcTrainingApply data) {
        boolean rs = dcTrainingApplyMapper.insert(data) > 0;
        log.info("创建 DcTrainingApply: {}", rs ? "成功" : "失败");
        if (StringUtils.isValid(data.getName()) && StringUtils.isValid(data.getTrainingType())) {
            String emailTemplate = ResourceUtils.loadResource("emailTemplate/peixun-notice.html");
            if (emailTemplate != null) {
                emailTemplate = emailTemplate.replaceAll("#\\{name}", data.getName());
                emailTemplate = emailTemplate.replaceAll("#\\{type}", data.getTrainingType());
                String subject = String.format("新的培训申请-%s", data.getName());
                emailSender.createMessage(emailConfig.getReceiver())
                        .subject(subject)
                        .htmlContent(emailTemplate)
                        .send();
                log.info("邮件通知成功: {}", subject);
            }
        }
        return data;
    }

    @Override
    public boolean updateDcTrainingApply(DcTrainingApply data) {
        boolean rs = dcTrainingApplyMapper.updateById(data) > 0;
        log.info("更新 DcTrainingApply: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public boolean deleteDcTrainingApplyById(Integer id) {
        boolean rs = dcTrainingApplyMapper.deleteById(id) > 0;
        log.info("删除 DcTrainingApply: {}", rs ? "成功" : "失败");
        return rs;
    }
}
