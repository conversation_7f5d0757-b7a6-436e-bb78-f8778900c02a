package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("sf_wrj_question_1")
public class SfWrjQuestion {

    @TableId
    private Integer tiId;              // 题目 ID
    private String question;       // 题目内容
    private int chapId;            // 章节 ID
    private String analysis;       // 解析
    private String chapName;       // 章节名称
    private Date updateTime;       // 更新时间
    private String answer;         // 答案 (选项 A, B, C, D)
    private int creatorId;         // 创建者 ID
    private int status;            // 状态 (1-正常, 2-已删除等)
    private String optList;        // 选项列表 (以 JSON 字符串形式存储)
    private Date myUpdateTime;       // 更新时间


}
