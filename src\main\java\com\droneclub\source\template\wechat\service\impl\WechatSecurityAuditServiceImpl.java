package com.droneclub.source.template.wechat.service.impl;

import cn.soulspark.source.common.enums.HttpMethod;
import cn.soulspark.source.common.enums.MediaTypes;
import cn.soulspark.source.common.execption.ZkException;
import cn.soulspark.source.common.http.HttpToolkit;
import cn.soulspark.source.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.droneclub.source.template.common.config.WechatConfig;
import com.droneclub.source.template.wechat.service.IWechatAuthService;
import com.droneclub.source.template.wechat.service.IWechatSecurityAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class WechatSecurityAuditServiceImpl implements IWechatSecurityAuditService {

    private final IWechatAuthService authService;

    @Override
    public Object contentAudit(String openId, String content, String userName) {
        if (StringUtils.isInvalid(openId)) {
            return true;
        }
        Map<String, String> params = new HashMap<>();
        if (StringUtils.isValid(content)) {
            params.put("content", content);
        }
        if (StringUtils.isValid(userName)) {
            params.put("nickname", userName);
        }
        params.put("version", "2");
        params.put("scene", "1");
        params.put("openid", openId);
        JSONObject json;
        String suggest = "";
        try {
            log.info("请求微信 内容审核 url: {} 参数: {}", WechatConfig.config.getContentAuditUrl(), params);
            json = HttpToolkit.request(WechatConfig.config.getContentAuditUrl() +
                            "?access_token=" + authService.getAccessToken(), HttpMethod.POST)
                    .addRaw(JSONObject.toJSONString(params))
                    .rawRequest(MediaTypes.APPLICATION_JSON)
                    .sync()
                    .getJSONObject();
            log.info("微信 内容审核 原始返回: {}", json);
            suggest = json.getJSONObject("result").getString("suggest");
        } catch (Exception e) {
            log.error("请求微信 内容审核 异常", e);
        }
        if (!"pass".equals(suggest)) {
            throw new ZkException("您发布的信息涉及敏感信息，请修改后发布;");
        }
        return true;
    }

    @Override
    public Object imageAudit(String openId, String imageUrl) {
        if (StringUtils.isInvalid(openId)) {
            return true;
        }
        Map<String, String> params = new HashMap<>();
        params.put("media_url", imageUrl);
        params.put("media_type", "2");
        params.put("version", "2");
        params.put("scene", "1");
        params.put("openid", openId);
        JSONObject json;
        String suggest = "";
        try {
            log.info("请求微信 图片审核 url: {} 参数: {}", WechatConfig.config.getMediaAuditUrl(), params);
            json = HttpToolkit.request(WechatConfig.config.getMediaAuditUrl() +
                            "?access_token=" + authService.getAccessToken(), HttpMethod.POST)
                    .addRaw(JSONObject.toJSONString(params))
                    .rawRequest(MediaTypes.APPLICATION_JSON)
                    .sync()
                    .getJSONObject();
            suggest = json.getJSONObject("result").getString("suggest");
        } catch (Exception e) {
            log.error("请求微信 内容审核 异常", e);
        }
        if (!"pass".equals(suggest)) {
            throw new ZkException("您发布的图片涉及敏感信息，请修改后发布;");
        }
        return true;
    }
}
