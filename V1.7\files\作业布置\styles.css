﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-63px;
  width:992px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u35_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:758px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u35 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:33px;
  width:375px;
  height:758px;
  display:flex;
}
#u35 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u35_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u36_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:45px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u36 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:33px;
  width:375px;
  height:45px;
  display:flex;
}
#u36 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u36_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u37_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u37 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:48px;
  width:56px;
  height:16px;
  display:flex;
}
#u37 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u37_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u38_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u38 {
  border-width:0px;
  position:absolute;
  left:75px;
  top:46px;
  width:20px;
  height:20px;
  display:flex;
}
#u38 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u38_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u39_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u39 {
  border-width:0px;
  position:absolute;
  left:135px;
  top:84px;
  width:42px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u39 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u39_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u40_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:286px;
  height:42px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:70px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u40 {
  border-width:0px;
  position:absolute;
  left:105px;
  top:735px;
  width:286px;
  height:42px;
  display:flex;
}
#u40 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u40_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u41_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:376px;
  height:2px;
}
#u41 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:105px;
  width:375px;
  height:1px;
  display:flex;
}
#u41 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u41_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u42_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u42 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:83px;
  width:42px;
  height:16px;
  display:flex;
}
#u42 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u42_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u43_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:45px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-top:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u43 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:106px;
  width:375px;
  height:45px;
  display:flex;
}
#u43 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u43_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u44_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:33px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:7px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u44 {
  border-width:0px;
  position:absolute;
  left:75px;
  top:112px;
  width:308px;
  height:33px;
  display:flex;
}
#u44 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u44_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u45_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u45 {
  border-width:0px;
  position:absolute;
  left:398px;
  top:121px;
  width:28px;
  height:16px;
  display:flex;
}
#u45 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u45_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u46_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u46 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:121px;
  width:98px;
  height:16px;
  display:flex;
}
#u46 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u46_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u47_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:166px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u47 {
  border-width:0px;
  position:absolute;
  left:75px;
  top:159px;
  width:351px;
  height:166px;
  display:flex;
}
#u47 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u47_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u48_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u48 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:171px;
  width:50px;
  height:50px;
  display:flex;
}
#u48 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u48_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u49_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u49 {
  border-width:0px;
  position:absolute;
  left:145px;
  top:173px;
  width:84px;
  height:16px;
  display:flex;
}
#u49 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u49_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u50_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u50 {
  border-width:0px;
  position:absolute;
  left:145px;
  top:205px;
  width:96px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u50 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u50_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u51_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:2px;
}
#u51 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:228px;
  width:327px;
  height:1px;
  display:flex;
}
#u51 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u51_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u52_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u52 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:244px;
  width:56px;
  height:16px;
  display:flex;
}
#u52 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u52_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u53_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u53 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:244px;
  width:56px;
  height:16px;
  display:flex;
}
#u53 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u53_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u54_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:23px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:34px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u54 {
  border-width:0px;
  position:absolute;
  left:339px;
  top:185px;
  width:69px;
  height:23px;
  display:flex;
}
#u54 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u54_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u55_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u55 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:173px;
  width:47px;
  height:16px;
  display:flex;
  font-size:12px;
}
#u55 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u55_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u56_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u56 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:299px;
  width:98px;
  height:16px;
  display:flex;
}
#u56 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u56_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u57_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u57 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:299px;
  width:72px;
  height:16px;
  display:flex;
}
#u57 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u57_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u58_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u58 {
  border-width:0px;
  position:absolute;
  left:394px;
  top:268px;
  width:20px;
  height:20px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u58 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u58_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u59_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:162px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u59 {
  border-width:0px;
  position:absolute;
  left:75px;
  top:335px;
  width:351px;
  height:162px;
  display:flex;
}
#u59 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u59_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u60_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u60 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:347px;
  width:50px;
  height:50px;
  display:flex;
}
#u60 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u60_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u61_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u61 {
  border-width:0px;
  position:absolute;
  left:145px;
  top:349px;
  width:84px;
  height:16px;
  display:flex;
}
#u61 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u61_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u62_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u62 {
  border-width:0px;
  position:absolute;
  left:145px;
  top:381px;
  width:96px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u62 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u62_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u63_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:2px;
}
#u63 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:404px;
  width:327px;
  height:1px;
  display:flex;
}
#u63 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u63_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u64_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u64 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:420px;
  width:56px;
  height:16px;
  display:flex;
}
#u64 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u64_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u65_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u65 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:420px;
  width:56px;
  height:16px;
  display:flex;
}
#u65 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u65_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u66_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u66 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:349px;
  width:47px;
  height:16px;
  display:flex;
  font-size:12px;
}
#u66 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u66_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u67_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u67 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:474px;
  width:98px;
  height:16px;
  display:flex;
}
#u67 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u67_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u68_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u68 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:474px;
  width:72px;
  height:16px;
  display:flex;
}
#u68 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u68_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u69_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u69 {
  border-width:0px;
  position:absolute;
  left:394px;
  top:428px;
  width:20px;
  height:20px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u69 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u69_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u70_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:159px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u70 {
  border-width:0px;
  position:absolute;
  left:75px;
  top:507px;
  width:351px;
  height:159px;
  display:flex;
}
#u70 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u70_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u71_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u71 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:519px;
  width:50px;
  height:50px;
  display:flex;
}
#u71 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u71_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u72_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u72 {
  border-width:0px;
  position:absolute;
  left:145px;
  top:521px;
  width:84px;
  height:16px;
  display:flex;
}
#u72 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u72_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u73_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u73 {
  border-width:0px;
  position:absolute;
  left:145px;
  top:553px;
  width:96px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u73 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u73_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u74_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:2px;
}
#u74 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:576px;
  width:327px;
  height:1px;
  display:flex;
}
#u74 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u74_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u75_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u75 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:592px;
  width:56px;
  height:16px;
  display:flex;
}
#u75 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u75_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u76_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u76 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:592px;
  width:56px;
  height:16px;
  display:flex;
}
#u76 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u76_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u77_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u77 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:521px;
  width:47px;
  height:16px;
  display:flex;
  font-size:12px;
}
#u77 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u77_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u78_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u78 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:640px;
  width:98px;
  height:16px;
  display:flex;
}
#u78 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u78_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u79_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u79 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:640px;
  width:72px;
  height:16px;
  display:flex;
}
#u79 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u79_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u80_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u80 {
  border-width:0px;
  position:absolute;
  left:394px;
  top:608px;
  width:20px;
  height:20px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u80 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u80_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u81_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u81 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:272px;
  width:56px;
  height:16px;
  display:flex;
}
#u81 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u81_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u82_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u82 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:272px;
  width:128px;
  height:16px;
  display:flex;
}
#u82 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u82_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u83_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:758px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u83 {
  border-width:0px;
  position:absolute;
  left:524px;
  top:33px;
  width:375px;
  height:758px;
  display:flex;
}
#u83 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u83_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u84_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:45px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u84 {
  border-width:0px;
  position:absolute;
  left:524px;
  top:33px;
  width:375px;
  height:45px;
  display:flex;
}
#u84 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u84_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u85_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u85 {
  border-width:0px;
  position:absolute;
  left:681px;
  top:48px;
  width:56px;
  height:16px;
  display:flex;
}
#u85 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u85_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u86_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u86 {
  border-width:0px;
  position:absolute;
  left:536px;
  top:46px;
  width:20px;
  height:20px;
  display:flex;
}
#u86 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u86_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u87_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u87 {
  border-width:0px;
  position:absolute;
  left:596px;
  top:84px;
  width:42px;
  height:16px;
  display:flex;
}
#u87 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u87_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u88_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:286px;
  height:42px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:70px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u88 {
  border-width:0px;
  position:absolute;
  left:566px;
  top:742px;
  width:286px;
  height:42px;
  display:flex;
}
#u88 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u88_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u89_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:376px;
  height:2px;
}
#u89 {
  border-width:0px;
  position:absolute;
  left:524px;
  top:105px;
  width:375px;
  height:1px;
  display:flex;
}
#u89 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u89_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u90_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u90 {
  border-width:0px;
  position:absolute;
  left:793px;
  top:83px;
  width:42px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u90 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u90_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u91_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:45px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-top:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u91 {
  border-width:0px;
  position:absolute;
  left:524px;
  top:106px;
  width:375px;
  height:45px;
  display:flex;
}
#u91 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u91_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u92_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:33px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:7px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u92 {
  border-width:0px;
  position:absolute;
  left:536px;
  top:112px;
  width:308px;
  height:33px;
  display:flex;
}
#u92 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u92_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u93_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u93 {
  border-width:0px;
  position:absolute;
  left:859px;
  top:121px;
  width:28px;
  height:16px;
  display:flex;
}
#u93 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u93_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u94_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u94 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:121px;
  width:98px;
  height:16px;
  display:flex;
}
#u94 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u94_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u95_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:190px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u95 {
  border-width:0px;
  position:absolute;
  left:536px;
  top:159px;
  width:351px;
  height:190px;
  display:flex;
}
#u95 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u95_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u96_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u96 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:171px;
  width:50px;
  height:50px;
  display:flex;
}
#u96 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u96_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u97_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u97 {
  border-width:0px;
  position:absolute;
  left:606px;
  top:173px;
  width:84px;
  height:16px;
  display:flex;
}
#u97 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u97_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u98_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u98 {
  border-width:0px;
  position:absolute;
  left:606px;
  top:205px;
  width:96px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u98 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u98_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u99_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:2px;
}
#u99 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:228px;
  width:327px;
  height:1px;
  display:flex;
}
#u99 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u99_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u100_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u100 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:244px;
  width:56px;
  height:16px;
  display:flex;
}
#u100 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u100_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u101_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u101 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:244px;
  width:56px;
  height:16px;
  display:flex;
}
#u101 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u101_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u102_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u102 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:299px;
  width:98px;
  height:16px;
  display:flex;
}
#u102 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u102_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u103_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u103 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:299px;
  width:72px;
  height:16px;
  display:flex;
}
#u103 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u103_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u104_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u104 {
  border-width:0px;
  position:absolute;
  left:853px;
  top:280px;
  width:20px;
  height:20px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u104 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u104_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u105_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:186px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u105 {
  border-width:0px;
  position:absolute;
  left:536px;
  top:359px;
  width:351px;
  height:186px;
  display:flex;
}
#u105 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u105_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u106_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u106 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:371px;
  width:50px;
  height:50px;
  display:flex;
}
#u106 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u106_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u107_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u107 {
  border-width:0px;
  position:absolute;
  left:606px;
  top:373px;
  width:84px;
  height:16px;
  display:flex;
}
#u107 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u107_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u108_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u108 {
  border-width:0px;
  position:absolute;
  left:606px;
  top:405px;
  width:96px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u108 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u108_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u109_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:2px;
}
#u109 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:428px;
  width:327px;
  height:1px;
  display:flex;
}
#u109 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u109_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u110_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u110 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:444px;
  width:56px;
  height:16px;
  display:flex;
}
#u110 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u110_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u111_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u111 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:444px;
  width:56px;
  height:16px;
  display:flex;
}
#u111 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u111_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u112_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u112 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:496px;
  width:98px;
  height:16px;
  display:flex;
}
#u112 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u112_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u113_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u113 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:497px;
  width:72px;
  height:16px;
  display:flex;
}
#u113 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u113_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u114_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u114 {
  border-width:0px;
  position:absolute;
  left:855px;
  top:480px;
  width:20px;
  height:20px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u114 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u114_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u115_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:180px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u115 {
  border-width:0px;
  position:absolute;
  left:536px;
  top:555px;
  width:351px;
  height:180px;
  display:flex;
}
#u115 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u115_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u116_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u116 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:567px;
  width:50px;
  height:50px;
  display:flex;
}
#u116 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u116_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u117_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u117 {
  border-width:0px;
  position:absolute;
  left:606px;
  top:569px;
  width:84px;
  height:16px;
  display:flex;
}
#u117 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u117_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u118_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u118 {
  border-width:0px;
  position:absolute;
  left:606px;
  top:601px;
  width:96px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u118 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u118_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u119_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:2px;
}
#u119 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:624px;
  width:327px;
  height:1px;
  display:flex;
}
#u119 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u119_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u120_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u120 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:640px;
  width:56px;
  height:16px;
  display:flex;
}
#u120 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u120_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u121_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u121 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:640px;
  width:56px;
  height:16px;
  display:flex;
}
#u121 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u121_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u122_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u122 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:685px;
  width:98px;
  height:16px;
  display:flex;
}
#u122 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u122_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u123_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u123 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:685px;
  width:72px;
  height:16px;
  display:flex;
}
#u123 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u123_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u124_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u124 {
  border-width:0px;
  position:absolute;
  left:855px;
  top:664px;
  width:20px;
  height:20px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u124 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u124_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u125_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u125 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:521px;
  width:84px;
  height:16px;
  display:flex;
}
#u125 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u125_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u126_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u126 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:521px;
  width:72px;
  height:16px;
  display:flex;
}
#u126 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u126_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u127_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u127 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:711px;
  width:84px;
  height:16px;
  display:flex;
}
#u127 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u127_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u128_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u128 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:711px;
  width:72px;
  height:16px;
  display:flex;
}
#u128 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u128_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u129_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u129 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:325px;
  width:84px;
  height:16px;
  display:flex;
}
#u129 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u129_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u130_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u130 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:325px;
  width:72px;
  height:16px;
  display:flex;
}
#u130 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u130_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u131_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u131 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:444px;
  width:56px;
  height:16px;
  display:flex;
}
#u131 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u131_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u132_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u132 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:444px;
  width:128px;
  height:16px;
  display:flex;
}
#u132 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u132_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u133_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u133 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:272px;
  width:56px;
  height:16px;
  display:flex;
}
#u133 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u133_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u134_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u134 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:272px;
  width:128px;
  height:16px;
  display:flex;
}
#u134 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u134_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u135_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u135 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:469px;
  width:56px;
  height:16px;
  display:flex;
}
#u135 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u135_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u136_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u136 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:469px;
  width:128px;
  height:16px;
  display:flex;
}
#u136 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u136_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u137_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:992px;
  height:494px;
  background:inherit;
  background-color:rgba(255, 223, 37, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  box-shadow:5px 5px 5px rgba(0, 0, 0, 0.2);
  font-size:16px;
}
#u137 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:818px;
  width:992px;
  height:494px;
  display:flex;
  font-size:16px;
}
#u137 .text {
  position:absolute;
  align-self:flex-start;
  padding:10px 10px 10px 10px;
  box-sizing:border-box;
  width:100%;
}
#u137_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u138_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u138 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:617px;
  width:56px;
  height:16px;
  display:flex;
}
#u138 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u138_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u139_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u139 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:617px;
  width:42px;
  height:16px;
  display:flex;
}
#u139 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u139_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u140_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u140 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:664px;
  width:56px;
  height:16px;
  display:flex;
}
#u140 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u140_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u141_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u141 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:664px;
  width:42px;
  height:16px;
  display:flex;
}
#u141 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u141_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
