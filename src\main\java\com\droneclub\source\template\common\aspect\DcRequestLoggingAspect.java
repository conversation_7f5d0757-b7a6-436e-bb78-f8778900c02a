package com.droneclub.source.template.common.aspect;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

@Slf4j
@Aspect
@Component
public class DcRequestLoggingAspect {

    @Pointcut("execution(public * com.droneclub.source.template.*.controller.*.*(..))")
    public void controllerMethods() {
    }

    @Around("controllerMethods()")
    public Object logAroundController(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = joinPoint.proceed();
        long endTime = System.currentTimeMillis();

        log.info("\n接口: {}\n请求参数: {}\n响应结果: {}\n接口耗时: {} ms",
                joinPoint.getSignature().toShortString(),
                joinPoint.getArgs(),
                JSONObject.toJSONString(result),
                endTime - startTime);

        return result;
    }

}