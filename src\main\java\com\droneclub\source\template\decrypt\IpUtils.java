package com.droneclub.source.template.decrypt;

import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

public class IpUtils {

    private static final List<String> IP_HEADER_CANDIDATES = Arrays.asList(
            "X-Forwarded-For",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR",
            "X-Real-IP"
    );

    public static String getClientIP(ServerHttpRequest request) {
        HttpHeaders headers = request.getHeaders();

        for (String header : IP_HEADER_CANDIDATES) {
            String ip = headers.getFirst(header);
            if (StringUtils.hasLength(ip) && !"unknown".equalsIgnoreCase(ip)) {
                return extractClientIp(ip);
            }
        }

        return Objects.requireNonNull(request.getRemoteAddress()).getAddress().getHostAddress();
    }

    private static String extractClientIp(String ip) {
        // 如果通过多个代理，则第一个IP为客户端真实IP，多个IP按','分割
        if (ip.contains(",")) {
            ip = ip.split(",")[0];
        }
        return ip.trim();
    }
}
