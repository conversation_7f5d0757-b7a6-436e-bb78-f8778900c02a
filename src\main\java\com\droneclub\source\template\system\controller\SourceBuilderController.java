package com.droneclub.source.template.system.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.system.service.IAuthManegeService;
import com.droneclub.source.template.system.service.ISourceBuilderService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/source")
public class SourceBuilderController {

    private final ISourceBuilderService sourceBuilderService;
    private final IAuthManegeService iAuthManegeService;


    @GetMapping("/builder")
    public Object sourceBuilder(boolean replace, String tableName, String mode) {
        return sourceBuilderService.sourceBuilder(replace, tableName, mode);
    }

    @GetMapping("/rebuildAuthData")
    public RestResult<Object> rebuildAuthData() {
        return RestResult.success(iAuthManegeService.rebuildAuthData());
    }

}
