select temp.*, dpi.*
from dc_publish_info dpi

         left join (SELECT business_id,
                           COUNT(*) AS operation_count
                    FROM dc_operation_record
                    WHERE operation_type = 'look_publish_info'
                      AND create_time >= NOW() - INTERVAL 1 month
                      and business_id in (select id from dc_publish_info dpi where info_type=1)
                    GROUP BY
                        business_id
                    ORDER BY
                        operation_count DESC
                        LIMIT 20) temp on temp.business_id = dpi.id
where temp.business_id is not null
order by temp.operation_count DESC