package com.droneclub.source.template.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.droneclub.source.template.entity.DcAllTopicRecord;
import com.droneclub.source.template.entity.DcTopic;
import com.droneclub.source.template.module.pojo.DcTopicVO;
import com.droneclub.source.template.module.pojo.QuestionBreakdown;
import com.droneclub.source.template.module.pojo.TopicChapterItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface DcTopicMapper extends BaseMapper<DcTopic> {
    /**
     * 获取题目章节计数
     */
    @Select("SELECT chapter_name as chapterName, COUNT(*) AS topicTotal\n" +
            "        FROM dc_topic\n" +
            "        WHERE is_delete = 0 and topic_status=1 AND chapter_type = #{chapterType}\n" +
            "        GROUP BY chapter_name")
    List<TopicChapterItem> getTopicChapterCountByChapterType(String chapterType);

    /**
     * 获取用户各章节最新一条有效的题目Id
     */
    @Select("SELECT t1.*\n" +
            "FROM dc_all_topic_record t1\n" +
            "JOIN (\n" +
            "    SELECT chapter_name, MAX(create_time) AS max_time\n" +
            "    FROM dc_all_topic_record\n" +
            "    WHERE user_id = #{userId}  AND record_type = 'zj' AND is_delete = 0 and topic_id in (select id from dc_topic dt where dt.is_delete=0 and dt.topic_status=1 AND chapter_type = #{chapterType})\n" +
            "    GROUP BY chapter_name\n" +
            ") t2\n" +
            "ON t1.chapter_name = t2.chapter_name AND t1.create_time = t2.max_time\n" +
            "WHERE t1.user_id = #{userId} AND t1.record_type = 'zj' AND t1.is_delete = 0;\n")
    List<DcAllTopicRecord> getLatestRecordByChapter(Integer userId, String chapterType);


    List<DcTopic> getRandomTopics(@Param("chapterType") String chapterType,
                                  @Param("excludeChapterName") List<String> excludeChapterName);

    @Select("<script>\n" +
            "            SELECT * \n" +
            "            FROM dc_topic \n" +
            "            WHERE is_delete = 0 \n" +
            "              AND topic_status = 1\n" +
            "              AND chapter_type = #{chapterType}\n" +
            "              AND chapter_name = #{chapterName}\n" +
            "            ORDER BY RAND()\n" +
            "            </script>")
    List<DcTopic> getRandomTopicsByChapterName(@Param("chapterType") String chapterType,
                                  @Param("chapterName") String chapterName);

    @Select("<script>SELECT \n" +
            "    dt.chapter_name,\n" +
            "    dt.topic_name,\n" +
            "    dt.topic_option,\n" +
            "    dt.question_answer,\n" +
            "    dt.ai_question_analysis,\n" +
            "    dt.question_analysis,\n" +
            "    \n" +
            "    -- 提取 topic_ai_detail_analysis JSON 中的各个字段\n" +
            "    JSON_UNQUOTE(JSON_EXTRACT(dtda.topic_ai_detail_analysis, '$.QuestionBreakdown')) AS questionBreakdown,\n" +
            "    JSON_UNQUOTE(JSON_EXTRACT(dtda.topic_ai_detail_analysis, '$.Background')) AS background,\n" +
            "    JSON_UNQUOTE(JSON_EXTRACT(dtda.topic_ai_detail_analysis, '$.KeyConcepts')) AS keyConcepts,\n" +
            "    JSON_UNQUOTE(JSON_EXTRACT(dtda.topic_ai_detail_analysis, '$.Images')) AS images,\n" +
            "    JSON_UNQUOTE(JSON_EXTRACT(dtda.topic_ai_detail_analysis, '$.TrueAnswer')) AS trueAnswer,\n" +
            "    JSON_UNQUOTE(JSON_EXTRACT(dtda.topic_ai_detail_analysis, '$.FrequentErrors')) AS frequentErrors,\n" +
            "    JSON_UNQUOTE(JSON_EXTRACT(dtda.topic_ai_detail_analysis, '$.Conclusion')) AS conclusion,\n" +
            "    \n" +
            "    -- 特殊处理 OptionAnalysis 数组\n" +
            "    dtda.topic_ai_detail_analysis->'$.OptionAnalysis' AS option_analysis_array\n" +
            "    \n" +
            "FROM \n" +
            "    dc_topic dt \n" +
            "LEFT JOIN \n" +
            "    dc_topic_detail_analysis dtda ON dtda.topic_id = dt.id \n" +
            "WHERE \n" +
            "    chapter_name = #{chapterName}\n" +
            "ORDER BY \n" +
            "    chapter_name, topic_rank ASC;</script>")
    List<QuestionBreakdown> getQuestionBreakdown(@Param("chapterName") String chapterName);

    List<DcTopicVO> getDcYcTopicForAllRecords();

    List<DcTopicVO> getDcYcTopicFor7DayRecords();
}
