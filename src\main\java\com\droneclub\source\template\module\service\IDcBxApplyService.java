package com.droneclub.source.template.module.service;

import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcBxApply;
import com.droneclub.source.template.module.pojo.DcBxApplyListSearch;
import com.droneclub.source.template.module.pojo.DcBxApplyVO;

public interface IDcBxApplyService {

    ListData<DcBxApplyVO> getDcBxApplyList(DcBxApplyListSearch params);

    DcBxApplyVO getDcBxApplyById(Integer id);

    DcBxApply createDcBxApply(DcBxApply data);

    boolean updateDcBxApply(DcBxApply data);

    boolean deleteDcBxApplyById(Integer id);
}
