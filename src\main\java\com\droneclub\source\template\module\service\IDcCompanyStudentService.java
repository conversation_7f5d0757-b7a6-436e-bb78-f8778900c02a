package com.droneclub.source.template.module.service;

import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcCompanyStudent;
import com.droneclub.source.template.module.pojo.DcOrganizationStudentSearch;
import com.droneclub.source.template.module.pojo.DcOrganizationStudentVO;

public interface IDcCompanyStudentService {
    
    ListData<DcOrganizationStudentVO> getStudentList(DcOrganizationStudentSearch search);
    
    DcOrganizationStudentVO getStudentById(Integer id);
    
    DcCompanyStudent createStudent(DcCompanyStudent student);
    
    Boolean updateStudent(DcCompanyStudent student);
    
    Boolean deleteStudentById(Integer id);
} 