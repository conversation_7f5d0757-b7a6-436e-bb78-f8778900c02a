package com.droneclub.source.template.module.service;

import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcCompanyMember;
import com.droneclub.source.template.module.pojo.DcCompanyMemberSearch;
import com.droneclub.source.template.module.pojo.DcCompanyMemberVO;

public interface IDcCompanyMemberService {
    
    ListData<DcCompanyMemberVO> getMemberList(DcCompanyMemberSearch search);

    DcCompanyMember createMember(DcCompanyMember member);
    
    Boolean updateMember(DcCompanyMember member);
    
    Boolean deleteMemberById(Integer id);
    
    Boolean checkMemberExists(Integer userId, Integer companyId);
} 