<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.droneclub.source.template.mapper.AuthManageMapper">

    <insert id="batchInsert">
        INSERT IGNORE INTO tm_auth_api(api_code, api_name, api_path)
        VALUES
        <foreach collection="authApis" item="item" index="index" separator=",">
            (#{item.apiCode}, #{item.apiName}, #{item.apiPath})
        </foreach>
    </insert>

    <insert id="initAuthData">
        INSERT
        IGNORE INTO tm_auth_button (button_code, button_name) VALUES ('admin_button','管理按钮');
        INSERT
        IGNORE INTO tm_rel_button_api
        SELECT null, 'admin_button', api_code
        from tm_auth_api;
        INSERT
        IGNORE INTO tm_role (role_code, role_name) VALUES ('admin_role','管理员角色');
        INSERT
        IGNORE INTO tm_role (role_code, role_name) VALUES ('common_role','通用角色');
        INSERT
        IGNORE INTO tm_rel_role_button
        SELECT null, 'admin_role', button_code
        from tm_auth_button;
    </insert>

    <select id="getUserAuthData" resultType="com.droneclub.source.template.system.dto.UserAuthDTO">

        SELECT DISTINCT tab.button_name, tab.button_code
        FROM tm_rel_user_role tur
        INNER JOIN tm_rel_role_button trrb ON trrb.role_code = tur.role_code
        INNER JOIN tm_auth_button tab ON tab.button_code = trrb.button_code
        WHERE tur.user_id = #{userId}
    </select>
</mapper>