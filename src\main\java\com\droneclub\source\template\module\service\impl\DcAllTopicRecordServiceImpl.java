package com.droneclub.source.template.module.service.impl;

import cn.soulspark.source.common.enums.ResultCode;
import cn.soulspark.source.common.execption.ZkException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.common.utils.TemplateSessionUtils;
import com.droneclub.source.template.entity.DcAllTopicRecord;
import com.droneclub.source.template.entity.DcChapterDoTopicRecord;
import com.droneclub.source.template.mapper.DcAllTopicRecordMapper;
import com.droneclub.source.template.module.pojo.DcAllTopicRecordListSearch;
import com.droneclub.source.template.module.pojo.DcAllTopicRecordVO;
import com.droneclub.source.template.module.service.IDcAllTopicRecordService;
import com.droneclub.source.template.module.service.IDcChapterDoTopicRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DcAllTopicRecordServiceImpl implements IDcAllTopicRecordService {

    private final DcAllTopicRecordMapper dcAllTopicRecordMapper;
    private final IDcChapterDoTopicRecordService chapterDoTopicRecordService;

    @Override
    public ListData<DcAllTopicRecordVO> getDcAllTopicRecordList(DcAllTopicRecordListSearch params) {
        QueryWrapper<DcAllTopicRecord> queryWrapper = new QueryWrapper<>();
        // 查询总数
        Long total = dcAllTopicRecordMapper.selectCount(queryWrapper);

        // 分页查询
        Page<DcAllTopicRecord> page = new Page<>(params.getPageNo(), params.getPageSize());
        IPage<DcAllTopicRecord> dcAllTopicRecordPage = dcAllTopicRecordMapper.selectPage(page, queryWrapper);
        List<DcAllTopicRecord> list = dcAllTopicRecordPage.getRecords();
        List<DcAllTopicRecordVO> listVO = list.stream()
                .map(dcAllTopicRecord -> JSONObject.parseObject(JSONObject.toJSONString(dcAllTopicRecord), DcAllTopicRecordVO.class))
                .collect(Collectors.toList());
        return new ListData<>(listVO, total, params.getPageNo(), params.getPageSize());
    }

    @Override
    public DcAllTopicRecordVO getDcAllTopicRecordById(Integer id) {
        DcAllTopicRecord dcAllTopicRecord = dcAllTopicRecordMapper.selectById(id);
        if (dcAllTopicRecord == null) {
            throw new ZkException(ResultCode.FAIL.getCode(), "未查询到相关数据.");
        }
        return JSONObject.parseObject(JSONObject.toJSONString(dcAllTopicRecord), DcAllTopicRecordVO.class);
    }


    @Override
    public DcAllTopicRecord createDcAllTopicRecord(DcAllTopicRecord data) {
        // 判断题目是否正确
        data.setAnswerRight(data.getTopicAnswer().equals(data.getUserAnswer()));
        int currentUserId = TemplateSessionUtils.getCurrentUser().getId();
        data.setUserId(currentUserId);
        LambdaQueryWrapper<DcAllTopicRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcAllTopicRecord::getUserId, currentUserId);
        queryWrapper.eq(DcAllTopicRecord::getTopicId, data.getTopicId());
        // 查询是否存在记录，存在则更新，不存在则新增
        DcAllTopicRecord allTopicRecord = dcAllTopicRecordMapper.selectOne(queryWrapper);
        if (allTopicRecord != null) {
            data.setId(allTopicRecord.getId());
            updateDcAllTopicRecord(data);
        } else {
            boolean rs = dcAllTopicRecordMapper.insert(data) > 0;
            log.info("创建 DcAllTopicRecord: {}", rs ? "成功" : "失败");
        }
        // 更新章节做题的最新进度
        chapterDoTopicRecordService.createDcChapterDoTopicRecord(DcChapterDoTopicRecord.builder()
                .chapterType(data.getChapterType())
                .chapterName(data.getChapterName())
                .topicId(data.getTopicId())
                .userId(data.getUserId())
                .build());
        return data;
    }

    @Override
    public boolean updateDcAllTopicRecord(DcAllTopicRecord data) {
        boolean rs = dcAllTopicRecordMapper.updateById(data) > 0;
        log.info("更新 DcAllTopicRecord: {}", rs ? "成功" : "失败");
        return rs;
    }

    @Override
    public Boolean deleteErrorAnswerRecord(Integer topicId) {
        int currentUserId = TemplateSessionUtils.getCurrentUser().getId();
        DcAllTopicRecord allTopicRecord = new DcAllTopicRecord();
        allTopicRecord.setUserId(currentUserId);
        allTopicRecord.setTopicId(topicId);
        LambdaQueryWrapper<DcAllTopicRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcAllTopicRecord::getUserId, currentUserId);
        queryWrapper.eq(DcAllTopicRecord::getTopicId, topicId);
        allTopicRecord = dcAllTopicRecordMapper.selectOne(queryWrapper);
        if (allTopicRecord != null) {
            boolean rs = dcAllTopicRecordMapper.deleteById(allTopicRecord.getId()) > 0;
            log.info("删除 DcAllTopicRecord: {}", rs ? "成功" : "失败");
            return rs;
        }
        return true;
    }
}
