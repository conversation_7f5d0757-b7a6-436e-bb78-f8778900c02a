package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcBxApply;
import com.droneclub.source.template.module.pojo.DcBxApplyListSearch;
import com.droneclub.source.template.module.pojo.DcBxApplyVO;
import com.droneclub.source.template.module.service.IDcBxApplyService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dcBxApply")
public class DcBxApplyController {

    private final IDcBxApplyService dcBxApplyService;

    @GetMapping("/getDcBxApplyList")
    public RestResult<ListData<DcBxApplyVO>> getDcBxApplyList(DcBxApplyListSearch params) {
        return RestResult.success(dcBxApplyService.getDcBxApplyList(params));
    }

    @GetMapping("/getDcBxApplyById")
    public RestResult<DcBxApplyVO> getDcBxApplyById(Integer id) {
        return RestResult.success(dcBxApplyService.getDcBxApplyById(id));
    }

    @PostMapping("/createDcBxApply")
    public RestResult<DcBxApply> createDcBxApply(@RequestBody DcBxApply data) {
        return RestResult.success(dcBxApplyService.createDcBxApply(data));
    }

    @PutMapping("/updateDcBxApply")
    public RestResult<Boolean> updateDcBxApply(@RequestBody DcBxApply data) {
        return RestResult.success(dcBxApplyService.updateDcBxApply(data));
    }

    @DeleteMapping("/deleteDcBxApplyById")
    public RestResult<Boolean> deleteDcBxApplyById(Integer id) {
        return RestResult.success(dcBxApplyService.deleteDcBxApplyById(id));
    }
}
