package com.droneclub.source.template.mapper;

import com.droneclub.source.template.entity.TmAuthApi;
import com.droneclub.source.template.system.dto.UserAuthDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface AuthManageMapper {


    int batchInsert(@Param("authApis") List<TmAuthApi> authApis);

    int initAuthData();

    List<UserAuthDTO> getUserAuthData(@Param("userId") Integer userId);
}