package com.droneclub.source.template.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("dc_topic")
public class DcTopic {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String chapterType;
    private String chapterName;
    private String topicName;
    private Integer topicRank;
    private Integer topicStatus;
    private String topicOption;
    private String questionAnswer;
    private String questionAnalysis;
    private String aiQuestionAnalysis;
    @TableField(exist = false)
    private JSONObject aiQuestionDetailAnalysis;
    private Integer sfId;
    @TableField(fill = FieldFill.INSERT)
    private Integer createUser;
    private String createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer updateUser;
    private String updateTime;
    private String isDelete;

    public boolean hasChanges(DcTopic other) {
        if (other == null) return false;
        return !Objects.equals(this.topicName, other.topicName) ||
                !Objects.equals(this.topicOption, other.topicOption) ||
                !Objects.equals(this.questionAnswer, other.questionAnswer) ||
                !Objects.equals(this.questionAnalysis, other.questionAnalysis);
    }

}
