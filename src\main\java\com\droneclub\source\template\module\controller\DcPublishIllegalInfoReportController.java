package com.droneclub.source.template.module.controller;

import cn.soulspark.source.common.model.RestResult;
import com.droneclub.source.template.common.model.ListData;
import com.droneclub.source.template.entity.DcPublishIllegalInfoReport;
import com.droneclub.source.template.module.pojo.DcPublishIllegalInfoReportListSearch;
import com.droneclub.source.template.module.pojo.DcPublishIllegalInfoReportVO;
import com.droneclub.source.template.module.service.IDcPublishIllegalInfoReportService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/dcPublishIllegalInfoReport")
public class DcPublishIllegalInfoReportController {

    private final IDcPublishIllegalInfoReportService dcPublishIllegalInfoReportService;

    @GetMapping("/getDcPublishIllegalInfoReportList")
    public RestResult<ListData<DcPublishIllegalInfoReportVO>> getDcPublishIllegalInfoReportList(DcPublishIllegalInfoReportListSearch params) {
        return RestResult.success(dcPublishIllegalInfoReportService.getDcPublishIllegalInfoReportList(params));
    }

    @GetMapping("/getDcPublishIllegalInfoReportById")
    public RestResult<DcPublishIllegalInfoReportVO> getDcPublishIllegalInfoReportById(Integer id) {
        return RestResult.success(dcPublishIllegalInfoReportService.getDcPublishIllegalInfoReportById(id));
    }

    @PostMapping("/createDcPublishIllegalInfoReport")
    public RestResult<DcPublishIllegalInfoReport> createDcPublishIllegalInfoReport(@RequestBody DcPublishIllegalInfoReport data) {
        return RestResult.success(dcPublishIllegalInfoReportService.createDcPublishIllegalInfoReport(data));
    }

    @PutMapping("/updateDcPublishIllegalInfoReport")
    public RestResult<Boolean> updateDcPublishIllegalInfoReport(@RequestBody DcPublishIllegalInfoReport data) {
        return RestResult.success(dcPublishIllegalInfoReportService.updateDcPublishIllegalInfoReport(data));
    }

    @DeleteMapping("/deleteDcPublishIllegalInfoReportById")
    public RestResult<Boolean> deleteDcPublishIllegalInfoReportById(Integer id) {
        return RestResult.success(dcPublishIllegalInfoReportService.deleteDcPublishIllegalInfoReportById(id));
    }
}
