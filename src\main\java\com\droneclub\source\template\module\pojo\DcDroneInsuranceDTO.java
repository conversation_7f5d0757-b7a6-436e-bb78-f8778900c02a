package com.droneclub.source.template.module.pojo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 无人机投保数据传输对象
 */
@Data
public class DcDroneInsuranceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物理主键
     */
    private Integer id;

    /**
     * 无人机品牌
     */
    @NotBlank(message = "无人机品牌不能为空")
    @Size(max = 50, message = "无人机品牌长度不能超过50个字符")
    private String droneBrand;

    /**
     * 无人机型号
     */
    @NotBlank(message = "无人机型号不能为空")
    @Size(max = 50, message = "无人机型号长度不能超过50个字符")
    private String droneModel;

    /**
     * 机器S/N码
     */
    @NotBlank(message = "机器S/N码不能为空")
    @Size(max = 50, message = "机器S/N码长度不能超过50个字符")
    private String machineSn;

    /**
     * 电池S/N码
     */
    @Size(max = 50, message = "电池S/N码长度不能超过50个字符")
    private String batterySn;

    /**
     * 投保类型
     */
    @NotBlank(message = "投保类型不能为空")
    private String insuranceType;

    /**
     * 被保人姓名
     */
    @NotBlank(message = "被保人姓名/企业名称不能为空")
    @Size(max = 100, message = "被保人姓名/企业名称长度不能超过100个字符")
    private String insuredName;

    /**
     * 身份证件号码
     */
    @NotBlank(message = "身份证件号码/统一社会信用代码不能为空")
    @Size(min = 15, max = 18, message = "身份证件号码/统一社会信用代码长度应在15-18位之间")
    private String idNumber;

    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空")
    private String phone;

    /**
     * 联系地址
     */
    @NotBlank(message = "联系地址不能为空")
    @Size(max = 200, message = "联系地址长度不能超过200个字符")
    private String address;

    /**
     * 机型类别
     */
    @NotBlank(message = "机型类别不能为空")
    @Size(max = 50, message = "机型类别长度不能超过50个字符")
    private String droneCategory;

    /**
     * 用途
     */
    @Size(max = 200, message = "用途长度不能超过200个字符")
    private String purpose;

    /**
     * 文件信息
     */
    @TableField(exist = false)
    private JSONObject files;

    /**
     * 出单方案序号
     */
    @NotBlank(message = "出单方案序号不能为空")
    @Size(max = 50, message = "出单方案序号长度不能超过50个字符")
    private String policySchemeNo;

    /**
     * 保险状态 1: 待审核 2: 已生效 3: 已拒绝 4: 已过期
     */
    private String insuranceStatus;

    /**
     * 是否删除 0: 未删除 1: 已删除
     */
    private Integer isDelete;

} 