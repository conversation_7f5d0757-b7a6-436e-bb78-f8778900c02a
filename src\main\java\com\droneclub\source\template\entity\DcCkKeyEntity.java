package com.droneclub.source.template.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;

/**
 * dc_ck_key实体类
 */
@Data
@TableName("dc_ck_key")
public class DcCkKeyEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 物理主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Integer id;
    /**
     * 渠道
     */
    private String channel;
    /**
     * 模块类型
     */
    private String cdkCode;
    /**
     * 状态（0=未激活,1=已激活,2=已冻结）
     */
    private Integer status;
    /**
     * 激活码激活设备信息
     */
    private String activationUserAgent;
    /**
     * 激活时间
     */
    private String activationTime;
    /**
     *
     */
    private String createTime;
    /**
     *
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateTime;
    /**
     *
     */
    private Integer isDelete;
}