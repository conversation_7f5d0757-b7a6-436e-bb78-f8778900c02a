package com.droneclub.source.template.system.controller;

import cn.soulspark.source.common.model.RestResult;
import cn.soulspark.source.common.utils.StringUtils;
import com.droneclub.source.template.common.utils.FileUtils;
import com.droneclub.source.template.system.pojo.FileInfo;
import com.droneclub.source.template.system.service.IFileService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


@RestController
@RequestMapping("/file")
public class FileController {
    final IFileService fileService;

    public FileController(IFileService projectService) {
        this.fileService = projectService;
    }

    /**
     * 文件上传接口
     *
     * @param moduleType 模块了些
     * @param fileType   文件类型
     * @param businessId 关联id
     * @return 上传后的文件信息
     */
    @PostMapping("/upload")
    public RestResult<FileInfo> upload(String moduleType, String fileType, String businessId, MultipartFile file) {
        if (StringUtils.isInvalid(moduleType) || StringUtils.isInvalid(fileType) || StringUtils.isInvalid(businessId)) {
            return RestResult.failure(RestResult.FAIL.getCode(), "缺少参数");
        }
        if (file.isEmpty()) {
            return RestResult.failure(RestResult.FAIL.getCode(), "文件为空");
        }
        FileInfo fileInfo = FileUtils.initFileInfo(moduleType, fileType, businessId, file);
        return RestResult.success(fileService.upload(fileInfo, file));
    }

    /**
     * 根据id删除图片
     *
     * @param fileUrl 文件url
     * @return 删除结果
     */
    @PostMapping("/delete")
    public RestResult<Boolean> deleteFile(String fileUrl) {
        if (StringUtils.isInvalid(fileUrl)) {
            return RestResult.failure(RestResult.FAIL.getCode(), "缺少参数");
        }
        return RestResult.success(fileService.deleteFile(fileUrl));
    }

}
